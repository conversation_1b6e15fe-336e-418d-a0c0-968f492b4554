import React, { useState, useCallback } from "react";
import * as z from "zod";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { CalendarDateRangePicker } from "@/components/ui/date-range-picker";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import debounce from "lodash/debounce";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ServerSideDataTable } from "@/components/ui/server-side-data-table";
import { Button } from "@/components/ui/button";
import { Filter, X } from "lucide-react";
import transTableColumns, { TransactionProps } from "./trans-table";
import loadable from "@loadable/component";

import { useFetchTransactionTypes } from "@/hooks/optionsHooks/use-fetch-transtypes";
import { useFetchChargeModels } from "@/hooks/optionsHooks/use-fetch-chargemodels";
import { useFetchBrands } from "@/hooks/optionsHooks/use-fetch-brands";
import { useFetchBrandGroups } from "@/hooks/optionsHooks/use-fetch-brandgroups";
import { useForm } from "react-hook-form";
import { useFetchChannels } from "@/hooks/optionsHooks/use-fetch-channels";
import { zodResolver } from "@hookform/resolvers/zod";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { useFetchTransactionStatuses } from "@/hooks/optionsHooks/use-fetch-transaction-statuses";
import { FilterState } from "../index";
import { ReviewTransactionFilter } from "@/hooks/transactionHooks/use-fetch-review-transactions";
import { DataTable } from "@/components/ui/data-table";

export const filterStateSchema = z.object({
    legacyusername: z.string().optional(),
    transtype: z.string().optional(),
    brand: z.string().optional(),
    brandgroup: z.string().optional(),
    channel: z.string().optional(),
    status: z.string().optional(),
    isavailable: z.boolean().optional(),
    issettled: z.boolean().optional(),
    transday: z.string().optional().nullable(),
    campaignid: z.string().optional(),
    charge: z.string().optional(),
    legacyid: z.string().optional(),
    brandtransid: z.string().optional(),
    nickname: z.string().optional(),
});

// type FilterState = {
//     legacyusername?: string;
//     transtype?: string;
//     brand?: string;
//     brandgroup?: string;
//     channel?: string;
//     status?: string;
//     isavailable?: boolean;
//     issettled?: boolean;
//     transday?: string | null;
//     campaignid?: string;
//     charge?: string;
//     legacyid?: string;
//     brandtransid?: string;
// };

interface TransTableProps {
    data: any;
    filterable?: boolean;
    isDemo?: boolean;
    bulk?: boolean;
    title?: string;
    loading?: boolean;
    pagination?: any;
    pageCount?: number;
    keyword?: string;
    columns?: any;
    onPaginationChange?: (newPagination: any) => void;
    onKeywordChange?: (newKeyword: string) => void;
    onFilterChange?: (filters: FilterState) => void;
    sorting?: any;
    onSortingChange?: (newSorting: any) => void;
}

export default function ServerSideTransactionTable({
    data,
    filterable = true,
    title,
    isDemo = false,
    loading,
    pagination,
    pageCount,
    sorting,
    bulk,
    keyword,
    columns,

    onFilterChange,
}: TransTableProps) {
    const { data: brands, isLoading: isLoadingBrands } = useFetchBrands();
    const { data: transTypes, isLoading: isLoadingTransTypes } = useFetchTransactionTypes();
    const { data: brandGroups, isLoading: isLoadingBrandGroups } = useFetchBrandGroups();
    const { data: channels, isLoading: isLoadingChannels } = useFetchChannels();
    const { data: transactionStatuses, isLoading: isLoadingTransactionStatuses } =
        useFetchTransactionStatuses();

    // const handlePendingFilterChange = (key: keyof FilterState, value: any) => {
    //     setFilters((prev) => ({ ...prev, [key]: value }));
    // };

    // console.log("OOOPS =>", filters);

    const filterForm = useForm<FilterState>({
        resolver: zodResolver(filterStateSchema),
        defaultValues: {
            transday: undefined,
            legacyusername: "",
            transtype: "",
            brand: "",
            brandgroup: "",
            channel: "",
            status: "",
            isavailable: undefined,
            issettled: undefined,
            campaignid: "",
            charge: "",
            legacyid: "",
            nickname: "",
            brandtransid: "",
        },
    });

    const onSubmit = (data: FilterState) => {
        // const formattedFilters = formatFiltersForApi(data);
        onFilterChange(data);
    };

    const resetFilters = () => {
        filterForm.reset();
        onFilterChange({});
    };

    // console.log("data from table", data);
    return (
        <div className="mx-auto py-8">
            {filterable && (
                <Card className="my-4">
                    <CardHeader className="flex flex-row items-center justify-between p-6">
                        <h3 className="text-lg font-medium">Filters</h3>
                    </CardHeader>
                    <CardContent>
                        <Form {...filterForm}>
                            <form className="grid gap-6">
                                {/* Search and Basic Filters */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                                    <FormField
                                        control={filterForm.control}
                                        name="status"
                                        render={({ field }) => (
                                            <FormItem className="w-full">
                                                <FormLabel>Status</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select brand" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {isLoadingTransactionStatuses ? (
                                                            <SelectItem value="loading">
                                                                Loading...
                                                            </SelectItem>
                                                        ) : (
                                                            transactionStatuses?.data?.map(
                                                                (status, index) => (
                                                                    <SelectItem
                                                                        key={index}
                                                                        value={status}
                                                                    >
                                                                        {status}
                                                                    </SelectItem>
                                                                )
                                                            )
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={filterForm.control}
                                        name="brand"
                                        render={({ field }) => (
                                            <FormItem className="w-full">
                                                <FormLabel>Brand</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select brand" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {isLoadingBrands ? (
                                                            <SelectItem value="loading">
                                                                Loading...
                                                            </SelectItem>
                                                        ) : (
                                                            brands?.data?.map((brand, index) => (
                                                                <SelectItem
                                                                    key={index}
                                                                    value={brand}
                                                                >
                                                                    {brand}
                                                                </SelectItem>
                                                            ))
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={filterForm.control}
                                        name="brandgroup"
                                        render={({ field }) => (
                                            <FormItem className="w-full">
                                                <FormLabel>Brand Group</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select brand group" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {isLoadingBrandGroups ? (
                                                            <SelectItem value="loading">
                                                                Loading...
                                                            </SelectItem>
                                                        ) : (
                                                            brandGroups?.data?.map(
                                                                (brandgroup, index) => (
                                                                    <SelectItem
                                                                        key={index}
                                                                        value={brandgroup}
                                                                    >
                                                                        {brandgroup}
                                                                    </SelectItem>
                                                                )
                                                            )
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                {/* Brand and Channel Filters */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                                    <FormField
                                        control={filterForm.control}
                                        name="channel"
                                        render={({ field }) => (
                                            <FormItem className="w-full">
                                                <FormLabel>Channels</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select Channels " />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {isLoadingChannels ? (
                                                            <SelectItem value="loading">
                                                                Loading...
                                                            </SelectItem>
                                                        ) : (
                                                            channels?.data?.map(
                                                                (channel, index) => (
                                                                    <SelectItem
                                                                        key={index}
                                                                        value={channel}
                                                                    >
                                                                        {channel}
                                                                    </SelectItem>
                                                                )
                                                            )
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={filterForm.control}
                                        name="transtype"
                                        render={({ field }) => (
                                            <FormItem className="w-full">
                                                <FormLabel>Transaction Types</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Transaction Types " />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {isLoadingTransTypes ? (
                                                            <SelectItem value="loading">
                                                                Loading...
                                                            </SelectItem>
                                                        ) : (
                                                            transTypes?.data?.map(
                                                                (type: string, index: number) => (
                                                                    <SelectItem
                                                                        key={index}
                                                                        value={type}
                                                                    >
                                                                        {type}
                                                                    </SelectItem>
                                                                )
                                                            )
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={filterForm.control}
                                        name="issettled"
                                        render={({ field }) => (
                                            <FormItem className="w-full">
                                                <FormLabel>Is Settled?</FormLabel>
                                                <Select
                                                    onValueChange={(value) => {
                                                        field.onChange(value === "true");
                                                    }}
                                                    value={field.value?.toString()}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Settlement" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        <SelectItem value="true">
                                                            Settled
                                                        </SelectItem>
                                                        <SelectItem value="false">
                                                            Unsettled
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                {/* Transaction Type and Campaign Filters */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
                                    <FormField
                                        control={filterForm.control}
                                        name="campaignid"
                                        render={({ field }) => (
                                            <Input
                                                type="number"
                                                placeholder="Campaign ID"
                                                {...field}
                                            />
                                        )}
                                    />

                                    <FormField
                                        control={filterForm.control}
                                        name="charge"
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                placeholder="Charge Amount"
                                                {...field}
                                            />
                                        )}
                                    />

                                    <FormField
                                        control={filterForm.control}
                                        name="legacyusername"
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                placeholder="Legacy Username"
                                                {...field}
                                            />
                                        )}
                                    />

                                    {/* <Input
                                        type="text"
                                        placeholder="Legacy Username"
                                        value={filters.legacyusername}
                                        onChange={(e) =>
                                            handlePendingFilterChange(
                                                "legacyusername",
                                                e.target.value
                                            )
                                        }
                                    /> */}
                                    <FormField
                                        control={filterForm.control}
                                        name="brandtransid"
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                placeholder="Brand Trans Id"
                                                {...field}
                                            />
                                        )}
                                    />
                                    <FormField
                                        control={filterForm.control}
                                        name="nickname"
                                        render={({ field }) => (
                                            <Input type="text" placeholder="Nickname" {...field} />
                                        )}
                                    />
                                </div>

                                {/* Date Filters */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                                    <FormField
                                        control={filterForm.control}
                                        name="transday"
                                        render={({ field }) => (
                                            <FormItem className="w-full">
                                                <FormLabel>Transaction Day</FormLabel>
                                                <DatePicker
                                                    date={
                                                        field.value
                                                            ? new Date(field.value)
                                                            : undefined
                                                    }
                                                    setDate={(date) =>
                                                        field.onChange(
                                                            date?.toISOString().split("T")[0] ||
                                                                undefined
                                                        )
                                                    }
                                                    clearable={true}
                                                />
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                {/* Boolean and Legacy Filters */}
                                {/* <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <FormField
                                            control={filterForm.control}
                                            name="isavailable"
                                            render={({ field }) => (
                                                <FormItem className="space-y-3">
                                                    <FormLabel>Is Available</FormLabel>
                                                    <FormControl>
                                                        <RadioGroup
                                                            onValueChange={field.onChange}
                                                            value={field.value}
                                                            className="flex space-x-4"
                                                        >
                                                            <div className="flex items-center space-x-2">
                                                                <RadioGroupItem
                                                                    value="true"
                                                                    id="available"
                                                                />
                                                                <Label htmlFor="available">
                                                                    Yes
                                                                </Label>
                                                            </div>
                                                            <div className="flex items-center space-x-2">
                                                                <RadioGroupItem
                                                                    value="false"
                                                                    id="unavailable"
                                                                />
                                                                <Label htmlFor="unavailable">
                                                                    No
                                                                </Label>
                                                            </div>
                                                        </RadioGroup>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>
                                </div> */}
                                <div className="flex space-x-2">
                                    <Button
                                        type="submit"
                                        variant="default"
                                        size="sm"
                                        className="h-8"
                                        onClick={filterForm.handleSubmit(onSubmit)}
                                    >
                                        <Filter className="mr-2 h-4 w-4" />
                                        Apply Filters
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={resetFilters}
                                        className="h-8"
                                    >
                                        <X className="mr-2 h-4 w-4" />
                                        Reset Filters
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            )}

            <DataTable
                columns={columns}
                data={data}
                title={title ?? ""}
                filterKey="name"
                loading={loading}
                isFilterable={false}
            />
        </div>
    );
}
