/**
 * A custom hook for formatting currency amounts.
 *
 * @param {any} amount - The amount to be formatted. Should be a number or a string that can be converted to a number.
 * @returns {string} The formatted currency string.
 *
 * @example
 * const formattedAmount = useCurrencyFormatter(1000);
 * console.log(formattedAmount); // Outputs: "GHS 1,000.00"
 */
export const useCurrencyFormatter = (amount: any) => {
    const formattedTotalBalance = new Intl.NumberFormat("en-GB", {
        style: "currency",
        currency: "GHS",
    }).format(amount);

    return formattedTotalBalance;
};
