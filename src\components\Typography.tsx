import React from "react";

interface Props {
    text?: string;
    variant:
        | "h1"
        | "h2"
        | "h3"
        | "h4"
        | "p"
        | "blockquote"
        | "table"
        | "list"
        | "Inline code"
        | "lead"
        | "large"
        | "small"
        | "muted";
    items?: string[]; // Optional prop for the list
    className?: string; // Add className for custom styles
}

export function Typography({ variant, text, items, className = "" }: Props) {
    switch (variant) {
        case "h1":
            return (
                <h1
                    className={`scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl ${className}`}
                >
                    {text}
                </h1>
            );
        case "h2":
            return (
                <h2
                    className={`scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0 ${className}`}
                >
                    {text}
                </h2>
            );
        case "h3":
            return (
                <h3 className={`scroll-m-20 text-2xl font-semibold tracking-tight ${className}`}>
                    {text}
                </h3>
            );
        case "h4":
            return (
                <h4 className={`scroll-m-20 text-xl font-semibold tracking-tight ${className}`}>
                    {text}
                </h4>
            );
        case "p":
            return <p className={`leading-7 tracking-wider ${className}`}>{text}</p>;
        case "blockquote":
            return (
                <blockquote className={`mt-6 border-l-2 pl-6 italic ${className}`}>
                    {text}
                </blockquote>
            );
        case "list":
            return (
                <ul className={`my-6 ml-6 list-disc [&>li]:mt-2 ${className}`}>
                    {items?.map((item, index) => <li key={index}>{item}</li>)}
                </ul>
            );
        case "Inline code":
            return (
                <code
                    className={`relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold ${className}`}
                >
                    {text}
                </code>
            );
        case "lead":
            return <p className={`text-xl text-muted-foreground ${className}`}>{text}</p>;
        case "large":
            return <div className={`text-lg font-semibold ${className}`}>{text}</div>;
        case "small":
            return (
                <small className={`text-sm font-medium leading-none ${className}`}>{text}</small>
            );
        case "muted":
            return <p className={`text-sm text-muted-foreground ${className}`}>{text}</p>;
        default:
            return <p className={className}>{text}</p>;
    }
}
