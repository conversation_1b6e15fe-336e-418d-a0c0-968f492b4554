import { useMutation, useQueryClient } from "@tanstack/react-query";
import { approveCampaign } from "@/api/campaigns/appsApi";

export const useApproveCampaign = () => {
    const queryClient = useQueryClient();

    const {
        mutate: approveCampaignMutation,
        isLoading,
        isError,
        error,
        isSuccess,
    } = useMutation({
        mutationFn: (campaignId: number) => approveCampaign(campaignId),
        onSuccess: () => {
            // Invalidate and refetch campaigns list
            queryClient.invalidateQueries({ queryKey: ["campaigns"] });
        },
    });

    return {
        approveCampaignMutation,
        isLoading,
        isError,
        error,
        isSuccess,
    };
};
