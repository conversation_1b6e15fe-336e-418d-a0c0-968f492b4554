import ExcelJS from "exceljs";

// Define interfaces for the data structure
interface ChargeProfileDetails {
    brand: string;
    brandgroup: string;
    chargemodel: string;
    chargetype: string;
    id: number;
    lowerbound: number;
    merchantcharge: number;
    payercharge: number;
    transtype: string;
    upperbound: number;
}

interface ChargeProfile {
    chargeprofiledetails: ChargeProfileDetails[];
    createdby: number;
    createddate: string;
    description: string;
    id: number;
    isactive: boolean;
    name: string;
    updatedby: number;
    updateddate: string;
}

interface CampaignData {
    campaignid: number;
    chargeprofile: ChargeProfile;
    chargeprofileid: number;
    chargeprofiletype: string;
    createdby: number;
    createddate: string;
    enddate: string;
    id: number;
    startdate: string;
    updatedby: number;
    updateddate: string;
}

class ChargeExcelGenerator {
    // Method to generate Excel file
    static async generateExcel(data: CampaignData[], filePath: string): Promise<void> {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet("Campaign Data");

        // Define headers
        worksheet.columns = [
            { header: "Campaign ID", key: "campaignid", width: 15 },
            { header: "Charge Profile ID", key: "chargeprofileid", width: 20 },
            { header: "Charge Profile Type", key: "chargeprofiletype", width: 20 },
            { header: "Created By", key: "createdby", width: 15 },
            { header: "Created Date", key: "createddate", width: 20 },
            { header: "End Date", key: "enddate", width: 20 },
            { header: "ID", key: "id", width: 10 },
            { header: "Start Date", key: "startdate", width: 20 },
            { header: "Updated By", key: "updatedby", width: 15 },
            { header: "Updated Date", key: "updateddate", width: 20 },
            { header: "Charge Profile Created By", key: "chargeprofile_createdby", width: 25 },
            { header: "Charge Profile Created Date", key: "chargeprofile_createddate", width: 25 },
            { header: "Charge Profile Description", key: "chargeprofile_description", width: 25 },
            { header: "Charge Profile ID", key: "chargeprofile_id", width: 20 },
            { header: "Charge Profile Is Active", key: "chargeprofile_isactive", width: 20 },
            { header: "Charge Profile Name", key: "chargeprofile_name", width: 20 },
            { header: "Charge Profile Updated By", key: "chargeprofile_updatedby", width: 25 },
            { header: "Charge Profile Updated Date", key: "chargeprofile_updateddate", width: 25 },
            { header: "Brand", key: "brand", width: 20 },
            { header: "Brand Group", key: "brandgroup", width: 20 },
            { header: "Charge Model", key: "chargemodel", width: 20 },
            { header: "Charge Type", key: "chargetype", width: 20 },
            { header: "Charge Detail ID", key: "chargeprofiledetails_id", width: 20 },
            { header: "Lower Bound", key: "lowerbound", width: 15 },
            { header: "Merchant Charge", key: "merchantcharge", width: 20 },
            { header: "Payer Charge", key: "payercharge", width: 20 },
            { header: "Trans Type", key: "transtype", width: 20 },
            { header: "Upper Bound", key: "upperbound", width: 15 },
        ];

        // Add data to the worksheet
        data.forEach((campaign) => {
            campaign.chargeprofile.chargeprofiledetails.forEach((detail) => {
                worksheet.addRow({
                    campaignid: campaign.campaignid,
                    chargeprofileid: campaign.chargeprofileid,
                    chargeprofiletype: campaign.chargeprofiletype,
                    createdby: campaign.createdby,
                    createddate: campaign.createddate,
                    enddate: campaign.enddate,
                    id: campaign.id,
                    startdate: campaign.startdate,
                    updatedby: campaign.updatedby,
                    updateddate: campaign.updateddate,
                    chargeprofile_createdby: campaign.chargeprofile.createdby,
                    chargeprofile_createddate: campaign.chargeprofile.createddate,
                    chargeprofile_description: campaign.chargeprofile.description,
                    chargeprofile_id: campaign.chargeprofile.id,
                    chargeprofile_isactive: campaign.chargeprofile.isactive,
                    chargeprofile_name: campaign.chargeprofile.name,
                    chargeprofile_updatedby: campaign.chargeprofile.updatedby,
                    chargeprofile_updateddate: campaign.chargeprofile.updateddate,
                    brand: detail.brand,
                    brandgroup: detail.brandgroup,
                    chargemodel: detail.chargemodel,
                    chargetype: detail.chargetype,
                    chargeprofiledetails_id: detail.id,
                    lowerbound: detail.lowerbound,
                    merchantcharge: detail.merchantcharge,
                    payercharge: detail.payercharge,
                    transtype: detail.transtype,
                    upperbound: detail.upperbound,
                });
            });
        });

        // Save the workbook to a file
        await workbook.xlsx.writeFile(filePath);
        console.log(`Excel file saved to ${filePath}`);
    }
}

export default ChargeExcelGenerator;
