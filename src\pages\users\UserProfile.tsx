import TransactionsTable from "@/components/dashboard/transactions-table";
import React from "react";
import Banner from "./Banner";
import UsersTable from "./users-table";
import SubUsersTable from "./users-table/sub-users";
import { useFetchSubUsers } from "@/hooks/usersHooks/use-fetch-subusers";
import WalletTable from "./users-table/wallet";
import { useFetchWallets } from "@/hooks/momoHooks/use-fetch-wallets";
import BankAccountsTable from "./users-table/banks-table";
import { useFetchBankAccounts } from "@/hooks/bankHooks/use-fetch-bankaccounts";
import ServerSideUsersTable from "./users-table/server-side-table";

const UserProfileWindow = ({ user }: any) => {
    const { data, pagination, keyword, isLoading, onPaginationChange, onKeywordChange } =
        useFetchSubUsers(50, user?.userid);

    const {
        data: walletData,
        isLoading: walletLoading,
        keyword: walletKeyword,
        onKeywordChange: walletOnKeywordChange,
        onPaginationChange: walletOnPaginationChange,
    } = useFetchWallets(user?.accountid, 50);

    const {
        data: bankData,
        isLoading: bankLoading,
        keyword: bankKeyword,
        onKeywordChange: bankOnKeywordChange,
        onPaginationChange: bankOnPaginationChange,
    } = useFetchBankAccounts(user?.accountid, 50);

    return (
        <div className="w-full space-y-4">
            <div className="relative my-8">
                <Banner user={user} />
            </div>

            <ServerSideUsersTable
                // columns={columns}
                data={data}
                title="Sub Users"
                loading={isLoading}
                filterable={true}
                pagination={pagination}
                pageCount={data?.totalPages || 0}
                keyword={keyword}
                onPaginationChange={onPaginationChange}
                onKeywordChange={onKeywordChange}
            />

            <div className="flex w-full flex-wrap items-start justify-between gap-4">
                <div className="w-full">
                    <WalletTable
                        data={walletData?.content || []}
                        filterable={true}
                        title="Mobile Money Wallets"
                        loading={walletLoading}
                        pagination={pagination}
                        pageCount={data?.totalPages || 0}
                        keyword={walletKeyword}
                        onPaginationChange={walletOnPaginationChange}
                        onKeywordChange={walletOnKeywordChange}
                    />
                </div>
                <div className="w-full">
                    <BankAccountsTable
                        data={bankData?.content || []}
                        filterable={true}
                        title="Bank Accounts"
                        loading={bankLoading}
                        pagination={pagination}
                        accountId={user?.accountid}
                        pageCount={data?.totalPages || 0}
                        keyword={bankKeyword}
                        onPaginationChange={bankOnPaginationChange}
                        onKeywordChange={bankOnKeywordChange}
                    />
                </div>
            </div>
        </div>
    );
};

export default UserProfileWindow;
