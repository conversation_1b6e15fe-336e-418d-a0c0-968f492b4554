import React from "react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CirclePlay } from "lucide-react";
import SimulateChargeForm from "./simulate-charge-form";

// interface SimulateChargeDialogProps {
//     accountId: number;
// }

const SimulateChargeDialog: React.FC<{ accountId: number }> = ({ accountId }) => {
    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <CirclePlay className="h-4 w-4 text-yellow-500" />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">
                            Simulate Charge
                        </DialogTitle>
                        <DialogDescription>Simulate a charge for this campaign</DialogDescription>
                    </DialogHeader>
                </DialogHeader>
                <SimulateChargeForm accountId={accountId} />
            </DialogContent>
        </Dialog>
    );
};

export default SimulateChargeDialog;
