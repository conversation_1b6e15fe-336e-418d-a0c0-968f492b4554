import { useState, useMemo } from "react";
import { IDLE, defaultApiStatuses, ApiStatus } from "@/api/constants/apiStatus";


type Statuses = Record<`is${Capitalize<Lowercase<ApiStatus>>}`, boolean>;

/**
 * Capitalizes the first letter of a string.
 * @param s - The string to capitalize.
 */
const capitalize = (s: string) => s.charAt(0).toUpperCase() + s.slice(1);

/**
 * Prepares the status indicators based on the current API status.
 * @param currentStatus - The current status of the API call.
 * @returns An object with boolean values indicating the current status.
 */
const prepareStatuses = (currentStatus: ApiStatus): Statuses => {
    const statuses = {} as Statuses;

    for (const status of defaultApiStatuses) {
        const normalizedStatus = capitalize(status.toLowerCase());
        const normalizedStatusKey = `is${normalizedStatus}` as keyof Statuses;
        statuses[normalizedStatusKey] = status === currentStatus;
    }

    return statuses;
};

/**
 * Custom hook for managing the status of an API call.
 * @param currentStatus - Initial status of the API call.
 * @returns An object containing the status, a setter for the status, and boolean indicators for each possible status.
 */
export const useApiStatus = (currentStatus: ApiStatus = IDLE) => {
    const [status, setStatus] = useState<ApiStatus>(currentStatus);
    const statuses = useMemo(() => prepareStatuses(status), [status]);

    return {
        status,
        setStatus,
        ...statuses,
    };
};
