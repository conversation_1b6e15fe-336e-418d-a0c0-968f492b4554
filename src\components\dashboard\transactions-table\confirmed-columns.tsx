import React, { lazy } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";

import { Checkbox } from "@/components/ui/checkbox";

import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";
import { TransactionDetailsDialog } from "./transaction-details";
import loadable from "@loadable/component";
import { ConfirmTransaction } from "./confirm-transaction";
import { isNil } from "es-toolkit";

// This type is used to define the shape of our data.
// You can use a Zod schema here if you want.
// export type Transaction = {
//     id: number;
//     transId: string;
//     brandTransId: string;
//     username: string;
//     appName: string;
//     brandGroup: string;
//     amount: any;
// };

export interface ITransactionsProps {
    id: number;
    createdby: number;
    createddate: string;
    enddate: string;
    notes: string | null;
    startdate: string;
    channel: string;
    brandgroups: string | null;
    ip: string | null;
    hostName: string | null;
    userAgent: string | null;
}

export const confirmedColumns: ColumnDef<ITransactionsProps>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },

    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
        cell: ({ row }) => <div className="w-[50px]">{row.getValue("id")}</div>,
        enableSorting: true,
        enableHiding: false,
    },

    {
        accessorKey: "notes",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Notes" />,

        cell: ({ row }) => {
            const notes = row.getValue("notes");
            return <p className="font-medium">{isNil(notes) ? " No Notes " : notes}</p>;
        },
    },
    {
        accessorKey: "channel",
        header: "Channel",
        cell: ({ row }) => {
            const channel = row.getValue("channel");
            return (
                <div className="capitalize">
                    {channel === "MOBILE_MONEY" ? "Mobile Money" : "Card"}
                </div>
            );
        },
    },

    {
        accessorKey: "brandgroups",
        header: "Brand",
        // cell: ({ row }) => <div>{row.getValue("brandgroups")}</div>,
        cell: ({ row }) => {
            const brands = row.getValue("brandgroups");
            return <p className="font-medium">{isNil(brands) ? " No Brand " : brands}</p>;
        },
    },
    {
        accessorKey: "ip",
        header: "IP",
        cell: ({ row }) => {
            const IP = row.getValue("ip");
            return <p className="font-medium">{isNil(IP) ? " No IP " : IP}</p>;
        },
        // cell: ({ row }) => <div>{row.getValue("ip")}</div>,
    },

    // {
    //     accessorKey: "status",
    //     header: "Status",
    //     cell: ({ row }) => {
    //         const active = row.getValue("status");
    //         return (
    //             <p className={` ${active ? "text-green-500" : "text-red-500"}`}>
    //                 {active ? "PAID" : "UNPAID"}
    //             </p>
    //         );
    //     },
    // },
    {
        accessorKey: "startdate",
        header: "Start Date",
        cell: ({ row }) => <div>{row.getValue("startdate")}</div>,
    },
    {
        accessorKey: "enddate",
        header: "End Date",
        cell: ({ row }) => <div>{row.getValue("enddate")}</div>,
    },

    {
        accessorKey: "createddate",
        header: "Date",
        cell: ({ row }) => {
            const date = new Date(row.getValue("createddate"));
            return <div>{format(date, "PPP")}</div>;
        },
    },
    // {
    //     id: "actions",
    //     enableHiding: false,
    //     header: "Action",
    //     cell: ({ row }) => {
    //         const user = row.original;
    //         return <ConfirmTransaction transaction={user} />;
    //     },
    // },
];
