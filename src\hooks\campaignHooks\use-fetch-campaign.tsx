import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { UsersApiResponse } from "@/lib/interfaces";
import { approveCampaign, fetchAllCampaigns, viewCampaign } from "@/api/campaigns/appsApi";

export const useApproveCampaign = (campaignId: number) => {
    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        queryKey: ["campaign", campaignId],
        queryFn: () => viewCampaign(campaignId),
    });

    return {
        data: data?.data,

        isLoading,
        isError,
        error,

        refetch,
    };
};
