import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";

import { Checkbox } from "@/components/ui/checkbox";

import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";
import { TransactionActions } from "./transaction-details";

// This type is used to define the shape of our data.
// You can use a Zod schema here if you want.
export type Transaction = {
    id: number;
    transId: string;
    brandTransId: string;
    username: string;
    appName: string;
    brandGroup: string;
    amount: any;
};

export interface ITransactionsProps {
    accountid: number;
    actualamount: number;
    amount: number;
    brandgroup: string;
    brandtransid: string;
    campaignid: number;
    channel: string;
    charge: number;
    clientreference: string;
    clienttransid: string;
    description: string;
    id: number;
    isavailable: Boolean;
    status: string;
    transactiondate: string;
    transtype: string;
}

export const columns: ColumnDef<ITransactionsProps>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
        cell: ({ row }) => <div className="w-[50px]">{row.getValue("id")}</div>,
        enableSorting: false,
        enableHiding: false,
    },

    {
        accessorKey: "amount",
        header: "Amount ",
        // cell: ({ row }) => <div className="">{row.getValue("brandtransid")}</div>,

        cell: ({ row }) => {
            const amount: number = row.getValue("amount");
            return formatCurrency(amount);
        },
    },
    {
        accessorKey: "channel",
        header: "Channel",
        cell: ({ row }) => <div className="lowercase">{row.getValue("channel")}</div>,
    },
    {
        accessorKey: "accountid",
        header: "Account ID",
        cell: ({ row }) => <div className="lowercase">{row.getValue("accountid")}</div>,
    },
    {
        accessorKey: "brandgroup",
        header: "Brand",
        cell: ({ row }) => <div>{row.getValue("brandgroup")}</div>,
    },
    {
        accessorKey: "transtype",
        header: "Trans Type",
        // cell: ({ row }) => {
        //     const company = row.getValue("iscompany");
        //     return <p className="font-medium">{company ? "Company" : "Client"}</p>;
        // },
        cell: ({ row }) => <div>{row.getValue("transtype")}</div>,
    },
    {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => {
            const active = row.getValue("status");
            return (
                <p className={` ${active ? "text-green-500" : "text-red-500"}`}>
                    {active ? "PAID" : "UNPAID"}
                </p>
            );
        },
    },
    {
        accessorKey: "transactiondate",
        header: "Date",
        cell: ({ row }) => {
            const date = new Date(row.getValue("transactiondate"));
            return <div>{format(date, "PPP")}</div>;
        },
    },
    {
        id: "actions",
        enableHiding: false,
        header: "Action",
        cell: ({ row }) => {
            const user = row.original;
            const id = row.getValue("id");
            return <TransactionActions user={user} data={id} />;
        },
    },
];
