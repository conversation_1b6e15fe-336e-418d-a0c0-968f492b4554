import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { fetchApps } from "@/api/campaigns/appsApi";
import { AppResponseObject, UsersApiResponse } from "@/lib/interfaces";
// export const useFetchApps = (accountId: any, pageSize: number = 50) => {
//     const [page, setPage] = React.useState(0);

//     const [search, setSearch] = React.useState<string>("");
//     const [size, setSize] = React.useState(pageSize);

//     const jump = (page: number) => {
//         setPage(page);
//     };

//     const updateFilters = (searchTerm?: string) => {
//         if (searchTerm !== undefined) setSearch(searchTerm);
//         setPage(0); // Reset to first page when filters change
//     };

//     const itemPerPage = (sz: number) => {
//         setSize(sz);
//         setPage(0); // Reset to first page when page size changes
//     };

//     return useQuery({
//         queryKey: ["apps", accountId, page, size, search],
//         queryFn: () =>
//             fetchApps(accountId, {
//                 page,
//                 size,
//                 search,
//             }),
//         select: (response: any) => ({
//             setPage,
//             jump,
//             updateFilters,
//             itemPerPage,
//             data: response?.data?.content,
//             currentPage: page,
//             totalElements: response?.data?.totalElements,
//             totalPages: response?.data?.totalPages,
//         }),
//     });
// };

export const useFetchApps = (initialPageSize = 50, accountId: number) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });

    const [keyword, setKeyword] = useState<string>("");
    const [sorting, setSorting] = useState([{ id: "createddate", desc: true }]);
    const { data, isLoading, isError, error, refetch } = useQuery<AppResponseObject, Error>({
        queryKey: ["apps", accountId, pagination.pageIndex, keyword, pagination.pageSize, sorting],
        queryFn: () =>
            fetchApps(accountId, pagination.pageIndex, pagination.pageSize, keyword, sorting),
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const onKeywordChange = (newKeyword: string) => {
        setKeyword(newKeyword);
    };
    const onSortingChange = (newSorting: any) => {
        setSorting(newSorting);
    };

    return {
        data: data?.data,
        pagination,
        keyword,
        isLoading,
        isError,
        error,
        sorting,
        onPaginationChange,
        onSortingChange,
        onKeywordChange,
        refetch,
    };
};
