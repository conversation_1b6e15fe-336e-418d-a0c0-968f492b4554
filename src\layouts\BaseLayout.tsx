import React from "react";
import DragWindowRegion from "@/components/DragWindowRegion";
import ToggleTheme from "@/components/ToggleTheme";
import useDemoToggle from "@/hooks/use-demo";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Toggle } from "@/components/ui/toggle";
import { ToggleOn } from "iconsax-react";
import { ToggleLeftIcon, ToggleRightIcon } from "lucide-react";

export default function BaseLayout({ children }: { children: React.ReactNode }) {
    const { isDemo, toggleDemo } = useDemoToggle();
    // console.log(isDemo);

    return (
        <div className="h-full">
            {/* <DragWindowRegion /> */}
            <main className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-r from-redGradientStart to-redGradientEnd dark:bg-background">
                <div className="flex items-center justify-between gap-4">
                    <div className="my-2 flex items-center gap-x-2">
                        <p className="text-gray-300">Switch Theme</p>
                        <ToggleTheme />
                    </div>
                    <div className="flex items-center gap-x-2">
                        <p className="text-gray-300">Demo mode is {isDemo ? "ON" : "OFF"}</p>
                        <Toggle
                            size="sm"
                            variant="outline"
                            onClick={toggleDemo}
                            className="bg-primary"
                        >
                            {isDemo ? (
                                <ToggleRightIcon className="h-4 w-4" />
                            ) : (
                                <ToggleLeftIcon className="h-4 w-4" />
                            )}
                        </Toggle>
                    </div>
                </div>

                {children}
            </main>
            <footer className="absolute flex w-full justify-between px-4">
                <p className="text-gray-600">Redde Dashboard</p>
            </footer>
        </div>
    );
}
