import { loginUser, registerUser } from "@/api/user/authApi";
import { LoginRequest, RegisterRequest } from "@/lib/interfaces";
import { useMutation } from "@tanstack/react-query";

/**
 * A hook that wraps the loginUser function in a mutation for use with react-query.
 *
 * This hook uses the useDemoToggle hook to determine if the demo mode is active.
 * It then returns a mutation function that calls the loginUser function with the provided data.
 *
 * @returns {Object} An object containing the mutation function.
 */
export const useLoginUser = () => {
    // const { isDemo } = useDemoToggle();
    return useMutation({
        mutationFn: (data: LoginRequest) => loginUser(data),
    });
};

/**
 * A hook that wraps the registerUser function in a mutation for use with react-query.
 *
 * This hook returns a mutation function that calls the registerUser function with the provided data.
 * It can be used to handle user registration in React components.
 *
 * @returns {UseMutationResult} A mutation result object from react-query, containing the mutation function and related states.
 */
export const useRegisterUser = () => {
    return useMutation({
        mutationFn: (data: RegisterRequest) => registerUser(data),
    });
};
