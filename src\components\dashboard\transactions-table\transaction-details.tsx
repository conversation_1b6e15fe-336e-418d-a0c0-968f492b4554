import React, { useRef, useState } from "react";
import ReactDOMServer from "react-dom/server";
import { format } from "date-fns";
import { formatCurrency, formatDateToLongString } from "@/lib/utils";
import {
    ArrowDown,
    ArrowUp,
    ReceiptCent,
    EyeIcon,
    LucideIcon,
    PencilIcon,
    PrinterIcon,
    BadgeCent,
    RotateCcw,
} from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useReverseTransaction } from "@/hooks/accountsHooks/use-fetch-transactions";
import { toast } from "@/components/ui/use-toast";
import { Textarea } from "@/components/ui/textarea";

interface TransactionDetailsProps {
    user: any;
    data?: any;
    isReview?: boolean;
}

interface ActionButton {
    iconColor?: string;
    icon: LucideIcon;
    label: string;
    onClick: () => void;
    dialogContent?: React.ReactNode;
}

const ReverseTransactionDialog: React.FC<{
    isOpen: boolean;
    onClose: () => void;
    onConfirm: (reason: string, transactionId: any) => void;
    transactionId: any;
}> = ({ isOpen, onClose, onConfirm, transactionId }) => {
    const [reason, setReason] = useState("");
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!reason.trim()) return;

        setIsLoading(true);
        try {
            await onConfirm(reason, transactionId);
            onClose();
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-lg !rounded-sm">
                <DialogHeader>
                    <DialogTitle className="mb-2 text-xl font-bold">
                        Reverse Transaction
                    </DialogTitle>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="reason">Reason for Reversal</Label>
                        <Textarea
                            id="reason"
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                            placeholder="Enter reason for reversing the transaction"
                            required
                            className="w-full"
                        />
                    </div>
                    <DialogFooter className="flex justify-end space-x-2">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                            disabled={isLoading}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={!reason.trim() || isLoading}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            {isLoading ? "Reversing..." : "Reverse Transaction"}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
};

// Separate component for the transaction details content
const TransactionContent: React.FC<TransactionDetailsProps> = ({ user, isReview = false }) => (
    <div className="print:p-8">
        <div className="mt-6 flex flex-col items-center">
            <div className="relative print:hidden">
                <div className="rounded-full bg-[#efebeb] p-3 dark:bg-[#2a2a2a]">
                    {user?.transtype === "Receive" ? (
                        <ArrowDown className="text-primary dark:text-[#5bb5fb]" />
                    ) : (
                        <ArrowUp className="text-primary dark:text-[#5bb5fb]" />
                    )}
                </div>
                <ReceiptCent className="absolute -bottom-1 -right-1 h-4 w-4 text-[#f7931a]" />
            </div>
            <h2 className="my-3 text-2xl font-bold">{user?.transtype}</h2>
            <p className="my-2 mt-2 text-4xl font-bold text-primary dark:text-[#5bb5fb]">
                {formatCurrency(user?.amount)}
            </p>
            <p className="mt-1 text-sm">actual amount: {formatCurrency(user?.actualamount)}</p>
            <p className="mt-1 text-center text-sm">charge: {formatCurrency(user?.charge)}</p>
            <div className="my-2 flex w-full items-center justify-center py-2 text-xs">
                <p className="ml-1">Client transaction id:</p>
                <p className="ml-1 font-semibold text-primary dark:text-[#5bb5fb]">
                    {user.clienttransid}
                </p>
            </div>
        </div>
        <div className="mt-8">
            <div className="flex items-center justify-between border-y border-gray-200 py-3 dark:border-gray-700">
                <p className="dark:text-gray-400">Transaction Date</p>
                <p className="font-semibold text-primary dark:text-[#5bb5fb]">
                    {isReview
                        ? formatDateToLongString(user.transdate)
                        : format(user.transactiondate, "PPP")}
                </p>
            </div>
            <div className="flex items-center justify-between border-b border-gray-200 py-3 dark:border-gray-700">
                <p className="dark:text-gray-400">Status</p>
                <div className="print:hidden">
                    {user?.status === "PAID" ? (
                        <Badge
                            variant="outline"
                            className="!rounded-sm bg-green-800 text-white hover:bg-green-800 dark:text-green-400"
                        >
                            Paid
                        </Badge>
                    ) : (
                        <Badge
                            variant="outline"
                            className="!rounded-sm bg-red-800 text-white hover:bg-red-800 dark:text-red-400"
                        >
                            Unpaid
                        </Badge>
                    )}
                </div>
                <div className="hidden font-semibold print:block">{user?.status}</div>
            </div>
            <div className="flex items-center justify-between border-b border-gray-200 py-3 dark:border-gray-700">
                <p className="dark:text-gray-400">Client Reference</p>
                <p className="font-semibold text-primary dark:text-[#5bb5fb]">
                    {user?.clientreference}
                </p>
            </div>
            <div className="flex items-center justify-between border-b border-gray-200 py-3 dark:border-gray-700">
                <p className="dark:text-gray-400">Transaction ID</p>
                <p className="font-semibold text-primary dark:text-[#5bb5fb]">{user?.id}</p>
            </div>
            <div className="flex items-center justify-between border-b border-gray-200 py-3 dark:border-gray-700">
                <p className="dark:text-gray-400">Channel</p>
                <p className="font-semibold text-primary dark:text-[#5bb5fb]">{user?.channel}</p>
            </div>
            <div className="flex items-center justify-between border-b border-gray-200 py-3 dark:border-gray-700">
                <p className="dark:text-gray-400">Brand Group</p>
                <p className="font-semibold text-primary dark:text-[#5bb5fb]">{user?.brandgroup}</p>
            </div>
            <div className="flex items-center justify-between border-b border-gray-200 py-3 dark:border-gray-700">
                <p className="dark:text-gray-400">Brand Transaction ID</p>
                <p className="font-semibold text-primary dark:text-[#5bb5fb]">
                    {user?.brandtransid}
                </p>
            </div>
        </div>
    </div>
);

const TransactionDetailsContent: React.FC<TransactionDetailsProps> = ({
    user,
    isReview = false,
}) => (
    <>
        <DialogHeader className="border-b border-gray-200 dark:border-gray-700">
            <DialogTitle className="mb-2 text-xl font-bold">Transaction Details</DialogTitle>
        </DialogHeader>
        <TransactionContent user={user} isReview={isReview} />
    </>
);

const ActionButtonWithDialog: React.FC<ActionButton> = ({
    icon: Icon,
    label,
    onClick,
    dialogContent,
    iconColor,
}) => {
    if (dialogContent) {
        return (
            <Dialog>
                <DialogTrigger asChild>
                    <Button variant="icon" title={label}>
                        <Icon className="h-4 w-4 text-primary" />
                    </Button>
                </DialogTrigger>
                <DialogContent className="max-w-lg !rounded-sm">{dialogContent}</DialogContent>
            </Dialog>
        );
    }

    return (
        <Button variant="icon" onClick={onClick} title={label}>
            <Icon className={`h-4 w-4 ${iconColor}`} />
        </Button>
    );
};

export const TransactionActions: React.FC<TransactionDetailsProps> = ({
    user,
    data,
    isReview = false,
}) => {
    const printRef = useRef<HTMLDivElement>(null);
    const [isReverseDialogOpen, setIsReverseDialogOpen] = useState(false);
    const reverseTransaction = useReverseTransaction();

    // console.log(`{user: ${user}, data: ${data}, isReview: ${isReview}}`);
    const handlePrint = () => {
        // Create a new window for printing
        const printWindow = window.open("", "", "width=600,height=600");
        if (!printWindow) return;

        // Add necessary styles
        const styles = `
            <style>
                body { 
                    font-family: Arial, sans-serif;
                    padding: 2rem;
                    margin: 0;
                }
                .print-container {
                    max-width: 800px;
                    margin: 0 auto;
                }
                .flex { display: flex; }
                .justify-between { justify-content: space-between; }
                .items-center { align-items: center; }
                .border-b, .border-y { border-bottom: 1px solid #e5e7eb; }
                .border-y { border-top: 1px solid #e5e7eb; }
                .py-3 { padding: 0.75rem 0; }
                .mt-8 { margin-top: 2rem; }
                .font-bold { font-weight: 700; }
                .text-2xl { font-size: 1.5rem; }
                .text-4xl { font-size: 2.25rem; }
                .text-center { text-align: center; }
                .mt-1 { margin-top: 0.25rem; }
                .text-sm { font-size: 0.875rem; }
                .text-xs { font-size: 0.75rem; }
                .text-primary { color: #2563eb; }
            </style>
        `;

        // Create the content with the transaction details
        const content = `
            <div class="print-container">
                <h1 style="text-align: center; margin-bottom: 2rem;">Transaction Details</h1>
                <div class="mt-6 text-center">
                    <h2 class="text-2xl font-bold">${user.transtype}</h2>
                    <p class="text-4xl font-bold text-primary">${formatCurrency(user.amount)}</p>
                    <p class="mt-1 text-sm">actual amount: ${formatCurrency(user.actualamount)}</p>
                    <p class="mt-1 text-sm">charge: ${formatCurrency(user.charge)}</p>
                    <div class="my-2 text-xs">
                        <p>Client transaction id: <span class="text-primary">${user.clienttransid}</span></p>
                    </div>
                </div>
                <div class="mt-8">
                    <div class="flex justify-between items-center border-y py-3">
                        <p>Transaction Date</p>
                        <p class="text-primary">${isReview ? formatDateToLongString(user?.transdate) : format(user?.transactiondate, "PPP")}</p>
                    </div>
                    <div class="flex justify-between items-center border-b py-3">
                        <p>Status</p>
                        <p>${user.status}</p>
                    </div>
                    <div class="flex justify-between items-center border-b py-3">
                        <p>Client Reference</p>
                        <p class="text-primary">${user.clientreference}</p>
                    </div>
                    <div class="flex justify-between items-center border-b py-3">
                        <p>Transaction ID</p>
                        <p class="text-primary">${user.id}</p>
                    </div>
                    <div class="flex justify-between items-center border-b py-3">
                        <p>Channel</p>
                        <p class="text-primary">${user.channel}</p>
                    </div>
                    <div class="flex justify-between items-center border-b py-3">
                        <p>Brand Group</p>
                        <p class="text-primary">${user.brandgroup}</p>
                    </div>
                    <div class="flex justify-between items-center border-b py-3">
                        <p>Brand Transaction ID</p>
                        <p class="text-primary">${user.brandtransid}</p>
                    </div>
                </div>
            </div>
        `;

        // Write the complete HTML document to the new window
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
                <head>
                    <title>Transaction Details</title>
                    ${styles}
                </head>
                <body>
                    ${content}
                </body>
            </html>
        `);

        // Close the document and trigger print
        printWindow.document.close();
        printWindow.focus();

        // Wait for styles to load before printing
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    };

    const handleReverse = async (reason: string, id: any) => {
        try {
            const response = await reverseTransaction.mutateAsync({ reason, transactionid: id });
            if (response.status === "success") {
                toast({
                    title: "Success",
                    description: response?.message || "Transaction reversed successfully",
                });
            }
            setIsReverseDialogOpen(false);
            // console.log("🚀 ~ handleReverse ~ response:", response);

            // console.log("Reversing transaction:", user.id, "Reason:", reason, "transactionId:", id);
        } catch (error) {
            toast({
                title: "Failed",
                description: (error as Error)?.message || "Transaction reversal failed",
            });
            setIsReverseDialogOpen(false);
            console.error("Error reversing transaction:", error);
            throw error;
        }
    };

    const actionButtons: ActionButton[] = [
        {
            icon: EyeIcon,
            label: "View Details",
            iconColor: "",
            onClick: () => {},
            dialogContent: <TransactionDetailsContent user={user} isReview={isReview} />,
        },
        {
            icon: RotateCcw,
            label: "Reverse Transaction",
            iconColor: "text-green-500",
            onClick: () => setIsReverseDialogOpen(true),
        },
        {
            icon: PrinterIcon,
            label: "Print Transaction",
            iconColor: "text-yellow-500",
            onClick: handlePrint,
        },
    ];

    return (
        <>
            <div className="flex gap-2">
                {actionButtons.map((button, index) => (
                    <ActionButtonWithDialog key={index} {...button} iconColor={button.iconColor} />
                ))}
            </div>
            <ReverseTransactionDialog
                isOpen={isReverseDialogOpen}
                onClose={() => setIsReverseDialogOpen(false)}
                onConfirm={handleReverse}
                transactionId={data}
            />
        </>
    );
};

export default TransactionActions;
