import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Wallet2, Wallet2Icon, Wallet } from "lucide-react";
import { useLocation } from "react-router-dom";

import DashboardCard from "@/components/dashboard/DashboardCards";
// import Sidebar from "./sidebar";
// import Banner from "./Banner";
// import Transaction from "./Transaction";
// import Cashout from "./Cashout";
// import Apps from "./Apps";
// import UserProfileWindow from "./UserProfile";
import { useFetchUser } from "@/hooks/usersHooks/use-fetch-user";
import { useFetchUserBalance } from "@/hooks/accountsHooks/use-balance";
import { formatCurrency } from "@/lib/utils";
// import SkeletonComponent from "../account-summary/unconfirmed-balances/skeleton";
import { useFetchTransactions } from "@/hooks/accountsHooks/use-fetch-transactions";
import TransactionsTable from "@/components/dashboard/transactions-table";
import { useFetchApps } from "@/hooks/campaignHooks/use-fetch-apps";
import { totalUsers } from "@/lib/utils";
import loadable from "@loadable/component";
import { CampaignData } from "@/lib/interfaces";
import Sidebar from "@/pages/users/sidebar";
import Banner from "@/pages/users/Banner";
import CampaignBanner from "@/components/campaigns/CampaignBanner";
import Cashout from "@/pages/users/Cashout";
import CampaignCharges from "@/components/campaigns/campaign-charges";
import CampaignCommissions from "@/components/campaigns/campaign-commissions";
// import ServerSideTransactionsTable from "@/components/dashboard/transactions-table";

// const ServerSideTransactionsTableLazy = loadable(
//     () => import("@/components/dashboard/transactions-table"),
//     {
//         fallback: <SkeletonComponent />,
//     }
// );

const CampaignProfile = () => {
    const location = useLocation();
    const { campaign } = location.state as { campaign: CampaignData };

    const [selectedSection, setSelectedSection] = useState("Dashboard");
    const links = ["Charges", "Commissions", "Apps", "Profile"];

    console.log("🚀 ~ Campaign Profile ~ data:", campaign);

    // const userBalances = useFetchUserBalance(campaign.id);
    // const userBalance = userBalances?.data?.data[0];

    // console.log("🚀 ~ UserProfile ~ userBalance:", userBalance)
    if (!campaign) {
        return <div>Campaign not found</div>;
    }

    return (
        <div className="flex h-screen w-full">
            <div className="fixed h-full w-[10%] bg-white shadow-2xl dark:bg-card">
                <Sidebar links={links} selected={selectedSection} onSelect={setSelectedSection} />
            </div>
            <div className="ml-[10%] w-[90%] overflow-y-auto px-8 py-4">
                {selectedSection === "Dashboard" && (
                    <>
                        <div className="relative my-12">
                            <CampaignBanner campaign={campaign} />
                        </div>
                        {/* <div className="relative flex w-full justify-around">
                            <div className="grid w-full grid-cols-4 gap-4">
                                {dashInfo.map((dash) => (
                                    <DashboardCard
                                        key={dash.id}
                                        icon={dash.icon}
                                        iconColor={dash.iconColor}
                                        iconBgColor={dash.iconBgColor}
                                        title={dash.title}
                                        value={dash.value}
                                        selectOptions={dash?.selectOptions}
                                        extraStyles
                                    />
                                ))}
                            </div>
                        </div> */}
                        {/* <ServerSideTransactionsTableLazy
                            data={data}
                            title="Transactions"
                            loading={isLoading}
                            filterable={true}
                            pagination={pagination}
                            pageCount={data?.totalPages || 0}
                            keyword={keyword}
                            onPaginationChange={onPaginationChange}
                            onKeywordChange={onKeywordChange}
                        /> */}
                    </>
                )}
                {/* {selectedSection === "Transaction" && (
                    <ServerSideTransactionsTableLazy
                        data={data}
                        title="Transactions"
                        loading={isLoading}
                        filterable={true}
                        pagination={pagination}
                        pageCount={data?.totalPages || 0}
                        keyword={keyword}
                        onPaginationChange={onPaginationChange}
                        onKeywordChange={onKeywordChange}
                    />
                )} */}
                {selectedSection === "Charges" && (
                    <CampaignCharges campaignId={campaign.id} active={campaign.isactive} />
                )}
                {selectedSection === "Commissions" && (
                    <CampaignCommissions campaignId={campaign.id} />
                )}
                {/* {selectedSection === "Profile" && <UserProfileWindow user={user} />} */}
            </div>
        </div>
    );
};

export default CampaignProfile;
