// AddUser.js
import React, { useState } from "react";

import { useFetchConfirmedTransactions } from "@/hooks/actionHooks/use-fetch-confirmed-transactions";
import loadable from "@loadable/component";

import { useFetchTransactionTypes } from "@/hooks/optionsHooks/use-fetch-transtypes";
const ServerSideTransactionsTableLazy = loadable(
    () => import("@/components/dashboard/transactions-table"),
    {
        fallback: <p>Loading...</p>,
    }
);

//: TODO: Complete the confirm transaction network request
//: TODO: Add a modal to view transaction details

const ConfirmTransaction: React.FC = () => {
    // const [transType, setTransType] = useState("");
    // const [startDate, setStartDate] = useState<Date | undefined>();
    // const [endDate, setEndDate] = useState<Date | undefined>();

    const {
        data,
        pagination,
        keyword,
        sorting,
        isLoading,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
    } = useFetchConfirmedTransactions(50);

    // const transactionTypesData = useFetchTransactionTypes();

    return (
        <div className="">
            <ServerSideTransactionsTableLazy
                data={data}
                title="Confirmed Transactions"
                loading={isLoading}
                filterable={true}
                confirmTransaction={true}
                isConfirmed={true}
                pagination={pagination}
                pageCount={data?.totalPages || 0}
                keyword={keyword}
                sorting={sorting}
                onPaginationChange={onPaginationChange}
                onKeywordChange={onKeywordChange}
                onSortingChange={onSortingChange}
            />
        </div>
    );
};

export default ConfirmTransaction;
