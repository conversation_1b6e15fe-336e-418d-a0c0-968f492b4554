import { useState } from "react";
import { fetchAllSubUsers, fetchAllUsers } from "@/api/user/authApi";
import { useQuery } from "@tanstack/react-query";
import { UsersApiResponse } from "@/lib/interfaces";

/**
 * A custom hook for fetching and managing subusers data.
 *
 * @param {number} initialPageSize - The initial number of items per page. Default is 10.
 * @param {number} userid - The ID of the user whose subusers are being fetched.
 * @returns {Object} An object containing:
 *   - data: The fetched subusers data.
 *   - pagination: Current pagination state.
 *   - userid: The ID of the user whose subusers are being fetched.
 *   - keyword: The current search keyword.
 *   - isLoading: <PERSON><PERSON>an indicating if data is being loaded.
 *   - isError: <PERSON>olean indicating if an error occurred.
 *   - error: Any error that occurred during the fetch.
 *   - onPaginationChange: Function to update pagination.
 *   - onKeywordChange: Function to update the search keyword.
 *   - refetch: Function to refetch the data.
 */

export const useFetchSubUsers = (initialPageSize: number = 10, userid: number) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });
    const [keyword, setKeyword] = useState("");

    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        queryKey: ["subusers", pagination.pageIndex, userid, pagination.pageSize],
        queryFn: () => fetchAllSubUsers(pagination.pageIndex, pagination.pageSize, userid, keyword),
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const onKeywordChange = (newKeyword: string) => {
        setKeyword(newKeyword);
    };

    return {
        data: data?.data,
        pagination,
        userid,
        keyword,
        isLoading,

        isError,
        error,
        onPaginationChange,
        onKeywordChange,
        refetch,
    };
};
