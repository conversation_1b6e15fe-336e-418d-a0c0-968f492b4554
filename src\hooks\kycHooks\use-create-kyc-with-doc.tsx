import { useMutation } from "@tanstack/react-query";
import { createKY<PERSON>WithDoc } from "@/api/kyc/kycApis";

/**
 * A hook that wraps the createKYCWithDoc function in a mutation for use with react-query.
 *
 * This hook creates a mutation that allows uploading KYC (Know Your Customer) documents
 * for a specific account. It handles the document creation process including image upload.
 *
 * @param {number} accountid - The ID of the account to associate the KYC document with
 * @returns {UseMutationResult} A mutation result object from react-query, containing the mutation
 * function and related states (isLoading, isError, etc.)
 */
export const useCreateKYCWithDoc = (accountid: number) => {
    return useMutation({
        mutationFn: (data: any) => createKYCWithDoc(accountid, data),
    });
};
