import React, { useEffect } from "react";
import { HashRouter as Router, Route, Routes } from "react-router-dom";
import { createRoot } from "react-dom/client";
import BaseLayout from "@/layouts/BaseLayout";

import { syncThemeWithLocal } from "@/helpers/theme_helpers";
import "@/localization/i18n";
import Dashboard from "@/pages/dashboard/Dashboard";
import Login from "@/pages/Login";
import { Toaster } from "@/components/ui/toaster";
import DefaultLayout from "./layouts/DefaultLayout";

import Settings from "./pages/settings";

import AccountSummary from "./pages/account-summary";
import { AuthProvider } from "./contexts/AuthContexts";

import Transactions from "./pages/transactions";
import ReddeActions from "./pages/redde-actions";
import Profile from "./pages/profile";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import UsersPage from "./pages/users";
import UserProfile from "./pages/users/Profile";
import CampaignProfile from "./pages/redde-actions/actions/CampaignProfile";
import { DemoProvider } from "./contexts/DemoContext";
import ErrorBoundary from "./pages/ErrorBoundary";
import { ProtectedRoute } from "./components/ProtectedRoute";

interface AdminRoute {
    id: number;
    path: string;
    component: React.ReactNode;
    requiredPermission: string;
}

const adminRoutes: AdminRoute[] = [
    {
        id: 0,
        path: "/dashboard",
        component: <Dashboard />,
        requiredPermission: "RED17",
    },
    {
        id: 1,
        path: "/account-summary",
        component: <AccountSummary />,
        requiredPermission: "RED00",
    },
    {
        id: 2,
        path: "/transactions",
        component: <Transactions />,
        requiredPermission: "RED16",
    },

    {
        id: 4,
        path: "/settings",
        component: <Settings />,
        requiredPermission: "RED17",
    },

    {
        id: 7,
        path: "/users",
        component: <UsersPage />,
        requiredPermission: "RED17",
    },
    {
        id: 8,
        path: "/settings",
        component: <Settings />,
        requiredPermission: "RED17",
    },
    {
        id: 9,
        path: "/redde-actions",
        component: <ReddeActions />,
        requiredPermission: "RED17",
    },
    {
        id: 10,
        path: "/profile",
        component: <Profile />,
        requiredPermission: "RED17",
    },
    {
        id: 11,
        path: "/user-profile",
        component: <UserProfile />,
        requiredPermission: "RED17",
    },
    {
        id: 12,
        path: "/campaign-profile",
        component: <CampaignProfile />,
        requiredPermission: "RED17",
    },
];

export default function App() {
    useEffect(() => {
        syncThemeWithLocal();
    }, []);

    const queryClient = new QueryClient();

    return (
        <ErrorBoundary>
            <QueryClientProvider client={queryClient}>
                <ReactQueryDevtools initialIsOpen={false} />
                <DemoProvider>
                    <Router>
                        <AuthProvider>
                            <Toaster />

                            <Routes>
                                <Route
                                    path="/"
                                    element={
                                        <BaseLayout>
                                            <Login />
                                        </BaseLayout>
                                    }
                                />

                                {adminRoutes.map((route) => (
                                    <Route
                                        key={route.id}
                                        path={route.path}
                                        element={
                                            <ProtectedRoute authItemId={route.requiredPermission}>
                                                <DefaultLayout>{route.component}</DefaultLayout>
                                            </ProtectedRoute>
                                        }
                                    />
                                ))}

                                <Route
                                    path="/unauthorized"
                                    element={
                                        <DefaultLayout>
                                            <div className="flex items-center justify-center h-screen">
                                                {/* <p>  You don't have permission to access this page.</p> */}
                                                <p className="text-2xl font-bold">
                                                    You don't have permission to access this page.
                                                </p>
                                            </div>
                                        </DefaultLayout>
                                    }
                                />
                            </Routes>
                        </AuthProvider>
                    </Router>
                </DemoProvider>
            </QueryClientProvider>
        </ErrorBoundary>
    );
}

const root = createRoot(document.getElementById("app")!);
root.render(<App />);
