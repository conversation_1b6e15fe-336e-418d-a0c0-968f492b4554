import { createBankAccount, editBankAccount } from "@/api/bank/bankaccountsApi";
import { IAccountIdParams } from "@/lib/interfaces";
import { useMutation } from "@tanstack/react-query";
/**
 * A hook that wraps the createBankAccount function in a mutation for use with react-query.
 *
 * This hook returns a mutation function that calls the createBankAccount function with the provided data.
 * It can be used to handle bank account creation in React components.
 *
 * @returns {UseMutationResult} A mutation result object from react-query, containing the mutation function and related states.
 */
export const useEditBankAccount = (accountId: number, bankAccountId: number) => {
    // console.log("useAccountId", accountId);
    return useMutation({
        mutationFn: (data: any) => editBankAccount(accountId, bankAccountId, data),
    });
};
