import React, { useCallback, useState } from "react";

import { ServerSideDataTable } from "@/components/ui/server-side-data-table";
import { columns } from "./columns";
import { confirmedColumns } from "./confirmed-columns";
import { debounce } from "lodash";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

import { ConfirmTransaction } from "./confirm-transaction";

export interface TransactionsProps {
    id: number;
    userid: number;
    name: string;
    email: string;
    accountid: number;
    isexpired: boolean;
    isactive: boolean;
    mobilenumber: string;
    iscompany: boolean;
    createdtime: string;
}

type TransactionTableProps = {
    // columns: ColumnDef<any, any>[];
    confirmTransaction?: boolean;
    isConfirmed?: boolean;
    data: any;
    filterable?: boolean;
    title?: string;
    loading?: boolean;
    pagination: any;
    pageCount: number;
    keyword: string;
    sorting: any;
    onPaginationChange: (newPagination: any) => void;
    onKeywordChange: (newKeyword: string) => void;
    onSortingChange: (newSorting: any) => void;
};

export default function ServerSideTransactionsTable({
    data,
    filterable = true,
    confirmTransaction = false,
    title,
    loading,
    pagination,
    isConfirmed = false,
    pageCount,
    keyword,
    sorting,
    onPaginationChange,
    onKeywordChange,
    onSortingChange,
}: TransactionTableProps) {
    const [searchTerm, setSearchTerm] = useState("");

    const debouncedSearch = useCallback(
        debounce((value: string) => {
            onKeywordChange(value);
        }, 500),
        [onKeywordChange]
    );

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        debouncedSearch(value);
    };

    return (
        <div className="mx-auto py-8">
            {filterable && (
                <Card>
                    <CardHeader>Filter</CardHeader>
                    <CardContent>
                        <div className="my-4 grid w-full grid-cols-2 gap-x-4">
                            <Input
                                type="text"
                                placeholder="Search..."
                                value={searchTerm}
                                onChange={handleSearchChange}
                            />

                            {confirmTransaction && <ConfirmTransaction />}
                        </div>
                    </CardContent>
                </Card>
            )}

            <div className="mt-4">
                <ServerSideDataTable
                    columns={isConfirmed ? confirmedColumns : columns}
                    data={data?.content || []}
                    title={title ?? ""}
                    loading={loading}
                    pagination={pagination}
                    filterable={filterable}
                    keyword={keyword}
                    onKeywordChange={onKeywordChange}
                    pageCount={pageCount}
                    sorting={sorting}
                    onPaginationChange={onPaginationChange}
                    onSortingChange={onSortingChange}
                />
            </div>
        </div>
    );
}
