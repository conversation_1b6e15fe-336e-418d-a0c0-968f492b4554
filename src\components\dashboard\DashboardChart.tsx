import React from "react";
import { DashboardTabs } from "./DashboardTabs";
// import { Bar<PERSON><PERSON> } from "./BarChart";
import { Card, CardContent } from "@/components/ui/card";
import { BarChartComponent } from "@/components/dashboard/BarChart";
import { PieChartComponent } from "@/components/dashboard/PieChart";

export const DashboardChart = () => {
    const tabContents = {
        "All Transactions": <BarChartComponent />,
        "New Users": <PieChartComponent />,
        "Total Users": <p>Total Users Content</p>,
    };
    return (
        <Card className="p-4">
            <CardContent>
                <DashboardTabs tabContents={tabContents} />
            </CardContent>
        </Card>
    );
};
