import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis, YAxis } from "recharts";

import {
    ChartConfig,
    ChartContainer,
    ChartLegend,
    ChartLegendContent,
    ChartTooltip,
    ChartTooltipContent,
} from "@/components/ui/chart";
import { Button } from "../ui/button";
import { Dot, File, Maximize2, MoreVertical, Upload } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import Export from "../custom-icons/export";
const chartData = [
    { month: "January", received: 186, cashout: 80 },
    { month: "February", received: 305, cashout: 200 },
    { month: "March", received: 237, cashout: 120 },
    { month: "April", received: 73, cashout: 190 },
    { month: "May", received: 209, cashout: 130 },
    { month: "June", received: 314, cashout: 240 },
    { month: "July", received: 114, cashout: 190 },
    { month: "August", received: 314, cashout: 240 },
    { month: "September", received: 414, cashout: 140 },
    { month: "October", received: 210, cashout: 110 },
    { month: "November", received: 390, cashout: 202 },
    { month: "December", received: 120, cashout: 180 },
];

const chartConfig = {
    all: {
        label: "all",
        color: "",
    },
    received: {
        label: "received",
        color: " #930006",
    },
    cashout: {
        label: "cashout",
        color: " rgba(255, 0, 0, 0.2)",
    },
} satisfies ChartConfig;

export function BarChartComponent() {
    const [activeChart, setActiveChart] = React.useState<keyof typeof chartConfig>("all");

    const total = React.useMemo(
        () => ({
            received: chartData.reduce((acc, curr) => acc + curr.received, 0),
            cashout: chartData.reduce((acc, curr) => acc + curr.cashout, 0),
        }),
        []
    );
    return (
        <>
            <div className="flex items-end justify-between">
                <div className="my-4 flex gap-x-2">
                    {["all", "received", "cashout"].map((key) => {
                        const chart = key as keyof typeof chartConfig;
                        return (
                            <Button
                                variant="outline"
                                size="sm"
                                key={chart}
                                data-active={activeChart === chart}
                                onClick={() => setActiveChart(chart)}
                            >
                                <Dot className={`text-[${chartConfig[chart].color}]`} />
                                <span className="text-xs text-muted-foreground">
                                    {chartConfig[chart].label.toLocaleUpperCase()}
                                </span>
                            </Button>
                        );
                    })}
                </div>
                <div className="ml-auto flex items-center gap-2">
                    <Button
                        size="sm"
                        variant="outline"
                        className="h-10 w-10 gap-1 rounded-full text-sm"
                    >
                        {/* <Export /> */}
                        <Upload className="h-3.5 w-3.5 text-gray-800 dark:text-white" />
                    </Button>
                    <Button
                        size="sm"
                        variant="outline"
                        className="h-10 w-10 gap-1 rounded-full text-sm"
                    >
                        <Maximize2 className="h-3.5 w-3.5 text-gray-800 dark:text-white" />
                    </Button>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-10 w-10 gap-1 rounded-full text-sm"
                            >
                                <MoreVertical className="h-3.5 w-3.5 text-gray-800 dark:text-white" />
                            </Button>
                        </DropdownMenuTrigger>

                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            {["all", "received", "cashout"].map((key) => {
                                const chart = key as keyof typeof chartConfig;
                                return (
                                    <DropdownMenuCheckboxItem
                                        key={chart}
                                        checked={activeChart === chart}
                                        onClick={() => setActiveChart(chart)}
                                    >
                                        {chartConfig[chart].label}
                                    </DropdownMenuCheckboxItem>
                                );
                            })}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            <ChartContainer config={chartConfig}>
                <BarChart
                    accessibilityLayer
                    data={chartData}
                    margin={{
                        left: 12,
                        right: 12,
                    }}
                >
                    <CartesianGrid vertical={false} />
                    <XAxis
                        dataKey="month"
                        tickLine={false}
                        tickMargin={10}
                        axisLine={false}
                        tickFormatter={(value) => value.slice(0, 3)}
                    />
                    <YAxis dataKey="received" tickLine={true} axisLine={true} />
                    <ChartTooltip
                        cursor={false}
                        content={<ChartTooltipContent indicator="dashed" />}
                    />
                    <ChartLegend content={<ChartLegendContent />} />

                    {activeChart === "all" ? (
                        <>
                            {" "}
                            <Bar dataKey="received" fill="var(--color-received)" radius={4} />
                            <Bar dataKey="cashout" fill="var(--color-cashout)" radius={4} />{" "}
                        </>
                    ) : (
                        <Bar
                            dataKey={activeChart}
                            fill={`var(--color-${activeChart})`}
                            radius={4}
                        />
                    )}
                </BarChart>
            </ChartContainer>
        </>
    );
}
