import React, { useState } from "react";
import { Tabs, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import clsx from "clsx";
import { MoreVertical } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

const tabs = [
    { name: "All Transactions", href: "#", current: true },
    { name: "New Users", href: "#", current: false },
    { name: "Total Users", href: "#", current: false },
];

interface DashboardTabsProps {
    tabContents: {
        [key: string]: React.ReactNode;
    };
}

export const DashboardTabs: React.FC<DashboardTabsProps> = React.memo(({ tabContents }) => {
    const [currentTab, setCurrentTab] = useState(tabs[0].name);

    return (
        <div>
            <Tabs defaultValue={currentTab}>
                <div className="flex items-center">
                    {/* <div className="flex items-center border-b-2 border-gray-300"> */}
                    <TabsList>
                        {tabs.map((tab) => (
                            <TabsTrigger
                                key={tab.name}
                                value={tab.name}
                                className={clsx(
                                    currentTab === tab.name
                                        ? "rounded-none border-red-500 text-red-600"
                                        : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700",
                                    "mx-3 whitespace-nowrap border-b-2 px-1 py-2.5 text-sm font-medium"
                                )}
                                aria-current={currentTab === tab.name ? "page" : undefined}
                                onClick={() => setCurrentTab(tab.name)}
                            >
                                {tab.name}
                            </TabsTrigger>
                        ))}
                    </TabsList>
                    <div className="ml-auto flex items-center gap-2">
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="icon" size="sm" className="h-7 gap-1 text-sm">
                                    <MoreVertical className="h-3.5 w-3.5" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuCheckboxItem checked>
                                    Fulfilled
                                </DropdownMenuCheckboxItem>
                                <DropdownMenuCheckboxItem>Declined</DropdownMenuCheckboxItem>
                                <DropdownMenuCheckboxItem>Refunded</DropdownMenuCheckboxItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </div>
                <div className="py-6">
                    {tabs.map((tab) => (
                        <TabsContent key={tab.name} value={tab.name}>
                            {tabContents[tab.name]}
                        </TabsContent>
                    ))}
                </div>
            </Tabs>
        </div>
    );
});
