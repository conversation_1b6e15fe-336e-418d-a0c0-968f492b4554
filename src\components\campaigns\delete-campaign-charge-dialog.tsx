// DeleteCampaignChargeDialog.tsx
import React from "react";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/components/ui/use-toast";
import { useDeleteChargeProfileAssignedToCampaign } from "@/hooks/campaignHooks/use-simulate-charge";
import { CampaignChargesProps } from "./campaigns-table/campaign-charges-table";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";

interface DeleteCampaignChargeDialogProps {
    campaigncharge: CampaignChargesProps;
}

const DeleteCampaignChargeDialog: React.FC<DeleteCampaignChargeDialogProps> = ({
    campaigncharge,
}) => {
    const deleteCharge = useDeleteChargeProfileAssignedToCampaign();
    const { toast } = useToast();

    const handleDelete = async () => {
        try {
            await deleteCharge.mutateAsync({
                campaignId: campaigncharge.id,
                chargeId: campaigncharge.chargeprofileid,
                // chargeId: campaigncharge.chargeprofile.id,
            });
            toast({
                title: "Success",
                description: "Charge profile deleted successfully",
            });
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.message || "Failed to delete charge profile",
                variant: "destructive",
            });
        }
    };

    return (
        <AlertDialog>
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <AlertDialogTrigger asChild>
                            <Button variant="icon">
                                <Trash2 className="h-4 w-4 bg-gray-100 text-primary dark:bg-background" />
                            </Button>
                        </AlertDialogTrigger>
                    </TooltipTrigger>
                    <TooltipContent>Delete Charge</TooltipContent>
                </Tooltip>
            </TooltipProvider>

            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                        This will permanently delete this charge profile from the campaign.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
};

export default DeleteCampaignChargeDialog;
