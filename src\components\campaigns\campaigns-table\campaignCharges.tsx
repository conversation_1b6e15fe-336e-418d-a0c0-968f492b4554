import React, { lazy, Suspense, useCallback, useState } from "react";
import { Input } from "@/components/ui/input";
import { Navigate, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

import { ServerSideDataTable } from "@/components/ui/server-side-data-table";
import loadable from "@loadable/component";
import { debounce } from "lodash";
import { useAuth } from "@/contexts/AuthContexts";
import AddSingleCharge from "../add-single-charge";
import CampaignProfileColumns from "./campaign-profile";

export interface CampaignsProps {
    id: number;
    allowcreditapi: boolean;
    apikey: string | null;
    apikeyexpirydate: string | null;
    autopayout: boolean;
    callbackurl: string;
    createby: number;
    createddate: string;
    description: string;
    enddate: string | null;
    ipwhitelist: string | null;
    isactive: boolean;
    isenhanced: boolean;
    legacyusername: string;
    settlementcallbackurl: string | null;
    startdate: string;
    transnotif: boolean;
    transnotiftype: string | null;
    updatedby: number | null;
    updateddate: string | null;
    accountid: number;
    approvedby: number;
    approveddate: string;
    isapproved: boolean;
    enableotp: boolean;
    payoutaccountid: number | null;
    senderid: string;
    itcproductid: string | null;
    itcmerchantid: string | null;
    itcapikey: string | null;
}

export interface SimulateChargeDialogProps {
    amount: number;
    brandgroup: string;
    transtype: string;
}

type CampaignTableProps = {
    // columns: ColumnDef<any, any>[];
    data: any;
    filterable?: boolean;
    title?: string;
    loading?: boolean;
    pagination: any;
    pageCount: number;
    keyword: string;
    sorting: any;
    onSortingChange: (newSorting: any) => void;
    onPaginationChange: (newPagination: any) => void;
    onKeywordChange: (newKeyword: string) => void;
};

const ViewCampaignChargeDialogLazy = loadable(() => import("@/components/campaigns/view-profile"));
const EditCampaignProfileDialogLazy = loadable(
    () => import("@/components/campaigns/edit-charge-profile")
);

export default function ServerSideCampaignChargesTable({
    data,
    filterable = true,
    title,
    loading,
    pagination,
    pageCount,
    keyword,
    sorting,
    onPaginationChange,
    onKeywordChange,
    onSortingChange,
}: CampaignTableProps) {
    const { user } = useAuth();
    const navigate = useNavigate();
    const [searchTerm, setSearchTerm] = useState("");
    const [open, setOpen] = React.useState(false);

    const debouncedSearch = useCallback(
        debounce((value: string) => {
            onKeywordChange(value);
        }, 500),
        [onKeywordChange]
    );

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        debouncedSearch(value);
    };

    const columns = CampaignProfileColumns({
        ViewCampaignChargeDialogLazy,
        EditCampaignProfileDialogLazy,
    });

    return (
        <div className="mx-auto py-8">
            {filterable && (
                // <Card className="flex items-center my-4">
                //     <div>
                //         <CardHeader>Filters</CardHeader>
                //         <CardContent>
                //             <div className="grid w-full grid-cols-4 my-4 gap-x-4">
                //                 <Input
                //                     type="text"
                //                     placeholder="Search..."
                //                     value={searchTerm}
                //                     onChange={handleSearchChange}
                //                 />
                //             </div>
                //         </CardContent>
                //     </div>
                //     <Button
                //         variant="outline"
                //         size="lg"
                //         className="h-12 w-96"
                //         onClick={() => setOpen(true)}
                //     >
                //         <Add className="w-4 h-4 mr-2" />
                //         Add Campaign Charge
                //     </Button>
                // </Card>
                <div className="flex w-full flex-col">
                    <AddSingleCharge user={user} campaignId={0} />
                    <Card className="my-4 w-full">
                        {/* <CardHeader>Filter</CardHeader> */}
                        <CardContent className="w-full p-5">
                            <Input
                                type="text"
                                placeholder="Search..."
                                value={searchTerm}
                                onChange={handleSearchChange}
                                className="w-full rounded-sm"
                            />
                        </CardContent>
                    </Card>
                </div>
            )}
            <ServerSideDataTable
                columns={columns}
                data={data || []}
                title={title || "Campaign Profile"}
                loading={loading}
                pagination={pagination}
                pageCount={pageCount}
                keyword={keyword}
                sorting={sorting}
                onPaginationChange={onPaginationChange}
                onKeywordChange={onKeywordChange}
            />
        </div>
    );
}
