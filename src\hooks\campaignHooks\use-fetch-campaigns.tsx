import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { UsersApiResponse } from "@/lib/interfaces";
import { fetchAllCampaignProfiles, fetchAllCampaigns } from "@/api/campaigns/appsApi";

export const useFetchCampaigns = (initialPageSize: number = 10) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });

    const [keyword, setKeyword] = useState<string>("");
    const [sorting, setSorting] = useState([{ id: "createddate", desc: true }]);
    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        queryKey: ["campaigns", pagination.pageIndex, keyword, pagination.pageSize, sorting],
        queryFn: () =>
            fetchAllCampaigns(pagination.pageIndex, pagination.pageSize, keyword, sorting),
        staleTime: 1000,
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const onKeywordChange = (newKeyword: string) => {
        setPagination((prev) => ({ ...prev, pageIndex: 0 }));
        setKeyword(newKeyword);
    };
    const onSortingChange = (newSorting: any) => {
        setSorting(newSorting);
    };

    return {
        data: data?.data,
        pagination,
        keyword,
        isLoading,
        isError,
        error,
        sorting,
        onPaginationChange,
        onSortingChange,
        onKeywordChange,
        refetch,
    };
};
export const useFetchCampaignProfile = (initialPageSize: number = 100) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });

    const [keyword, setKeyword] = useState<string>("");
    const [sorting, setSorting] = useState([{ id: "createddate", desc: true }]);
    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        queryKey: ["campaigns", pagination.pageIndex, keyword, pagination.pageSize, sorting],
        queryFn: () =>
            fetchAllCampaignProfiles(pagination.pageIndex, pagination.pageSize, keyword, sorting),
        staleTime: 1000,
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const onKeywordChange = (newKeyword: string) => {
        setPagination((prev) => ({ ...prev, pageIndex: 0 }));
        setKeyword(newKeyword);
    };
    const onSortingChange = (newSorting: any) => {
        setSorting(newSorting);
    };

    return {
        data: data?.data,
        pagination,
        keyword,
        isLoading,
        isError,
        error,
        sorting,
        onPaginationChange,
        onSortingChange,
        onKeywordChange,
        refetch,
    };
};

// export const useFetchConfirmedTransactions = (
//     accountId: number,
//     initialPageSize: number = 10,
//     campaignId?: number,
//     fromDate?: string,
//     toDate?: string,
//     transType?: string,
//     search?: string
// ) => {
//     const [pagination, setPagination] = useState({
//         pageIndex: 0,
//         pageSize: initialPageSize,
//     });

//     const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
//         queryKey: [
//             "confirmed-transactions",
//             pagination.pageIndex,
//             pagination.pageSize,
//             fromDate,
//             toDate,
//             transType,
//             search,
//             sorting,
//         ],
//         queryFn: () =>
//             fetchConfirmedTransactions(
//                 pagination.pageIndex,
//                 pagination.pageSize,
//                 keyword,
//                 {
//                     from: fromDate,
//                     to: toDate,
//                     transtype: transType,
//                     search,
//                 },
//                 sorting
//             ),
//     });

//     const onPaginationChange = (newPagination: any) => {
//         setPagination(newPagination);
//     };

//     const onKeywordChange = (newKeyword: string) => {
//         setKeyword(newKeyword);
//     };

//     const onSortingChange = (newSorting: any) => {
//         setSorting(newSorting);
//     };

//     return {
//         data: data?.data,
//         pagination,
//         keyword,
//         sorting,
//         isLoading,
//         isError,
//         error,
//         onPaginationChange,
//         onKeywordChange,
//         onSortingChange,
//         refetch,
//     };
// };
