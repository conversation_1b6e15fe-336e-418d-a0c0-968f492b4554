import { useCallback, useState } from "react";
import { fetchAllUsers } from "@/api/user/authApi";
import { useMutation, useQuery } from "@tanstack/react-query";
import { IResponseParams, UsersApiResponse } from "@/lib/interfaces";
import {
    fetchAllReviewTransactions,
    fetchTransactions,
    reverseTransactions,
} from "@/api/accounts/accountsApi";
// import { FilterState } from "@/pages/transactions/table/server-side-trans-table";
import { campaignCharges, chargesProfile } from "@/api/campaigns/appsApi";
import { FormState } from "@/components/campaigns/campaigns-table/chargeProfile";

export interface SortingState {
    id: string;
    desc: boolean;
}
export const useFetchTransactions = (
    accountId: number,
    initialPageSize: number = 50,
    campaignId?: number,
    fromDate?: string,
    toDate?: string,
    transType?: string,
    search?: string
) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });
    const [keyword, setKeyword] = useState("");

    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        //         queryKey: ["transactions", accountId, page, size, campaignId, fromDate, toDate, transType, search],
        queryKey: [
            "transactions",
            accountId,
            pagination.pageIndex,
            pagination.pageSize,
            campaignId,
            fromDate,
            toDate,
            transType,
            search,
        ],
        queryFn: () =>
            fetchTransactions(accountId, pagination.pageIndex, pagination.pageSize, keyword, {
                campaignid: campaignId,
                from: fromDate,
                to: toDate,
                transtype: transType,
                search,
            }),
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const onKeywordChange = (newKeyword: string) => {
        setKeyword(newKeyword);
    };

    return {
        data: data?.data,
        pagination,
        keyword,
        isLoading,
        isError,
        error,
        onPaginationChange,
        onKeywordChange,
        refetch,
    };
};

interface Reverse {
    reason: any;
    transactionid: any;
}
export interface TransactionFilter {
    keyword?: string;
    transactionDateRange?: {
        from?: string;
        to?: string;
    };
    settledDate?: string;
    transDay?: string;
    status?: string;
    brand?: string;
    brandGroup?: string;
    channel?: string;
    transtype?: string;
    settlement?: string;
    campaignId?: string;
    charge?: string;
    isAvailable?: string;
    legacyId?: string;
}

// Updated hook with return type
interface ReviewTransactionFilter {
    key: string;
    value: any;
    value2?: any;
}

/**
 * A hook that wraps the createCampaign function in a mutation for use with react-query.
 *
 * This hook returns a mutation function that calls the createCampaign function with the provided data.
 * It can be used to handle campaign creation in React components.
 *
 * @returns {UseMutationResult} A mutation result object from react-query, containing the mutation function and related states.
 */
export const useFetchReviewTransactions = (accountId: number) => {
    return useMutation({
        mutationFn: (filters: ReviewTransactionFilter[]) =>
            fetchAllReviewTransactions(accountId, filters),
    });
};

export const useReverseTransaction = () => {
    return useMutation({
        mutationFn: (data: Reverse) => reverseTransactions(data),
    });
};
export const useChargeCampaign = (campaignId: number) => {
    return useMutation({
        mutationFn: (data: FormState) => campaignCharges(campaignId, data),
    });
};
export const useChargeProfile = () => {
    return useMutation({
        mutationFn: (data: FormState) => chargesProfile(data),
    });
};
