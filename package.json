{"name": "redde-admin-desktop", "productName": "Redde Admin Dashboard", "version": "1.0.0", "description": "A desktop application for redde admin", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "prett": "prettier . --write"}, "author": "wigal.com.gh", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.4.0", "@electron-forge/maker-deb": "^7.4.0", "@electron-forge/maker-dmg": "^7.5.0", "@electron-forge/maker-rpm": "^7.4.0", "@electron-forge/maker-squirrel": "^7.4.0", "@electron-forge/maker-zip": "^7.4.0", "@electron-forge/plugin-auto-unpack-natives": "^7.4.0", "@electron-forge/plugin-fuses": "^7.4.0", "@electron-forge/plugin-vite": "^7.4.0", "@electron-forge/shared-types": "^7.4.0", "@electron/fuses": "^1.8.0", "@swc/cli": "^0.4.0", "@swc/core": "^1.6.6", "@swc/jest": "^0.2.36", "@tanstack/eslint-plugin-query": "^5.51.15", "@types/lodash": "^4.17.14", "@types/node": "^20.14.9", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "electron": "31.0.1", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.4", "ts-node": "^10.9.2", "typescript": "~5.4.5", "vite": "^5.3.2"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@loadable/component": "^5.16.4", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@reduxjs/toolkit": "^2.2.6", "@tanstack/react-query": "^5.51.21", "@tanstack/react-query-devtools": "^5.51.21", "@tanstack/react-table": "^8.19.3", "@types/loadable__component": "^5.13.9", "@vitejs/plugin-react": "^4.3.1", "address": "^2.0.3", "axios": "^1.7.2", "chart.js": "^4.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.12", "electron-squirrel-startup": "^1.0.1", "embla-carousel-react": "^8.5.2", "es-toolkit": "^1.15.1", "exceljs": "^4.4.0", "i18next": "^23.11.5", "iconsax-react": "^0.0.8", "lodash": "^4.17.21", "lucide-react": "^0.411.0", "network": "^0.7.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.1", "react-i18next": "^14.1.2", "react-icons": "^5.2.1", "react-redux": "^9.1.2", "react-router-dom": "^6.24.1", "recharts": "^2.12.7", "redux-persist": "^6.0.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.23.8"}}