import React, { lazy, Suspense, useCallback, useState } from "react";

import { Input } from "@/components/ui/input";

import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

import { CalendarDateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import { PaginationState, ServerSideDataTable } from "@/components/ui/server-side-data-table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import loadable from "@loadable/component";
import campaignsTableColumns from "./campaigns-columns";
import { debounce } from "lodash";
import CampaignChargesColumns from "./campaign-charges-columns";
import { SortingState } from "@tanstack/react-table";

export interface ChargeProfileDetails {
    brand: string;
    brandgroup: string;
    chargemodel: string;
    chargetype: string;
    id: number;
    lowerbound: number;
    merchantcharge: number;
    payercharge: number;
    transtype: string;
    upperbound: number;
}

export interface ChargeProfile {
    chargeprofiledetails: ChargeProfileDetails[];
    createdby: number;
    createddate: string;
    description: string;
    id: number;
    isactive: boolean;
    name: string;
    updatedby: number;
    updateddate: string;
}

export interface CampaignChargesProps {
    campaignid: number;
    chargeprofile: ChargeProfile;
    chargeprofileid: number;
    chargeprofiletype: string;
    createdby: number;
    createddate: string;
    enddate: string;
    id: number;
    startdate: string;
    updatedby: number;
    updateddate: string;
}
export interface CampaignProfileProps {
    chargeprofiledetails: ChargeProfileDetails[];
    createdby: number;
    createddate: string;
    isactive: boolean;
    name: string;
    description: string;
    id: number;
    startdate: string;
    updatedby: number;
    updateddate: string;
}

export interface SimulateChargeDialogProps {
    amount: number;
    brandgroup: string;
    transtype: string;
}

type CampaignChargesTableProps = {
    data: CampaignChargesProps[];
    filterable?: boolean;
    title?: string;
    loading?: boolean;
    pagination?: PaginationState;
    pageCount?: number;
    keyword?: string;
    sorting?: SortingState;
    onPaginationChange?: (pagination: PaginationState) => void;
    onKeywordChange?: (keyword: string) => void;
    onSortingChange?: (sorting: SortingState) => void;
};

const ModifyCampaignChargeDialogLazy = loadable(
    () => import("@/components/campaigns/modify-campaign-charge")
);
const ViewCampaignChargeDialogLazy = loadable(
    () => import("@/components/campaigns/campaign-charge")
);

const DeleteCampaignChargeDialogLazy = loadable(
    () => import("@/components/campaigns/delete-campaign-charge-dialog"),
    {
        fallback: <div>Loading...</div>,
    }
);
// const ApproveCampaignDialogLazy = loadable(() => import("@/components/campaigns/approve-campaign"));
// const EditCampaignDialogLazy = loadable(() => import("@/components/campaigns/edit-campaign"));
// const SimulateChargeDialogLazy = loadable(
//     () => import("@/components/campaigns/simulate-charge-dialog")
// );

export default function ServerSideCampaignsChargesTable({
    data,
    filterable = true,
    title,
    loading,
    pagination,
    pageCount,
    keyword,
    sorting,
    onPaginationChange,
    onKeywordChange,
    onSortingChange,
}: CampaignChargesTableProps) {
    const navigate = useNavigate();

    const [searchTerm, setSearchTerm] = useState("");

    const debouncedSearch = useCallback(
        debounce((value: string) => {
            onKeywordChange?.(value);
        }, 500),
        [onKeywordChange]
    );

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        debouncedSearch(value);
    };

    // const viewCampaign = (campaign: CampaignChargesProps) => {
    //     navigate("/campaign-profile", { state: { campaign } });
    // };

    const columns = CampaignChargesColumns({
        ViewCampaignChargeDialogLazy,
        ModifyCampaignChargeDialogLazy,
        DeleteCampaignChargeDialogLazy,
    });
    // console.log("demo campaigns", columns);
    return (
        <div className="mx-auto py-8">
            {filterable && (
                <Card className="my-4">
                    <CardHeader>Filter</CardHeader>
                    <CardContent>
                        <div className="my-4 grid w-full grid-cols-4 gap-x-4">
                            <Input
                                type="text"
                                placeholder="Search by ID, sender ID, username, merchant ID..."
                                value={searchTerm}
                                onChange={handleSearchChange}
                                className="w-full"
                            />
                        </div>
                    </CardContent>
                </Card>
            )}
            <ServerSideDataTable
                columns={columns}
                data={data || []}
                title={title || "Campaign Charges"}
                loading={loading}
                pagination={pagination}
                pageCount={pageCount}
                keyword={keyword}
                sorting={sorting}
                onPaginationChange={onPaginationChange}
                onKeywordChange={onKeywordChange}
            />
        </div>
    );
}
