import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { boolean, z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { useNavigate } from "react-router-dom";
import { Textarea } from "../ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useCreateCampaign } from "@/hooks/campaignHooks/use-create-campaign";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "../ui/dialog";
import { PlusIcon } from "lucide-react";
import { useCreateUser } from "@/hooks/usersHooks/use-create-user";
import { useAuth } from "@/contexts/AuthContexts";

const formSchema = z.object({
    createdby: z.number().optional(),
    accountid: z.number().optional(),
    email: z.string().optional(),
    expirydate: z.string().optional(),
    iscompany: z.boolean().optional(),
    mobilenumber: z.string().optional(),
    name: z.string().optional(),
    islocked: z.boolean().optional(),
    password: z.string().optional(),
    userid: z.number().optional(),
    // profile: z
    //     .object({
    //         id: z.number().optional(),
    //         isactive: z.boolean().optional(),
    //         notes: z.string().optional(),
    //         permissions: z.array(
    //             z
    //                 .object({
    //                     authitemid: z.string().optional(),
    //                     authitemname: z.string().optional(),
    //                     canadd: z.boolean().optional(),
    //                     canapprove: z.boolean().optional(),
    //                     candelete: z.boolean().optional(),
    //                     canedit: z.boolean().optional(),
    //                     canprint: z.boolean().optional(),
    //                     canview: z.boolean().optional(),
    //                     canviewlog: z.boolean().optional(),
    //                     id: z.number().optional(),
    //                 })
    //                 .optional()
    //         ),
    //         profilename: z.string().optional(),
    //     })
    //     .optional(),
});

function AddUserForm(data?: any) {
    // const { login, macAddress, user } = useAuth();
    // const { user } = useAuth();
    const [isModalOpen, setIsModalOpen] = useState(false);

    const { toast } = useToast();
    const navigate = useNavigate();
    const createUser = useCreateUser();

    // Add console.log to debug the data prop
    // console.log("User data props:", data?.data?.useraccount?.userid);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            // accountid: data?.data?.accountid,
            createdby: data?.data?.useraccount?.userid,

            email: "",
            iscompany: false,
            islocked: false,

            mobilenumber: "",
            name: "",
            password: "",
            userid: 0,
        },
    });

    //     {
    //   "email": "<EMAIL>",
    //   "iscompany": true,
    //   "locked": true,
    //   "mobilenumber": "************",
    //   "name": "New Gen Buz,
    //   "password": "StrongPwd52"
    // }
    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            // console.log("Values:", values);
            const result = await createUser.mutateAsync(values);
            console.log("Result:", result);

            toast({
                title: "Success",
                description: "User created successfully",
            });

            setIsModalOpen(false);
            // navigate("/campaigns");
        } catch (error: any) {
            const errorStatus = error?.response?.status;
            const errorMessage =
                error?.response?.data?.message || error.message || "Failed to create user";

            toast({
                title: `Error (${errorStatus || "unknown"})`,
                description: errorMessage,
                variant: "destructive",
            });

            setIsModalOpen(false);
        }
    };

    // React Query provides the isLoading state automatically through your useAuth hook.
    const isLoading = form.formState.isSubmitting;

    return (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
                <Button variant="default">
                    <PlusIcon className="mr-2 h-4 w-4" />
                    Add User
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">Add New User</DialogTitle>
                        {/* <DialogDescription className="">Add a new user  </DialogDescription> */}
                    </DialogHeader>
                </DialogHeader>
                <div>
                    <Form {...form}>
                        <form className="space-y-4">
                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Name</FormLabel>
                                        <Input type="text" {...field} placeholder="Name" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* <div className="grid grid-cols-2 gap-4"> */}
                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Email</FormLabel>
                                        <Input type="text" {...field} placeholder="Email" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="password"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Password</FormLabel>
                                        <Input type="password" {...field} placeholder="Password" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            {/* </div> */}

                            <FormField
                                control={form.control}
                                name="mobilenumber"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Mobile Number
                                        </FormLabel>
                                        <Input type="text" {...field} placeholder="Mobile Number" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* <FormField
                                control={form.control}
                                name="senderid"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Sender ID</FormLabel>
                                        <Input type="text" {...field} placeholder="Sender ID" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel
                                            className="dark:text-white"
                                            htmlFor="description"
                                        >
                                            Description
                                        </FormLabel>
                                        <Textarea {...field} placeholder="Description" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            /> */}

                            <FormField
                                control={form.control}
                                name="iscompany"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-center justify-between p-4">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">Is Company</FormLabel>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />

                            <Button
                                type="submit"
                                size="default"
                                className="w-full"
                                disabled={isLoading}
                                onClick={form.handleSubmit(onSubmit)}
                            >
                                {isLoading ? (
                                    <span className="flex items-center justify-center">
                                        <svg
                                            className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v8H4z"
                                            ></path>
                                        </svg>
                                        Loading...
                                    </span>
                                ) : (
                                    "Submit"
                                )}
                            </Button>
                        </form>
                    </Form>
                </div>
            </DialogContent>
        </Dialog>
    );
}

export default React.memo(AddUserForm);
