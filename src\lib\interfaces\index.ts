import { ReactNode } from "react";
import { LucideIcon } from "lucide-react";

export interface IUserProfileProps {
    toggleSidebar: () => void;
}

export interface INotificationIconProps {
    notificationCount: number;
}

export interface UseApiConfig<T> {
    initialData?: T;
}

export interface Filters {
    keyword?: string;
    type?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
}

export interface RegisterRequest {
    email: string;
    mobile: number;
    iscompany: boolean;
    locked: boolean;
    password: string;
}
export interface LoginRequest {
    email: string;
    macaddress: string;
    password: string;
}

export interface AccessToken {
    token: string;
    tokenType: string;
    createdDate: string;
    expiresOn: string;
}

export interface UserProfile {
    id: number;
    isactive: boolean;
    notes: string | null;
    profilename: string;
    permissions: any[];
}

export interface UserAccount {
    userid: number;
    name: string;
    email: string;
    accountid: number;
    profileid: number;
    isexpired: boolean;
    isactive: boolean;
    iscustomer: boolean;
    islocked: boolean;
    lastlogin: string;
    lastloginip: string;
    profile: UserProfile;
    mobilenumber: string;
    iscompany: boolean;
    requirepasswordchange: boolean;
}

export interface LoginResponse {
    accessToken: AccessToken;
    useraccount: UserAccount;
}

export interface AuthProviderProps {
    children: ReactNode;
}

export interface IAccountIdParams {
    accountid: number;
}

export interface IResponseParams {
    data: {}[];
    message: string;
    status: string;
}

export interface UnconfirmedBalance {
    balance: number;
    accountId: number;
    email: string;
    name: string;
    phone: string;
}

export interface ApiResponseUnconfirmedBalances {
    status: string;
    message: string;
    data: UnconfirmedBalance[][];
}

export interface DashboardCardProps {
    icon: LucideIcon;
    iconColor: string;
    iconBgColor: string;
    title: string;
    value: any;
    selectOptions?: { id: number; label: string; value: string }[];
    menuOptions?: { id: number; label: string; action: () => void }[];
    extraStyles?: any;
}

export interface Permission {
    authitemid: string;
    authitemname: string;
    canadd: boolean;
    canapprove: boolean;
    candelete: boolean;
    canedit: boolean;
    canprint: boolean;
    canview: boolean;
    canviewlog: boolean;
    id: number;
}

export interface Profile {
    id: number;
    isactive: boolean;
    notes: string;
    permissions: Permission[];
    profilename: string;
}

export interface User {
    accountid: number;
    createdby: number;
    createdtime: string;
    datelocked: string;
    datepasswordchanged: string;
    email: string;
    expirydate: string;
    isactive: boolean;
    iscompany: boolean;
    iscustomer: boolean;
    isexpired: boolean;
    islocked: boolean;
    lastlogin: string;
    lastloginip: string;
    mobilenumber: string;
    name: string;
    password: string;
    profile: Profile;
    profileid: number;
    requirepasswordchange: boolean;
    selfcreated: boolean;
    updatedtime: string;
    updateduser: number;
    userid: number;
}

export interface Documents {
    accountid: number;
    approvedby: number;
    createdby: number;
    createddate: string;
    dateapproved: string;
    documentnumber: string;
    documenttypeid: number;
    expirydate: string;
    id: number;
    isapproved: boolean;
    issueddate: string;
    name: string;
    notes: string;
    updatedby: number;
    updateddate: string;
}

export interface UsersApiResponse {
    data: {
        content: User[];
        empty: boolean;
        first: boolean;
        last: boolean;
        number: number;
        numberOfElements: number;
        pageable: {
            offset: number;
            pageNumber: number;
            pageSize: number;
            paged: boolean;
            sort: {
                empty: boolean;
                sorted: boolean;
                unsorted: boolean;
            };
            unpaged: boolean;
        };
        size: number;
        sort: {
            empty: boolean;
            sorted: boolean;
            unsorted: boolean;
        };
        totalElements: number;
        totalPages: number;
    };
    message: string;
    status: string;
}

export interface DocumentApiResponse {
    data: {
        content: Documents[];
        empty: boolean;
        first: boolean;
        last: boolean;
        number: number;
        numberOfElements: number;
        pageable: {
            offset: number;
            pageNumber: number;
            pageSize: number;
            paged: boolean;
            sort: {
                empty: boolean;
                sorted: boolean;
                unsorted: boolean;
            };
            unpaged: boolean;
        };
        size: number;
        sort: {
            empty: boolean;
            sorted: boolean;
            unsorted: boolean;
        };
        totalElements: number;
        totalPages: number;
    };
    message: string;
    status: string;
}

export interface UseFetchUsersParams {
    page: number;
    pageSize: number;
    sortBy: string;
    sortOrder: "asc" | "desc";
    search: string;
}

export interface CampaignData {
    accountid: number;
    allowcreditapi: boolean;
    apikey: string;
    apikeyexpirydate: string;
    approvedby: number;
    approveddate: string;
    autopayout: boolean;
    callbackurl: string;
    createby: number;
    createddate: string;
    description: string;
    enableotp: boolean;
    enddate: string;
    id: number;
    ipwhitelist: string;
    isactive: boolean;
    isapproved: boolean;
    isenhanced: boolean;
    itcapikey: string;
    itcmerchantid: string;
    itcproductid: string;
    legacyusername: string;
    payoutaccountid: number;
    senderid: string;
    settlementcallbackurl: string;
    startdate: string;
    transnotif: boolean;
    transnotiftype: string;
    updatedby: number;
    updateddate: string;
}

export interface AppContent {
    accountid: number;
    callbackurl: string;
    description: string;
    enddate: string;
    id: number;
    isactive: boolean;
    settlementcallbackurl: string;
    startdate: string;
}

export interface AppResponseObject {
    data: {
        content: AppContent[];
        empty: boolean;
        first: boolean;
        last: boolean;
        number: number;
        numberOfElements: number;
        pageable: {
            offset: number;
            pageNumber: number;
            pageSize: number;
            paged: boolean;
            sort: {
                empty: boolean;
                sorted: boolean;
                unsorted: boolean;
            };
            unpaged: boolean;
        };
        size: number;
        sort: {
            empty: boolean;
            sorted: boolean;
            unsorted: boolean;
        };
        totalElements: number;
        totalPages: number;
    };
    message: string;
    status: string;
}

export interface ChargeProfileDetails {
    brand: string;
    brandgroup: string;
    chargemodel: string;
    chargetype: string;
    id: number;
    lowerbound: number;
    merchantcharge: number;
    payercharge: number;
    transtype: string;
    upperbound: number;
}

export interface ChargeProfile {
    chargeprofiledetails: ChargeProfileDetails[];
    createdby: number;
    createddate: string;
    description: string;
    id: number;
    isactive: boolean;
    name: string;
    updatedby: number;
    updateddate: string;
}

export interface CampaignChargeProfileData {
    campaignid: number;
    chargeprofile: ChargeProfile;
    chargeprofileid: number;
    chargeprofiletype: string;
    createdby: number;
    createddate: string;
    enddate: string;
    id: number;
    startdate: string;
    updatedby: number;
    updateddate: string;
}
