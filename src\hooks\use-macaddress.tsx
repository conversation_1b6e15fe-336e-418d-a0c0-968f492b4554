import { useState, useEffect } from "react";

/**
 * Custom hook to fetch and manage the MAC address of the device.
 *
 * This hook uses the Electron API to retrieve the MAC address asynchronously.
 * It manages the loading state, error handling, and the MAC address itself.
 *
 * @returns {Object} An object containing:
 *   - macAddress: The retrieved MAC address (string or any, depending on the API response)
 *   - error: Error message if the retrieval fails (string | null)
 *   - loading: <PERSON><PERSON><PERSON> indicating whether the MAC address is being fetched
 */
export function useMacAddress() {
    const [macAddress, setMacAddress] = useState<any>("");
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
        /**
         * Asynchronous function to fetch the MAC address.
         * It uses the Electron API and handles potential errors and timeouts.
         */
        async function fetchMacAddress() {
            setLoading(true);
            try {
                const address = await new Promise<string>((resolve, reject) => {
                    window.electron.getMacAddress().then(resolve).catch(reject);

                    // Optionally handle timeout
                    setTimeout(() => reject(new Error("MAC address request timed out")), 5000);
                });
                setMacAddress(address);
            } catch (err) {
                setError("Failed to retrieve MAC address");
                console.error(err);
            } finally {
                setLoading(false);
            }
        }

        fetchMacAddress();
    }, []); // Empty dependency array ensures this effect runs only once on component mount

    return { macAddress, error, loading };
}
