import React, { useCallback, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";

import { format } from "date-fns";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { useNavigate } from "react-router-dom";
import { debounce } from "@/lib/utils";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ServerSideDataTable } from "@/components/ui/server-side-data-table";

export interface IWalletProps {
    accountid: number;
    brandgroup: string;
    createdby: number;
    createddate: string;
    updatedby: number;
    updateddate: string;
    wallet: string;
    nameonwallet: string;
}

type walletProps = {
    data?: any;
    filterable?: boolean;
    isDemo?: boolean;
    bulk?: string;
    title?: string;
    loading?: boolean;
    pagination?: any;
    pageCount?: number;
    keyword?: string;
    onPaginationChange?: (newPagination: any) => void;
    onKeywordChange?: (newKeyword: string) => void;
    sorting?: any;

    onSortingChange?: (newSorting: any) => void;
};
export default function WalletTable({
    data,
    filterable,
    title,
    loading,
    pagination,
    pageCount,
    sorting,
    keyword,
    onPaginationChange,
    onKeywordChange,
    onSortingChange,
}: walletProps) {
    const navigate = useNavigate();
    const [searchTerm, setSearchTerm] = useState("");

    const debouncedSearch = useCallback(
        debounce((value: string) => {
            onKeywordChange(value);
        }, 500),
        [onKeywordChange]
    );

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        debouncedSearch(value);
    };
    const columns: ColumnDef<IWalletProps>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-[2px]"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-[2px]"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "id",
            header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
            cell: ({ row }) => <div className="w-[50px]">{row.getValue("id")}</div>,
            enableSorting: false,
            enableHiding: false,
        },

        {
            accessorKey: "wallet",
            header: "Wallet",
            cell: ({ row }) => <div className="lowercase">{row.getValue("wallet")}</div>,
        },
        // {
        //     accessorKey: "accountid",
        //     header: "Account ID",
        //     cell: ({ row }) => <div className="lowercase">{row.getValue("accountid")}</div>,
        // },
        {
            accessorKey: "brandgroup",
            header: "Provider",
            cell: ({ row }) => <div>{row.getValue("brandgroup")}</div>,
        },

        {
            accessorKey: "createddate",
            header: "Created At",
            cell: ({ row }) => {
                const date = new Date(row.getValue("createddate"));
                return <div>{format(date, "PPP")}</div>;
            },
        },
    ];

    return (
        <Card className="mx-auto py-8">
            <CardContent>
                {filterable && (
                    <Card className="my-4">
                        <CardHeader>Filter</CardHeader>
                        <CardContent>
                            <div className="my-4 grid w-full grid-cols-2 gap-x-4">
                                <Input
                                    type="text"
                                    placeholder="Search mobile money wallets..."
                                    value={searchTerm}
                                    onChange={handleSearchChange}
                                />
                            </div>
                        </CardContent>
                    </Card>
                )}
                <ServerSideDataTable
                    columns={columns}
                    data={data || []}
                    title={title ?? ""}
                    loading={loading}
                    pagination={pagination}
                    pageCount={pageCount}
                    sorting={sorting}
                    onSortingChange={onSortingChange}
                    onPaginationChange={onPaginationChange}
                />
            </CardContent>
        </Card>
    );
}
