import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateCampaign } from "@/api/campaigns/appsApi";

export const useEditCampaign = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: ({ campaignId, updateData }: { campaignId: number; updateData: any }) =>
            updateCampaign(campaignId, updateData),
        onSuccess: () => {
            // Invalidate and refetch
            queryClient.invalidateQueries({ queryKey: ["campaigns"] });
        },
    });

    return {
        editCampaign: mutation.mutate,
        isLoading: mutation.isLoading,
        isError: mutation.isError,
        error: mutation.error,
    };
};
