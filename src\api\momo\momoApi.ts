import { IAccountIdParams, IResponseParams } from "@/lib/interfaces";
import { api } from "../api";

// Gets Account balances for every customer on Redde
/**
 * Fetches the wallets for a specific account.
 *
 * This function sends a GET request to the API to fetch the wallets for a specific account.
 * It includes optional parameters for filtering the wallets.
 *
 * @param {IAccountIdParams} accountId - The ID of the account whose wallets are to be fetched.
 * @param {any} params - Optional parameters for filtering the wallets.
 * @returns {Promise<any>} A promise that resolves to the API response data containing the wallets.
//  */
// export const fetchWallets = async (accountId: IAccountIdParams, params: any) => {
//     const response = await api.get(`/accounts/${accountId}/wallets`, { params });
//     return response?.data;
// };

export const fetchWallets = async (
    accountId: IAccountIdParams,
    page: number,
    pageSize: number,
    keyword: string
) => {
    try {
        const response = await api.get(
            `/accounts/${accountId}/wallets?page=${page}&size=${pageSize}&sort=desc,&search=${keyword}`
        );
        return response?.data;
    } catch (error) {
        throw error;
    }
};
