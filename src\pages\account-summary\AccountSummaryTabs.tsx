import React from "react";

import { SummaryTabs } from "./Tabs";
import ConfirmedBalances from "./confirmed-balances";
import UnconfirmedBalances from "./unconfirmed-balances";
import PendingSettlement from "./pending-settlement";
import TransactionBalances from "./transaction-balances";

import BrandsBalances from "./brands-balances";

export const AccountSummaryTabs = React.memo(() => {
    // const {data: data, isLoading, isError } = useQuery<AppBalanceResponse>('', fetchAppBalances(109))
    const tabContents = {
        "Confirmed Balances": <ConfirmedBalances />,
        "Unconfirmed Balances": <UnconfirmedBalances />,
        "Pending Settlements": <PendingSettlement />,
        "Transaction Balances": <TransactionBalances />,
        "Brands Balances": <BrandsBalances />,
    };
    return (
        <div className="p-4">
            {/* <CardContent> */}
            <SummaryTabs tabContents={tabContents} />
            {/* </CardContent> */}
        </div>
    );
});
