import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { boolean, z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { useNavigate } from "react-router-dom";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "../ui/dialog";
import { PlusIcon, PencilIcon } from "lucide-react";
import { Select, SelectItem, SelectContent, SelectValue, SelectTrigger } from "../ui/select";
import { useFetchBanks } from "@/hooks/optionsHooks/use-fetch-banks";
import { useFetchBankBranches } from "@/hooks/optionsHooks/use-fetch-bank-branches";
import { useCreateBankAccount } from "@/hooks/bankHooks/use-create-bank-accounts";
import { useAuth } from "@/contexts/AuthContexts";
import { useEditBankAccount } from "@/hooks/bankHooks/use-edit-bank-accounts";

const formSchema = z.object({
    updatedby: z.number({
        invalid_type_error: "Updated by must be a number",
    }),
    accountid: z.number({
        invalid_type_error: "Account ID must be a number",
        required_error: "Account ID is required",
    }),
    accountname: z
        .string({
            invalid_type_error: "Account name must be a string",
            required_error: "Account name is required",
        })
        .min(2, "Account name must be at least 2 characters"),
    accountnumber: z
        .string({
            invalid_type_error: "Account number must be a string",
            required_error: "Account number is required",
        })
        .min(6, "Account number must be at least 6 characters"),
    bankid: z.number({
        invalid_type_error: "Bank ID must be a number",
        required_error: "Bank is required",
    }),
    branchid: z.number({
        invalid_type_error: "Branch ID must be a number",
        required_error: "Branch is required",
    }),
    sortcode: z
        .string({
            invalid_type_error: "Sort code must be a string ",
        })
        .optional(),
    swiftcode: z
        .string({
            invalid_type_error: "Swift code must be a string",
        })
        .optional(),
});

function EditBankAccountForm({ data, accountId, bankAccountData }: any) {
    const { user } = useAuth();
    const [bankId, setBankId] = useState<number>(bankAccountData?.bankid || 0);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const { data: banks, isLoading: isLoadingBanks } = useFetchBanks();
    const { data: branches, isLoading: isLoadingBranches } = useFetchBankBranches(bankId);

    const userId = user?.useraccount.userid;
    const userAccountId = accountId;

    const { toast } = useToast();
    // const createBankAccount = useCreateBankAccount(userAccountId);
    const editBankAccount = useEditBankAccount(userAccountId, bankAccountData?.id);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            accountid: userAccountId,
            updatedby: userId,
            accountname: bankAccountData?.accountname || "",
            accountnumber: bankAccountData?.accountnumber || "",
            bankid: bankAccountData?.bankname,
            branchid: bankAccountData?.branch,
            sortcode: bankAccountData?.sortcode || "",
            swiftcode: bankAccountData?.swiftcode || "",
        },
    });

    // console.log("bankAccountData", bankAccountData);
    // Set initial form values when bankAccountData changes
    useEffect(() => {
        if (bankAccountData) {
            form.setValue("bankid", bankAccountData.bankname);
            form.setValue("branchid", bankAccountData.branch);
            setBankId(bankAccountData.bankid);
        }
    }, [bankAccountData, form]);

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            let result;
            result = await editBankAccount.mutateAsync(values);
            toast({
                title: "Success",
                description: "Bank account updated successfully",
            });

            form.reset();
            setIsModalOpen(false);
        } catch (error: any) {
            const errorStatus = error?.response?.status;
            const errorMessage =
                error?.response?.data?.message || error.message || `Failed to ${mode} bank account`;

            toast({
                title: `Error (${errorStatus || "unknown"})`,
                description: errorMessage,
                variant: "destructive",
            });
        }
    };

    // React Query provides the isLoading state automatically through your useAuth hook.
    const isLoading = form.formState.isSubmitting;

    // Update bank selection handler
    const handleBankChange = (value: string) => {
        const numValue = Number(value);
        setBankId(numValue); // Set bankId for branches query
        form.setValue("bankid", numValue); // Update form value
        form.setValue("branchid", undefined); // Reset branch selection when bank changes
    };

    return (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <>
                        <PencilIcon className="h-4 w-4 text-green-500" />
                        {/* Edit Bank Account */}
                    </>
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">
                            Edit Bank Account
                        </DialogTitle>
                        <DialogDescription className="">
                            {` Edit the bank account to this user account.`}
                        </DialogDescription>
                    </DialogHeader>
                </DialogHeader>
                <Form {...form}>
                    <form className="space-y-4">
                        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                            <FormField
                                control={form.control}
                                name="bankid"
                                render={({ field }) => (
                                    <FormItem className="w-full">
                                        <FormLabel>Bank Name</FormLabel>
                                        <Select
                                            onValueChange={handleBankChange}
                                            value={field.value?.toString()}
                                            defaultValue={bankAccountData?.bankid?.toString()}
                                        >
                                            <FormControl>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select Bank" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {isLoadingBanks ? (
                                                    <SelectItem value="loading">
                                                        Loading...
                                                    </SelectItem>
                                                ) : (
                                                    banks?.data?.map((bank: any) => (
                                                        <SelectItem
                                                            key={bank.id}
                                                            value={bank.id.toString()}
                                                        >
                                                            {bank.name}
                                                        </SelectItem>
                                                    ))
                                                )}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="branchid"
                                render={({ field }) => (
                                    <FormItem className="w-full">
                                        <FormLabel>Bank Branch</FormLabel>
                                        <Select
                                            onValueChange={(value) => field.onChange(Number(value))}
                                            value={field.value?.toString()}
                                            defaultValue={bankAccountData?.branchid?.toString()}
                                            disabled={!bankId}
                                        >
                                            <FormControl>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select Branch" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {isLoadingBranches ? (
                                                    <SelectItem value="loading">
                                                        Loading...
                                                    </SelectItem>
                                                ) : (
                                                    branches?.data?.map((branch: any) => (
                                                        <SelectItem
                                                            key={branch.id}
                                                            value={branch.id.toString()}
                                                        >
                                                            {branch.branch}
                                                        </SelectItem>
                                                    ))
                                                )}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <FormField
                            control={form.control}
                            name="accountname"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="dark:text-white">Account Name</FormLabel>
                                    <Input type="text" {...field} placeholder="Account Name" />
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* <div className="grid grid-cols-2 gap-4"> */}
                        <FormField
                            control={form.control}
                            name="accountnumber"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="dark:text-white">
                                        Account Number
                                    </FormLabel>
                                    <Input type="text" {...field} placeholder="Account Number" />
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                            <FormField
                                control={form.control}
                                name="sortcode"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Sort Code</FormLabel>
                                        <Input type="text" {...field} placeholder="Sort Code" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="swiftcode"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Swift Code
                                        </FormLabel>
                                        <Input type="text" {...field} placeholder="Swift Code" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <Button
                            type="submit"
                            size="default"
                            className="w-full"
                            onClick={form.handleSubmit(onSubmit)}
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <span className="flex items-center justify-center">
                                    <svg
                                        className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                    >
                                        <circle
                                            className="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            strokeWidth="4"
                                        ></circle>
                                        <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8v8H4z"
                                        ></path>
                                    </svg>
                                    "Updating..."
                                </span>
                            ) : (
                                "Edit Bank Account"
                            )}
                        </Button>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}

export default React.memo(EditBankAccountForm);
