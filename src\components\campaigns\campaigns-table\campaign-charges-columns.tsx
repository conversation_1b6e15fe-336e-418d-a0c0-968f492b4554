import React, { Suspense } from "react";
import { createRoot } from "react-dom/client";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { EyeIcon, CopyIcon, CheckIcon, XIcon, Recycle, Trash2 } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { isNil } from "es-toolkit";
import { CampaignChargesProps, ChargeProfile } from "./campaign-charges-table";
// Import the component for lazy loading
import loadable from "@loadable/component";

// Lazy load the delete dialog component


interface CampaignTableColumnsProps {
    ViewCampaignChargeDialogLazy: React.ComponentType<{ campaigncharge: CampaignChargesProps }>;
    ModifyCampaignChargeDialogLazy: React.ComponentType<{ campaigncharge: CampaignChargesProps }>;
    DeleteCampaignChargeDialogLazy: React.ComponentType<{ campaigncharge: CampaignChargesProps }>;
}
export const CampaignChargesColumns = ({
    ViewCampaignChargeDialogLazy,
    ModifyCampaignChargeDialogLazy,
    DeleteCampaignChargeDialogLazy,
}: CampaignTableColumnsProps): ColumnDef<CampaignChargesProps>[] => [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-[2px]"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-[2px]"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "id",
            header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
            cell: ({ row }) => <div className="w-[50px]">{row.getValue("id")}</div>,
            enableSorting: true,
            enableHiding: false,
        },
        {
            accessorKey: "chargeprofileid",
            header: ({ column }) => <DataTableColumnHeader column={column} title="Charge Profile ID" />,
            cell: ({ row }) => <div>{row.getValue("chargeprofileid")}</div>,
        },
        {
            accessorKey: "chargeprofiletype",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Charge Profile Type" />
            ),
            cell: ({ row }) => <div className="capitalize">{row.getValue("chargeprofiletype")}</div>,
        },
        {
            accessorKey: "chargeprofile",
            header: ({ column }) => <DataTableColumnHeader column={column} title="Charge Profile" />,
            cell: ({ row }) => {
                const profile = row.getValue("chargeprofile") as ChargeProfile;
                return (
                    <div className="space-y-1">
                        <div className="font-medium">{profile.name}</div>
                        <div className="text-sm text-muted-foreground">{profile.description}</div>
                    </div>
                );
            },
        },
        {
            accessorKey: "startdate",
            header: ({ column }) => <DataTableColumnHeader column={column} title="Start Date" />,
            cell: ({ row }) => {
                const date = new Date(row.getValue("startdate"));
                return <div>{format(date, "PPP")}</div>;
            },
        },
        // {
        //     accessorKey: "enddate",
        //     header: ({ column }) => <DataTableColumnHeader column={column} title="End Date" />,
        //     cell: ({ row }) => {
        //         const date = new Date(row.getValue("enddate"));
        //         return <div>{format(date, "PPP")}</div>;
        //     },
        // },
        {
            accessorKey: "createddate",
            header: ({ column }) => <DataTableColumnHeader column={column} title="Created Date" />,
            cell: ({ row }) => {
                const date = new Date(row.getValue("createddate"));
                return <div>{format(date, "PPP")}</div>;
            },
        },
        {
            id: "actions",
            enableHiding: false,
            header: "Actions",
            cell: ({ row }) => {
                const campaigncharge = row.original; // Get the full row data
                // console.log("cccc", row.original);
                return (
                    <div className="flex items-center gap-2">
                        {/* <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <Button variant="icon">
                                    <EyeIcon className="h-4 w-4 bg-gray-100 text-green-500 dark:bg-background" />
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>View Details</TooltipContent>
                        </Tooltip>
                    </TooltipProvider> */}
                        <Suspense fallback={<div>Loading...</div>}>
                            <ViewCampaignChargeDialogLazy campaigncharge={campaigncharge} />
                        </Suspense>
                        <Suspense fallback={<div>Loading...</div>}>
                            <ModifyCampaignChargeDialogLazy campaigncharge={campaigncharge} />
                        </Suspense>
                        <Suspense fallback={<div>Loading...</div>}>
                            <DeleteCampaignChargeDialogLazy campaigncharge={campaigncharge} />
                        </Suspense>
                    </div>
                );
            },
        },
    ];

export default CampaignChargesColumns;
