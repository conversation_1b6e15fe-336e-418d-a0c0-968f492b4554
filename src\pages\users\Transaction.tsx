import TransactionsTable from "@/components/dashboard/transactions-table";
import React, { useState } from "react";
import Banner from "./Banner";

const Transaction = ({ user }: any) => {
    return (
        <div>
            <div className="relative h-44 rounded-xl border border-[#930006] shadow"></div>

            {/* <TransactionsTable data={[]} filterable={false} title="Transactions" /> */}
        </div>
    );
};
export default Transaction;
