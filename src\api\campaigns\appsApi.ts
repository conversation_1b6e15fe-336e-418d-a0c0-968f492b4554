import { IAccountIdParams, IResponseParams } from "@/lib/interfaces";
import { api } from "../api";

/**
 * Fetches a list of apps associated with a given account ID.
 *
 * @param {IAccountIdParams} accountId - The ID of the account whose apps are to be fetched.
 * @param {any} params - Additional parameters to be passed with the request.
 * @returns {Promise<any>} A promise that resolves to the API response data containing the apps.
 */
export const fetchApps = async (
    accountId: number,
    page: number,
    pageSize: number,
    keyword?: string | number,
    sorting?: { id: string; desc: boolean }[]
) => {
    // const response = await api.get(`campaigns/accounts/${accountId}/apps`, { params });
    // return response?.data;
    const sortQuery = sorting?.map((sort) => `${sort.id},${sort.desc ? "desc" : "asc"}`).join(",");

    const response = await api.get(
        `/campaigns/accounts/${accountId}/apps?page=${page}&size=${pageSize},&sort=${sortQuery || "createddate,desc"},&search=${keyword}`
    );
    return response?.data;
};

/**
 * Fetches all campaigns, supporting pagination and search.
 *
 * @param {number} page - The page number for pagination.
 * @param {number} pageSize - The number of items per page.
 * @param {string} [keyword] - The search keyword to filter campaigns.
 * @returns {Promise<any>} A promise that resolves to the API response data containing the campaigns.
 */
export const fetchAllCampaigns = async (
    page: number,
    pageSize: number,
    keyword?: string | number,
    sorting?: { id: string; desc: boolean }[]
) => {
    const sortQuery = sorting?.map((sort) => `${sort.id},${sort.desc ? "desc" : "asc"}`).join(",");

    // Convert keyword to string and ensure it's not undefined
    const searchTerm = keyword?.toString() || "";

    // Update the API endpoint to include the search parameter
    const response = await api.get(
        `/campaigns?page=${page}&size=${pageSize}&sort=${sortQuery || "createddate,desc"}&search=${encodeURIComponent(searchTerm)}`
    );
    return response?.data;
};
export const fetchAllCampaignProfiles = async (
    page: number,
    pageSize: number,
    keyword?: string | number,
    sorting?: { id: string; desc: boolean }[]
) => {
    const sortQuery = sorting?.map((sort) => `${sort.id},${sort.desc ? "desc" : "asc"}`).join(",");

    // Convert keyword to string and ensure it's not undefined
    const searchTerm = keyword?.toString() || "";

    // Update the API endpoint to include the search parameter
    const response = await api.get(
        `/charges/profiles?page=${page}&size=${pageSize}&sort=${sortQuery || "createddate,desc"}&search=${encodeURIComponent(searchTerm)}`
    );
    return response?.data;
};

/**
 * Approves a campaign by its ID.
 *
 * @param {number} campaignId - The ID of the campaign to be approved.
 * @returns {Promise<any>} A promise that resolves to the API response data indicating the approval status.
 */
export const approveCampaign = async (campaignId: number) => {
    if (!campaignId) {
        throw new Error("Campaign ID is required");
    }

    const response = await api.get(`/campaigns/${campaignId}/approve`);
    return response?.data;
};

/**
 * Updates a campaign by its ID.
 *
 * @param {number} campaignId - The ID of the campaign to be updated.
 * @param {any} updateData - The data to update the campaign with.
 * @returns {Promise<any>} A promise that resolves to the API response data containing the updated campaign details.
 */
export const updateCampaign = async (campaignId: number, updateData: any) => {
    const response = await api.put(`/campaigns/${campaignId}`, updateData);
    return response?.data;
    // console.log("update data", updateData, ",", "campaign id", campaignId);
};

/**
 * Fetches a specific campaign by its ID.
 *
 * @param {number} campainId - The ID of the campaign to be fetched.
 * @returns {Promise<any>} A promise that resolves to the API response data containing the campaign details.
 */
export const viewCampaign = async (campainId: number) => {
    const response = await api.get(`/campaigns/${campainId}`);
    // console.log("response", response);
    return response?.data;
};

/**
 * Creates a new campaign.
 *
 * @param {any} campaignData - The data for creating the new campaign.
 * @returns {Promise<any>} A promise that resolves to the API response data containing the created campaign details.
 */
export const createCampaign = async (campaignData: any) => {
    try {
        const response = await api.post("/campaigns", campaignData);
        return response?.data;
    } catch (error) {
        throw error;
    }
    // console.log("campaign data", campaignData);
};

export const delayedCreateCampaign = async (campaignData: any) => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000));
    const response = await api.post("/campaigns", campaignData);
    // console.log("campaign data", response);

    return { response, success: true };
};

// New function to simulate bulk creation
export const createBulkCampaigns = async (
    campaigns: any[],
    onProgress?: (count: number) => void
) => {
    let successful = 0;
    let failed = 0;
    const errors: { record: any; error: any }[] = [];

    // In production, this would be a single API call
    // For now, we'll process them sequentially and return progress
    for (let i = 0; i < campaigns.length; i++) {
        try {
            await delayedCreateCampaign(campaigns[i]);
            successful++;
            // Call the progress callback with the current count
            onProgress?.(successful);
        } catch (error) {
            failed++;
            errors.push({ record: campaigns[i], error });
            // Still call progress even for failures
            onProgress?.(successful + failed);
        }
    }

    return { successful, failed, errors };
};
export const simulateCharge = async (campaignId: number, campaignData: any) => {
    try {
        const response = await api.post(`/campaigns/${campaignId}/simulatecharge`, campaignData);
        // console.log("simulate charge response", response);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const fetchCampaignCharges = async (campaignId: number, active: boolean) => {
    const response = await api.get(`/campaigns/${campaignId}/charges?active=${active}`);
    return response?.data;
};

export const createCampaignCharges = async (campaignId: number, campaignData: any) => {
    try {
        const response = await api.post(`/campaigns/${campaignId}/charges`, campaignData);
        return response?.data;
        // console.log("campaign data", campaignId, campaignData);
    } catch (error) {
        throw error;
    }
};

export const campaignCharges = async (campaignId: number, data?: any): Promise<IResponseParams> => {
    try {
        const response = await api.post<IResponseParams>(
            `/campaigns/${campaignId}/addcharges`,
            data
        );
        return response?.data;
    } catch (error) {
        // console.log("error", error);
        throw error;
    }
};

export const modifyCampaignCharge = async (
    campaignId: number,
    data: any
): Promise<IResponseParams> => {
    try {
        const response = await api.put<IResponseParams>(
            `/campaigns/${campaignId}/changecharges`,
            data
        );
        return response?.data;
    } catch (error) {
        // console.log("error", error);
        throw error;
    }
};

export const deleteChargeProfileAssignedToCampaign = async (
    campaignId: number,
    chargeId: number
) => {
    try {
        const response = await api.delete(
            `/campaigns/${campaignId}/charges/${chargeId}`
        );
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const chargesProfile = async (data: any): Promise<IResponseParams> => {
    try {
        const response = await api.post<IResponseParams>(`/charges/profiles`, data);
        return response?.data;
    } catch (error) {
        // console.log("error", error);
        throw error;
    }
};
export const editChargesProfile = async (
    profileid: number,
    data: any
): Promise<IResponseParams> => {
    try {
        const response = await api.put<IResponseParams>(`/charges/profiles/${profileid}`, data);
        return response?.data;
    } catch (error) {
        // console.log("error", error);
        throw error;
    }
};

export const fetchCampaignCommissions = async (campaignId: number) => {
    try {
        const response = await api.get(`/campaigns/${campaignId}/commissions`);
        return response?.data;
    } catch (error) {
        throw error;
    }
};
