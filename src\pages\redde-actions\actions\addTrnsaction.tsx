// AddUser.js
import { InputForm } from "@/components/InputTemplate";
import { Typography } from "@/components/Typography";
import { toast } from "@/components/ui/use-toast";
import { InfoCircle } from "iconsax-react";
import React from "react";
import BannerAction from "../banner";

const AddTransaction: React.FC = () => {
    const fields = [
        {
            id: 0,
            label: "Amount",
            name: "amount",
            placeholder: "Enter amount",
            inputType: "text" as const,
        },
        {
            id: 1,
            label: "Campaign ID",
            name: "campaignID",
            placeholder: "Enter Campaign ID",
            inputType: "text" as const,
        },
        {
            id: 2,
            label: "Reason",
            name: "reason",
            placeholder: "Enter Reason",
            inputType: "text" as const,
        },
        {
            id: 3,
            checkboxLabel: "Debit",
            name: "debit",
            inputType: "checkbox" as const,
        },
        {
            id: 4,
            checkboxLabel: "Credit",
            name: "credit",
            inputType: "checkbox" as const,
        },
    ];

    const handleSubmit = (data: any) => {
        toast({
            title: "You submitted the following values:",
            description: (
                <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
                    <code className="text-white">{JSON.stringify(data, null, 2)}</code>
                </pre>
            ),
        });
    };
    return (
        <div className="flex h-screen w-full flex-col space-y-10 bg-slate-50 p-10 shadow-lg dark:bg-card">
            <Typography variant="large" text="Simulate Charges" />
            <BannerAction text="Simulate Charges" />
            <div className="flex space-x-2 pb-3">
                <InputForm
                    fields={fields}
                    onSubmit={handleSubmit}
                    layout="row"
                    submit="Submit"
                    submitBg="bg-banner"
                />
            </div>
        </div>
    );
};

export default AddTransaction;
