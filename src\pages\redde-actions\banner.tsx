import { Typography } from "@/components/Typography";
import { InfoCircle } from "iconsax-react";
import React from "react";

interface BannerActionProps {
    text: string;
}

const BannerAction: React.FC<BannerActionProps> = ({ text }) => {
    return (
        <div className="bg-banner flex h-12 w-full items-center space-x-3 rounded-md px-8 py-2">
            <InfoCircle color="#fff" size={20} />
            <Typography variant="p" text={text} className="font-thin text-white dark:font-bold" />
        </div>
    );
};

export default BannerAction;
