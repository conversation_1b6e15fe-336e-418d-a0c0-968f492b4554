import React, { useState, useCallback } from "react";

import { Input } from "@/components/ui/input";
import debounce from "lodash/debounce";

import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

import { ServerSideDataTable } from "@/components/ui/server-side-data-table";
import usersTableColumns from "./users-column";
import { demoUsersTableColumns } from "./demo-users-column";
import { BulkUpload } from "@/components/ui/bulk-upload";
import loadable from "@loadable/component";
import { Button } from "@/components/ui/button";
import AddUserForm from "@/components/users/AddUserForm";
import { useAuth } from "@/contexts/AuthContexts";

export interface UsersProps {
    id: number;
    userid: number;
    name: string;
    email: string;
    accountid: number;
    isexpired: boolean;
    isactive: boolean;
    mobilenumber: string;
    iscompany: boolean;
    createdtime: string;
}

type UserTableProps = {
    // columns: ColumnDef<any, any>[];
    data: any;
    filterable?: boolean;
    isDemo?: boolean;
    bulk?: string;
    title?: string;
    loading?: boolean;
    pagination?: any;
    pageCount?: number;
    keyword?: string;
    onPaginationChange?: (newPagination: any) => void;
    onKeywordChange?: (newKeyword: string) => void;
    sorting?: any;

    onSortingChange?: (newSorting: any) => void;
};

const AddCampaignDialogLazy = loadable(() => import("@/components/campaigns/add-campaign"));
const EditUserDialogLazy = loadable(() => import("@/pages/users/edit-user-form"));
// const
// const AddUserComponentLazy = loadable(() => import("@/components/users/AddUserComponent"));
// Add this type definition for better type safety

export default function ServerSideUsersTable({
    data,
    filterable = true,
    title,
    isDemo = false,
    loading,
    pagination,
    pageCount,
    sorting,
    bulk,
    keyword,
    onPaginationChange,
    onKeywordChange,
    onSortingChange,
}: UserTableProps) {
    const navigate = useNavigate();
    const [searchTerm, setSearchTerm] = useState("");
    const { user } = useAuth();

    const debouncedSearch = useCallback(
        debounce((value: string) => {
            onKeywordChange(value);
        }, 500),
        [onKeywordChange]
    );

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        debouncedSearch(value);
    };

    const viewUser = (user: UsersProps) => {
        navigate("/user-profile", { state: { user } });
    };

    const columns = usersTableColumns({ viewUser, AddCampaignDialogLazy, EditUserDialogLazy });
    const demo_columns = demoUsersTableColumns({ viewUser, AddCampaignDialogLazy });

    return (
        <Card className="mx-auto py-8">
            <CardContent>
                {filterable && (
                    <Card className="my-4">
                        <CardHeader>Filter</CardHeader>
                        <CardContent>
                            <div className="my-4 grid w-full grid-cols-2 items-center gap-x-4">
                                <Input
                                    type="text"
                                    placeholder="Search..."
                                    value={searchTerm}
                                    onChange={handleSearchChange}
                                />

                                {bulk === "bulkcampaign" ? (
                                    <BulkUpload
                                        // onSubmit={handleBulkUpload}
                                        title="Upload Campaign Data"
                                        description="Upload your campaign data in Excel format"
                                    />
                                ) : bulk === "adduser" ? (
                                    <>
                                        {/* <Button variant="default">Add a user</Button> */}
                                        <AddUserForm data={user} />
                                    </>
                                ) : null}
                            </div>
                        </CardContent>
                    </Card>
                )}
                <ServerSideDataTable
                    columns={isDemo ? demo_columns : columns}
                    data={data?.content || []}
                    title={title ?? ""}
                    loading={loading}
                    pagination={pagination}
                    pageCount={pageCount}
                    sorting={sorting}
                    onSortingChange={onSortingChange}
                    onPaginationChange={onPaginationChange}
                />
            </CardContent>
        </Card>
    );
}
