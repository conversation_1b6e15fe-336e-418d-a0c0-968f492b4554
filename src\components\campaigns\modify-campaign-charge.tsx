import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { PencilLine, ServerCog } from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useToast } from "../ui/use-toast";
import { CampaignChargesProps } from "./campaigns-table/campaign-charges-table";
import { useFetchCampaignProfile } from "@/hooks/campaignHooks/use-fetch-campaigns";
import { useFetchChargeProfileTypes } from "@/hooks/optionsHooks/use-fetch-chargeprofiletypes";
import { FormCombobox } from "./form-combobox";
import { Input } from "../ui/input";
import { useModifyCampaignCharge } from "@/hooks/campaignHooks/use-simulate-charge";

interface ModifyCampaignChargeProps {
    campaigncharge: CampaignChargesProps;
}

const formSchema = z.object({
    chargeprofiletype: z.string({
        required_error: "Charge profile type is required",
    }),
    chargeprofile: z.object({}), // Make it flexible to accept any object
    startdate: z.string({
        required_error: "Start date is required",
    }),
    enddate: z.string().optional(),
});

function formatDateTime(inputDateTime: string): string {
    const date = new Date(inputDateTime);

    const formattedDate = date.toISOString().slice(0, 19).replace("T", " ") + ".585";

    return formattedDate;
}

const ModifyCampaignCharge: React.FC<ModifyCampaignChargeProps> = ({ campaigncharge }) => {
    const { data: chargeProfileTypes, isLoading: isLoadingTypes } = useFetchChargeProfileTypes();
    const {
        data: chargeProfiles,
        pagination,
        keyword,
        sorting,
        isLoading: isLoadingProfiles,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
    } = useFetchCampaignProfile(50);

    const [open, setOpen] = React.useState(false);
    const { toast } = useToast();
    const [selectedChargeProfile, setSelectedChargeProfile] = useState<any>(
        campaigncharge?.chargeprofile || null
    );
    const [searchTerm, setSearchTerm] = useState("");
    const modifyCampaignCharge = useModifyCampaignCharge();
    const chargeProfileDetails = campaigncharge?.chargeprofile?.chargeprofiledetails;

    // Format date string for datetime-local input
    const formatDateForInput = (dateString: string | undefined): string => {
        if (!dateString) return "";
        try {
            const date = new Date(dateString);
            return date.toISOString().slice(0, 16); // Format as YYYY-MM-DDThh:mm
        } catch (error) {
            return "";
        }
    };

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            chargeprofiletype: campaigncharge?.chargeprofiletype || "",
            chargeprofile: campaigncharge?.chargeprofile || {},
            startdate: formatDateForInput(campaigncharge?.startdate),
            enddate: formatDateForInput(campaigncharge?.enddate) || "",
        },
    });

    const handleChargeProfileChange = (selectedValue: string) => {
        // Find the selected charge profile and set its details
        const profile = chargeProfiles?.content?.find(
            (profile: any) => profile.name === selectedValue
        );
        if (profile) {
            setSelectedChargeProfile(profile);
            form.setValue("chargeprofile", profile);
        } else {
            setSelectedChargeProfile(null);
            form.setValue("chargeprofile", {});
        }
    };

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        const payload = {
            campaignid: campaigncharge?.id,
            // chargeprofile: selectedChargeProfile || {},
            chargeprofileid: selectedChargeProfile?.id || "",
            chargeprofiletype: form.getValues().chargeprofiletype,
            // createdby: userId,
            startdate: formatDateTime(form.getValues().startdate ?? ""),
            enddate: form.getValues().enddate ? formatDateTime(form.getValues().enddate ?? "") : "",
        };

        try {
            await modifyCampaignCharge.mutateAsync({
                accountId: campaigncharge?.id,
                campaignData: [payload],
            });
            toast({
                title: "Success",
                description: "Charge modified successfully",
            });
            setOpen(false);
        } catch (error: any) {
            const errorStatus = error?.response?.status;
            const errorMessage =
                error?.response?.data?.message || error.message || "Failed to modify charge";

            toast({
                title: `Error (${errorStatus || "unknown"})`,
                description: errorMessage,
                variant: "destructive",
            });
        }
    };

    const isLoading = form.formState.isSubmitting || modifyCampaignCharge.isPending;

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <PencilLine className="h-4 w-4 bg-gray-100 text-green-500 dark:bg-background" />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-screen-xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">
                            Modify Charge Profile Details
                        </DialogTitle>
                    </DialogHeader>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="chargeprofiletype"
                                render={({ field }) => (
                                    <FormItem className="w-full">
                                        <FormLabel>Charge Profile Type</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value ?? ""}
                                        >
                                            <FormControl>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select charge profile type" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {isLoadingTypes ? (
                                                    <SelectItem value="loading">
                                                        Loading...
                                                    </SelectItem>
                                                ) : (
                                                    chargeProfileTypes?.data?.map(
                                                        (type: any, index: any) => (
                                                            <SelectItem key={index} value={type}>
                                                                {type}
                                                            </SelectItem>
                                                        )
                                                    )
                                                )}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormCombobox
                                form={form}
                                name="chargeprofile"
                                label="Charge Profile"
                                // data={formattedProfiles}
                                data={chargeProfiles?.content || []}
                                isLoading={isLoadingProfiles}
                                onSearch={setSearchTerm}
                                onSelect={handleChargeProfileChange}
                                selectedValue={selectedChargeProfile}
                                onKeywordChange={onKeywordChange}
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="startdate"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Start Date</FormLabel>
                                        <FormControl>
                                            <Input type="datetime-local" {...field} className="" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="enddate"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>End Date</FormLabel>
                                        <FormControl>
                                            <Input type="datetime-local" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <Button
                            type="submit"
                            size="default"
                            className="w-full"
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <span className="flex items-center justify-center">
                                    <svg
                                        className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                    >
                                        <circle
                                            className="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            strokeWidth="4"
                                        ></circle>
                                        <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8v8H4z"
                                        ></path>
                                    </svg>
                                    Loading...
                                </span>
                            ) : (
                                "Modify Charge"
                            )}
                        </Button>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
};

export default ModifyCampaignCharge;
