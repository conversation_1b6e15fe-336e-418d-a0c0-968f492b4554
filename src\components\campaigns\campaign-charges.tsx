// AddUser.js
import React, { useState } from "react";

import ServerSideCampaignsTable from "@/components/campaigns/campaigns-table/server-side-table";
import { useFetchCampaignCharges } from "@/hooks/campaignHooks/use-fetch-campaign-charges";
import _ from "lodash";
import ServerSideCampaignsChargesTable from "./campaigns-table/campaign-charges-table";
import { Button } from "../ui/button";
import { PlusIcon } from "lucide-react";
import AddSingleCharge, { FormState } from "./add-single-charge";
import { useAuth } from "@/contexts/AuthContexts";
import { useChargeCampaign } from "@/hooks/accountsHooks/use-fetch-transactions";
import { toast } from "../ui/use-toast";

interface CampaignChargesProps {
    campaignId: number;
    active: boolean;
}

const CampaignCharges: React.FC<CampaignChargesProps> = ({ campaignId, active }) => {
    const { user } = useAuth();
    const [open, setOpen] = React.useState(false);
    const [campaignid, setCampaignid] = useState<number | null>(null);
    const chargeCampaign = useChargeCampaign(campaignId ?? 0);
    const {
        data,
        isLoading,
        pagination,
        pageCount,
        keyword,
        sorting,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
    } = useFetchCampaignCharges(campaignId, active);

    return (
        <div className="">
            <div className="flex w-full justify-end">
                {/* <Button variant="default" size="sm" onClick={() => setOpen(true)}>
                    <PlusIcon className="h-4 w-4" />
                    Add Campaign Charge
                </Button> */}

                {/* <AddSingleCharge
                    open={open}
                    onOpenChange={setOpen}
                    // onSubmit={handleSubmit}
                    user={user}
                    campaignId={campaignId}
                /> */}
            </div>

            <ServerSideCampaignsChargesTable
                data={data}
                title="Campaign Charges"
                loading={isLoading}
                filterable={false}
                pagination={pagination}
                pageCount={pageCount}
                keyword={keyword}
                sorting={sorting}
                onPaginationChange={onPaginationChange}
                onKeywordChange={onKeywordChange}
                onSortingChange={onSortingChange}
            />
            {/* <p>Coming Soon</p> */}
        </div>
    );
};

export default CampaignCharges;
