import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>pi<PERSON>xecutor,
    ApiExe<PERSON>or<PERSON><PERSON><PERSON>,
    ApiRequestConfig,
    WithAbortFn,
} from "@/lib/types/apiTypes";
import axios, { AxiosInstance, AxiosRequestConfig, Cancel } from "axios";

const handleLogout = () => {
    localStorage.removeItem("user");
    localStorage.removeItem("userPermissions");
    localStorage.removeItem("token");
    // localStorage.setItem("tokenState", "expired");

    // Force page reload to clear all state
    window.location.href = "/";
};

/**
 * Configuration for the API base URLs.
 */
const apiConfig = {
    baseURL: "https://api.reddeonline.com",
    demoURL: "https://demoapi.reddeonline.com",
};

/**
 * Creates an instance of Axios with default configuration.
 */
const axiosInstance = axios.create();

/**
 * Sets the base URL for the Axios instance based on the demo mode.
 *
 * @param {boolean} isDemo - Indicates if the demo mode is active.
 */
export const setApiBaseUrl = (isDemo: boolean) => {
    axiosInstance.defaults.baseURL = isDemo ? apiConfig.demoURL : apiConfig.baseURL;
};

/**
 * Adds an interceptor to include the bearer token in every request.
 *
 * @param {AxiomsInstance} instance - The Axios instance to add the interceptor to.
 * @param {string} tokenKey - The key to use for retrieving the token from localStorage.
 */
const addAuthInterceptor = (instance: AxiosInstance, tokenKey: string) => {
    instance.interceptors.request.use((config) => {
        const token = localStorage.getItem(tokenKey);
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    });
};

// Adds the authentication interceptor to the axiosInstance
addAuthInterceptor(axiosInstance, "token");

/**
 * Adds a response interceptor to handle errors, specifically token expiration.
 *
 * @param {AxiomsInstance} instance - The Axios instance to add the interceptor to.
 * @param {string} tokenKey - The key to use for retrieving the token from localStorage.
 */
const addResponseInterceptor = (instance: AxiosInstance, tokenKey: string) => {
    instance.interceptors.response.use(
        (response) => response,
        async (error) => {
            const originalRequest = error.config;

            // If the error status is 401 and there is no originalRequest._retry flag,
            // it means the token has expired and we need to refresh it
            if (error.response.status === 401) {
                localStorage.setItem(`${tokenKey}`, "expired");
                handleLogout();

                // You might want to return a specific error for 401
                return Promise.reject({
                    ...error,
                    isAuthError: true,
                    message: "Authentication failed. Please log in again.",
                });
            }

            return Promise.reject(error);
        }
    );
};

// Adds the response interceptor to the axiosInstance
addResponseInterceptor(axiosInstance, "token");

/**
 * Utility function to check if an error is due to request cancellation.
 *
 * @param {unknown} error - The error to check.
 * @returns {boolean} - True if the error is due to request cancellation.
 */
export const didAbort = (error: unknown): error is Cancel & { aborted: boolean } =>
    axios.isCancel(error);

/**
 * Creates a new CancelToken source.
 *
 * @returns {{ cancel: (message?: string) => void; token: CancelToken }} - A new CancelToken source.
 */
const getCancelSource = () => axios.CancelToken.source();

/**
 * Checks if the provided error is an ApiError.
 *
 * @param {unknown} error - The error to check.
 * @returns {boolean} - True if the error is an ApiError.
 */
export const isApiError = (error: unknown): error is ApiError => {
    return axios.isAxiosError(error);
};

/**
 * Enhances a function to include request cancellation logic.
 *
 * @param {WithAbortFn<T>} fn - The function to enhance.
 * @returns {ApiExecutor<T>} - The enhanced function.
 */
const withAbort = <T>(fn: WithAbortFn) => {
    const executor: ApiExecutor<T> = async (...args: ApiExecutorArgs) => {
        const originalConfig = args[args.length - 1] as ApiRequestConfig;
        const { abort, ...config } = originalConfig;

        if (typeof abort === "function") {
            const { cancel, token } = getCancelSource();
            config.cancelToken = token;
            abort(cancel);
        }

        try {
            if (args.length > 2) {
                const [url, body] = args;
                return await fn<T>(url, body, config);
            } else {
                const [url] = args;
                return await fn<T>(url, config);
            }
        } catch (error) {
            console.error("API error:", error);
            if (didAbort(error)) {
                error.aborted = true;
            }
            throw error;
        }
    };

    return executor;
};

/**
 * Factory function to create API methods with axios instance and request cancellation.
 *
 * @param {AxiomsInstance} axios - The Axios instance to use.
 * @returns {object} - An object containing API methods.
 */
const createApi = (axios: AxiosInstance) => {
    return {
        get: <T>(url: string, config: ApiRequestConfig = {}) =>
            withAbort<T>(axios.get)(url, config),
        delete: <T>(url: string, config: ApiRequestConfig = {}) =>
            withAbort<T>(axios.delete)(url, config),
        post: <T>(url: string, body: unknown, config: ApiRequestConfig = {}) =>
            withAbort<T>(axios.post)(url, body, config),
        patch: <T>(url: string, body: unknown, config: ApiRequestConfig = {}) =>
            withAbort<T>(axios.patch)(url, body, config),
        put: <T>(url: string, body: unknown, config: ApiRequestConfig = {}) =>
            withAbort<T>(axios.put)(url, body, config),
    };
};

// Creates the API instance using the axiosInstance
const api = createApi(axiosInstance);

export { api };
