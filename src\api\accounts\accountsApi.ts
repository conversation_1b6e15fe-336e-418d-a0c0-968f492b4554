// import { fetchTransactions } from '@/api/accounts/accountsApi';
// authApi.ts
import { IAccountIdParams, IResponseParams } from "@/lib/interfaces";
import { api } from "../api";

export const fetchAppBalances = async (accountid: IAccountIdParams): Promise<IResponseParams> => {
    const response = await api.get<IResponseParams>(`accounts/${accountid}/adm/appbalances`);
    return response.data;
};

// fetch unconfirmed balances
export const fetchUnconfirmedBalances = async (accountId: IAccountIdParams) => {
    const response = await api.get(`/accounts/${accountId}/adm/unconfirmedbalances`);
    return response?.data;
};

// Gets Account balances for every customer on Redde
export const fetchFullBalances = async (accountId: IAccountIdParams) => {
    const response = await api.get(`/accounts/${accountId}/adm/fullbalances`);
    return response?.data;
};

// Gets Account balances for every all brands on Redde
export const fetchBrandsTodayBalances = async (accountId: IAccountIdParams) => {
    const response = await api.get(`accounts/${accountId}/adm/brandtoday`);
    return response?.data;
};

export const fetchUserBalance = async (accountId: IAccountIdParams): Promise<IResponseParams> => {
    const response = await api.get<IResponseParams>(`accounts/${accountId}/balance`);
    return response?.data;
};

// // Fetch transactions for a given account
// export const fetchTransactions = async (accountId: IAccountIdParams, params: any): Promise<IResponseParams> => {
//     const response = await api.get<IResponseParams>(
//         `/accounts/${accountId}/transactions`, { params }
//     );
//     return response.data;
// };

export const fetchTransactions = async (
    accountId: number,
    page?: number,
    pageSize?: number,
    keyword?: string,
    params?: any
): Promise<IResponseParams> => {
    const response = await api.get<IResponseParams>(
        `/accounts/${accountId}/transactions?page=${page}&size=${pageSize}&sort=desc,&search=${keyword}`,
        { params }
    );
    return response?.data;
};
export const fetchAllTransactions = async (
    accountId: number,
    data?: any[],
    sorting?: { id: string; desc: boolean }[]
): Promise<IResponseParams> => {
    // const sortQuery = sorting?.map((sort) => `${sort.id},${sort.desc ? "desc" : "asc"}`).join(",");

    // console.log(data);
    const response = await api.post<IResponseParams>(
        `/accounts/${accountId}/adm/reviewtrans`,
        data
    );
    return response?.data;
};

export const fetchAllReviewTransactions = async (
    accountId: number,
    filters: Array<{ key: string; value: any; value2?: any }>
): Promise<IResponseParams> => {
    const response = await api.post<IResponseParams>(
        `/accounts/${accountId}/adm/reviewtrans`,
        filters
    );
    return response?.data;
};

export const fetchFailedTransactions = async (
    accountId: number,
    filters: Array<{ key: string; value: any; value2?: any }>
): Promise<IResponseParams> => {
    const response = await api.post<IResponseParams>(
        `/accounts/${accountId}/adm/reviewfailedtrans`,
        filters
    );
    return response?.data;
};
export const reverseTransactions = async (data?: any): Promise<IResponseParams> => {
    const response = await api.post<IResponseParams>(`/actions/reversetransaction`, data);
    return response?.data;
};

export const fetchConfirmedTransactions = async (
    page?: number,
    pageSize?: number,
    keyword?: string,
    params?: any,
    sorting?: { id: string; desc: boolean }[]
): Promise<IResponseParams> => {
    const sortQuery = sorting?.map((sort) => `${sort.id},${sort.desc ? "desc" : "asc"}`).join(",");
    const response = await api.get<IResponseParams>(
        `/actions/confirmtransactions?page=${page}&size=${pageSize}&sort=${sortQuery || "createddate,desc"}&search=${keyword}`,
        { params }
    );
    return response?.data;
};

export const confirmTransactions = async (data: any) => {
    try {
        const response = await api.post(`/actions/confirmtransactions`, data);
        return response?.data;
        // await console.log("confirm transactions data", data);
    } catch (error) {
        throw error;
    }
};

export const addTransaction = async (data: any) => {
    try {
        const response = await api.post("actions/addtransaction", data);
        return response.data;
        // await console.log("transaction", data);
    } catch (error) {
        throw error;
    }
};
