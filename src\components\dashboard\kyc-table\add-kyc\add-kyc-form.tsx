import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { PlusIcon, ImageIcon, X } from "lucide-react";
import { useAuth } from "@/contexts/AuthContexts";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useFetchDocumentTypes } from "@/hooks/kycHooks/use-fetch-documenttypes";
import { useCreateKYCWithDoc } from "@/hooks/kycHooks/use-create-kyc-with-doc";

const formSchema = z.object({
    details: z.object({
        accountid: z.number(),
        approvedby: z.number().optional(),
        createdby: z.number(),
        documentnumber: z.string().min(1, "Document number is required"),
        documenttypeid: z.number().min(1, "Document type is required"),
        expirydate: z.string().optional(),
        isapproved: z.boolean().default(false),
        issueddate: z.string().optional(),
        name: z.string().min(1, "Name is required"),
        notes: z.string().optional(),
    }),
    image: z.object({
        documentid: z.number().optional(),
        image: z.string().optional(),
    }),
});

function AddKYCForm(data?: any) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const { toast } = useToast();
    const { user } = useAuth();
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    // console.log("staff id", data.data);
    const { data: docsData, isLoading: docsLoading, isError: docsError } = useFetchDocumentTypes();

    const createKyc = useCreateKYCWithDoc(data?.data?.accountid);
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            details: {
                accountid: data?.data?.accountid || 0,
                createdby: user?.useraccount?.userid || 0,
                documentnumber: "",
                documenttypeid: undefined,
                expirydate: "",
                issueddate: "",
                name: "",
                notes: "",
                isapproved: false,
            },
            image: {
                documentid: undefined,
                image: undefined,
            },
        },
    });

    // console.log("docs data", docsData);

    // Add function to convert File to base64
    const convertToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                if (typeof reader.result === "string") {
                    // Remove the data:image/[type];base64, prefix
                    // const base64String = reader.result.split(",")[1];
                    resolve(reader.result);
                }
            };
            reader.onerror = (error) => reject(error);
        });
    };

    // Update image upload handler
    const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            const validTypes = ["image/jpeg", "image/png", "image/jpg"];
            if (!validTypes.includes(file.type)) {
                toast({
                    title: "Error",
                    description: "Please upload a valid image file (JPG, PNG)",
                    variant: "destructive",
                });
                return;
            }

            try {
                const base64String = await convertToBase64(file);
                form.setValue("image.image", base64String);
                const url = URL.createObjectURL(file);
                setPreviewUrl(url);
            } catch (error) {
                toast({
                    title: "Error",
                    description: "Failed to process image",
                    variant: "destructive",
                });
            }
        }
    };

    // Update form submission
    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            // No need for FormData anymore since we're sending JSON
            const payload = {
                details: values.details,
                image: {
                    documentid: values.image.documentid,
                    image: values.image.image,
                },
            };

            // Send the payload directly
            const result = await createKyc.mutateAsync(payload);

            // console.log("kyc response", result);

            toast({
                title: "Success",
                description: "KYC document added successfully",
            });

            setIsModalOpen(false);
        } catch (error: any) {
            const errorStatus = error?.response?.status;
            const errorMessage =
                error?.response?.data?.message || error.message || "Failed to add KYC document";

            toast({
                title: `Error (${errorStatus || "unknown"})`,
                description: errorMessage,
                variant: "destructive",
            });
        }
    };

    const isLoading = form.formState.isSubmitting;

    // Update remove image handler
    const handleRemoveImage = () => {
        form.setValue("image.image", undefined);
        if (previewUrl) {
            URL.revokeObjectURL(previewUrl);
            setPreviewUrl(null);
        }
    };

    // Clean up preview URL when component unmounts
    useEffect(() => {
        return () => {
            if (previewUrl) {
                URL.revokeObjectURL(previewUrl);
            }
        };
    }, [previewUrl]);

    return (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
                <Button variant="default">
                    <PlusIcon className="mr-2 h-4 w-4" />
                    Add KYC
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">
                            Add New KYC Document
                        </DialogTitle>
                    </DialogHeader>
                </DialogHeader>
                <div>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <FormField
                                control={form.control}
                                name="details.name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Document Name
                                        </FormLabel>
                                        <Input type="text" {...field} placeholder="Document Name" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="details.documentnumber"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">
                                                Document Number
                                            </FormLabel>
                                            <Input
                                                type="text"
                                                {...field}
                                                placeholder="Document Number"
                                            />
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="details.documenttypeid"
                                    render={({ field }) => (
                                        <FormItem className="w-full">
                                            <FormLabel>Document Type</FormLabel>
                                            <Select
                                                onValueChange={(value) =>
                                                    field.onChange(Number(value))
                                                }
                                                value={field.value?.toString()}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="w-full">
                                                        <SelectValue placeholder="Document type" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {docsLoading ? (
                                                        <SelectItem value="loading">
                                                            Loading...
                                                        </SelectItem>
                                                    ) : (
                                                        docsData?.data?.map((kyc: any) => (
                                                            <SelectItem key={kyc.id} value={kyc.id}>
                                                                {kyc.name}
                                                            </SelectItem>
                                                        ))
                                                    )}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="details.issueddate"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">
                                                Issue Date
                                            </FormLabel>
                                            <Input type="date" {...field} />
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="details.expirydate"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">
                                                Expiry Date
                                            </FormLabel>
                                            <Input type="date" {...field} />
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <FormField
                                control={form.control}
                                name="details.notes"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Notes</FormLabel>
                                        <Textarea {...field} placeholder="Additional Notes" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="details.isapproved"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-center justify-between p-4">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">Approved</FormLabel>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="image.image"
                                render={({ field: { value, onChange, ...field } }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Document Image
                                        </FormLabel>
                                        <FormControl>
                                            <div className="space-y-4">
                                                <div className="flex items-center gap-4">
                                                    <Input
                                                        type="file"
                                                        accept="image/png, image/jpeg, image/jpg"
                                                        onChange={handleImageUpload}
                                                        className="hidden"
                                                        id="image-upload"
                                                        {...field}
                                                    />
                                                    <label
                                                        htmlFor="image-upload"
                                                        className="flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-gray-500 dark:hover:bg-gray-600"
                                                    >
                                                        {previewUrl ? (
                                                            <div className="relative h-full w-full">
                                                                <img
                                                                    src={previewUrl}
                                                                    alt="Document preview"
                                                                    className="h-full w-full rounded-lg object-contain p-2"
                                                                />
                                                                <button
                                                                    type="button"
                                                                    onClick={(e) => {
                                                                        e.preventDefault();
                                                                        handleRemoveImage();
                                                                    }}
                                                                    className="absolute -right-2 -top-2 rounded-full bg-destructive p-1 text-white hover:bg-destructive/90"
                                                                >
                                                                    <X className="h-4 w-4" />
                                                                </button>
                                                            </div>
                                                        ) : (
                                                            <div className="text-center">
                                                                <ImageIcon className="mx-auto h-8 w-8 text-gray-400" />
                                                                <span className="mt-2 block text-sm text-gray-600 dark:text-gray-400">
                                                                    Click to upload document image
                                                                </span>
                                                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                                                    PNG, JPG up to 10MB
                                                                </span>
                                                            </div>
                                                        )}
                                                    </label>
                                                </div>
                                                {previewUrl && (
                                                    <div className="mt-4 rounded-lg border border-gray-200 p-4 dark:border-gray-700">
                                                        <p className="mb-2 text-sm font-medium">
                                                            Preview:
                                                        </p>
                                                        <div className="relative h-[200px] w-full overflow-hidden rounded-lg bg-gray-50 dark:bg-gray-700">
                                                            <img
                                                                src={previewUrl}
                                                                alt="Document preview"
                                                                className="h-full w-full object-contain"
                                                            />
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </FormControl>
                                        <FormDescription>
                                            Upload a clear image of the document (PNG, JPG formats
                                            only)
                                        </FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <Button
                                type="submit"
                                size="default"
                                className="w-full"
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <span className="flex items-center justify-center">
                                        <svg
                                            className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v8H4z"
                                            ></path>
                                        </svg>
                                        Loading...
                                    </span>
                                ) : (
                                    "Submit"
                                )}
                            </Button>
                        </form>
                    </Form>
                </div>
            </DialogContent>
        </Dialog>
    );
}

export default React.memo(AddKYCForm);
