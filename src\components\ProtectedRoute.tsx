import React from "react";
import { PermissionService } from "@/lib/PermissionService";
import { Navigate } from "react-router-dom";

interface ProtectedRouteProps {
    authItemId: string;
    children: React.ReactNode;
}

export const ProtectedRoute = ({ authItemId, children }: ProtectedRouteProps) => {
    const hasAccess = PermissionService.canAccessModule(authItemId);

    if (!hasAccess) {
        return <Navigate to="/unauthorized" replace />;
    }

    return <>{children}</>;
};
