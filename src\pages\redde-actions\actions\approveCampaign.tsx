// AddUser.js
import { InputForm } from "@/components/InputTemplate";
import { Typography } from "@/components/Typography";
import { toast } from "@/components/ui/use-toast";
import React from "react";
import BannerAction from "../banner";

const ApproveCampaign: React.FC = () => {
    const fields = [
        {
            id: 0,
            label: "Campaign ID",
            name: "campaignID",
            placeholder: "Enter Campaign ID",
            inputType: "text" as const,
        },
        {
            id: 1,
            label: "API Request URL",
            name: "requestURL",
            placeholder: "Enter API Request URL",
            inputType: "text" as const,
        },
    ];

    const handleSubmit = (data: any) => {
        toast({
            title: "You submitted the following values:",
            description: (
                <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
                    <code className="text-white">{JSON.stringify(data, null, 2)}</code>
                </pre>
            ),
        });
    };
    return (
        <div className="flex h-screen w-full flex-col space-y-10 bg-slate-50 p-10 shadow-lg dark:bg-card">
            <Typography variant="large" text="Approve Campaign" />
            <BannerAction text="Approve campaign" />
            <div className="flex space-x-2 pb-3">
                <InputForm
                    fields={fields}
                    onSubmit={handleSubmit}
                    layout="row"
                    submit="Approve Campaign"
                    submitBg="bg-banner"
                />
            </div>
        </div>
    );
};

export default ApproveCampaign;
