// import { getMacAddress } from './lib/utils';
// This allows TypeScript to pick up the magic constants that's auto-generated by Forge's Vite
// plugin that tells the Electron app where to look for the Vite-bundled app code (depending on
// whether you're running in development or production).
declare const MAIN_WINDOW_VITE_DEV_SERVER_URL: string;
declare const MAIN_WINDOW_VITE_NAME: string;

// Preload types
interface ThemeModeContext {
    toggle: () => Promise<boolean>;
    dark: () => Promise<void>;
    light: () => Promise<void>;
    system: () => Promise<boolean>;
    current: () => Promise<"dark" | "light" | "system">;
}
interface ElectronWindow {
    minimize: () => Promise<void>;
    maximize: () => Promise<void>;
    close: () => Promise<void>;
    getMacAddress: () => Promise<string>;
    onMacAddress: (callback: (mac: string) => void) => void; // Adjusted to match the implementation
    open: () => Promise<void>;
    goBack: () => Promise<void>;
    goForward: () => Promise<void>;
    canGoBack: () => Promise<boolean>;
    canGoForward: () => Promise<boolean>;
}

declare interface Window {
    themeMode: ThemeModeContext;
    electronWindow: ElectronWindow;
    electron: {
        getMacAddress: () => Promise<string>;
        onMacAddress: (callback: (mac: string) => void) => void;
        goBack: () => Promise<void>;
        goForward: () => Promise<void>;
        canGoBack: () => Promise<boolean>;
        canGoForward: () => Promise<boolean>;
        readExcelFile: (filePath: string) => Promise<any[]>;
    };
}
