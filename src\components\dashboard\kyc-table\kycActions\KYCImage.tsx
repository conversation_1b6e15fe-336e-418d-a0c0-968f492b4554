import React, { useState, useRef } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { EyeIcon, ZoomIn, ZoomOut, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ImagePreviewProps {
    src: string;
    alt?: string;
    className?: string;
    triggerClassName?: string;
}

const KYCImage: React.FC<ImagePreviewProps> = ({
    src,
    alt = "Preview image",
    className = "",
    triggerClassName = "",
}) => {
    const [scale, setScale] = useState(1);
    const [rotation, setRotation] = useState(0);
    const imageRef = useRef<HTMLImageElement>(null);

    const handleZoomIn = () => {
        setScale((prevScale) => Math.min(prevScale + 0.2, 3));
    };

    const handleZoomOut = () => {
        setScale((prevScale) => Math.max(prevScale - 0.2, 0.5));
    };

    const handleReset = () => {
        setScale(1);
        setRotation(0);
    };

    const handleRotate = () => {
        setRotation((prevRotation) => (prevRotation + 90) % 360);
    };

    return (
        <TooltipProvider>
            <Dialog>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <DialogTrigger asChild>
                            <button
                                className={`inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 ${triggerClassName}`}
                            >
                                <EyeIcon className="h-4 w-4" />
                            </button>
                        </DialogTrigger>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>Preview image</p>
                    </TooltipContent>
                </Tooltip>
                <DialogContent className="max-w-7xl overflow-hidden p-0">
                    <div className="relative flex h-[80vh] w-full flex-col">
                        {/* Image Container */}
                        <div className="relative flex-1 overflow-auto bg-black/5 p-4">
                            <div className="flex h-full w-full items-center justify-center">
                                <img
                                    ref={imageRef}
                                    src={src}
                                    alt={alt}
                                    className={`h-auto w-full object-contain transition-transform duration-200 ${className}`}
                                    style={{
                                        transform: `scale(${scale}) rotate(${rotation}deg)`,
                                    }}
                                />
                            </div>
                        </div>

                        {/* Controls */}
                        <div className="flex items-center justify-between border-t bg-background p-4">
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={handleZoomIn}
                                    disabled={scale >= 3}
                                >
                                    <ZoomIn className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={handleZoomOut}
                                    disabled={scale <= 0.5}
                                >
                                    <ZoomOut className="h-4 w-4" />
                                </Button>
                                <Button variant="outline" size="icon" onClick={handleRotate}>
                                    <RotateCcw className="h-4 w-4" />
                                </Button>
                                <Button variant="outline" size="icon" onClick={handleReset}>
                                    Reset
                                </Button>
                            </div>
                            <div className="text-sm text-muted-foreground">
                                {Math.round(scale * 100)}%
                            </div>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </TooltipProvider>
    );
};

export default KYCImage;
