import React from "react";

const TransactionsTable = () => {
    const transactions = [
        {
            transId: "text",
            brandTransId: "Support",
            username: "Today 2:00",
            appName: "Today 2:00",
            brandGroup: "Today 2:00",
        },
        {
            transId: "Website down for one week",
            brandTransId: "Support",
            username: "Yesterday",
            appName: "Yesterday",
            brandGroup: "Yesterday",
        },
        {
            transId: "text",
            brandTransId: "Support",
            username: "Today 2:00",
            appName: "Today 2:00",
            brandGroup: "Today 2:00",
        },
        {
            transId: "Website down for one week",
            brandTransId: "Support",
            username: "Yesterday",
            appName: "Yesterday",
            brandGroup: "Yesterday",
        },
        {
            transId: "text",
            brandTransId: "Support",
            username: "Today 2:00",
            appName: "Today 2:00",
            brandGroup: "Today 2:00",
        },
        {
            transId: "Website down for one week",
            brandTransId: "Support",
            username: "Yesterday",
            appName: "Yesterday",
            brandGroup: "Yesterday",
        },
        {
            transId: "text",
            brandTransId: "Support",
            username: "Today 2:00",
            appName: "Today 2:00",
            brandGroup: "Today 2:00",
        },
        {
            transId: "Website down for one week",
            brandTransId: "Support",
            username: "Yesterday",
            appName: "Yesterday",
            brandGroup: "Yesterday",
        },
    ];

    return (
        <div className="mx-4 mb-24 overflow-x-auto rounded-md border border-gray-200 bg-white">
            <div className="flex flex-row items-center justify-between">
                <h1 className="text-md my-3 ml-4 text-left text-black">Transactions</h1>
                <h1 className="mr-4 text-sm text-red-600">View All</h1>
            </div>
            <table className="min-w-full border bg-white">
                <thead className="border-b bg-red-100">
                    <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            TransID
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            BrandTransID
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            Username
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            App Name
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            BrandGroup
                        </th>
                    </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                    {transactions.map((transaction, index) => (
                        <tr key={index}>
                            <td className="whitespace-nowrap px-4 py-2 text-sm text-gray-700">
                                {transaction.transId}
                            </td>
                            <td className="whitespace-nowrap px-4 py-2 text-sm text-gray-700">
                                {transaction.brandTransId}
                            </td>
                            <td className="whitespace-nowrap px-4 py-2 text-sm text-gray-700">
                                {transaction.username}
                            </td>
                            <td className="whitespace-nowrap px-4 py-2 text-sm text-gray-700">
                                {transaction.appName}
                            </td>
                            <td className="whitespace-nowrap px-4 py-2 text-sm text-gray-700">
                                {transaction.brandGroup}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default TransactionsTable;
