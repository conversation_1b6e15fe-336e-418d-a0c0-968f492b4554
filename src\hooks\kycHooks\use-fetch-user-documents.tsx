import { useState, useCallback } from "react";
import { fetchAllUsers, fetchUserDocuments } from "@/api/user/authApi";
import { useQuery } from "@tanstack/react-query";
import { DocumentApiResponse, UsersApiResponse } from "@/lib/interfaces";
import debounce from "lodash/debounce";

export const useFetchUserDocuments = (initialPageSize: number = 50, accountId: number) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });

    const [keyword, setKeyword] = useState<string>("");
    const [sorting, setSorting] = useState([{ id: "createdtime", desc: true }]);

    const fetchUserDocs = useCallback(() => {
        return fetchUserDocuments(pagination.pageIndex, pagination.pageSize, accountId, keyword);
    }, [pagination.pageIndex, pagination.pageSize, keyword, sorting]);

    const { data, isLoading, isError, error, refetch } = useQuery<DocumentApiResponse, Error>({
        queryKey: ["users", pagination.pageIndex, pagination.pageSize, keyword, sorting],
        queryFn: fetchUserDocs,
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const debouncedKeywordChange = useCallback(
        debounce((newKeyword: string) => {
            setKeyword(newKeyword);
        }, 300),
        []
    );

    const onKeywordChange = (newKeyword: string) => {
        debouncedKeywordChange(newKeyword);
    };

    const onSortingChange = (newSorting: any) => {
        setSorting(newSorting);
    };

    return {
        data: data?.data,
        pagination,
        keyword,
        isLoading,
        isError,
        error,
        sorting,
        onSortingChange,
        onPaginationChange,
        onKeywordChange,
        refetch,
    };
};
