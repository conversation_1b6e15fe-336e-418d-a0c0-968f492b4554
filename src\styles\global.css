@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    @font-face {
        font-family: "Geist";
        font-weight: 100;

        src: url("../assets/fonts/geist/Geist-Thin.woff2") format("woff2");
    }
    @font-face {
        font-family: "Geist";
        font-weight: 200;

        src: url("../assets/fonts/geist/Geist-UltraLight.woff2") format("woff2");
    }
    @font-face {
        font-family: "Geist";
        font-weight: 300;

        src: url("../assets/fonts/geist/Geist-Light.woff2") format("woff2");
    }
    @font-face {
        font-family: "Geist";
        font-weight: 400;
        font-style: normal;

        src: url("../assets/fonts/geist/Geist-Regular.woff2") format("woff2");
    }
    @font-face {
        font-family: "Geist";
        font-weight: 600;

        src: url("../assets/fonts/geist/Geist-SemiBold.woff2") format("woff2");
    }
    @font-face {
        font-family: "Geist";
        font-weight: 700;

        src: url("../assets/fonts/geist/Geist-Bold.woff2") format("woff2");
    }
    @font-face {
        font-family: "Geist";
        font-weight: 800;

        src: url("../assets/fonts/geist/Geist-Black.woff2") format("woff2");
    }
    @font-face {
        font-family: "Geist";
        font-weight: 900;

        src: url("../assets/fonts/geist/Geist-UltraBlack.woff2") format("woff2");
    }
}

@layer base {
    @layer base {
        :root {
            --background: 0 0% 100%;
            --foreground: 240 10% 3.9%;
            --card: 0 0% 100%;
            --card-foreground: 240 10% 3.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 240 10% 3.9%;
            --primary: 346.8 77.2% 49.8%;
            --primary-foreground: 355.7 100% 97.3%;
            --secondary: 240 4.8% 95.9%;
            --secondary-foreground: 240 5.9% 10%;
            --muted: 240 4.8% 95.9%;
            --muted-foreground: 240 3.8% 46.1%;
            --accent: 240 4.8% 95.9%;
            --accent-foreground: 240 5.9% 10%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 0 0% 98%;
            --border: 240 5.9% 90%;
            --input: 240 5.9% 90%;
            --ring: 346.8 77.2% 49.8%;
            --radius: 0.75rem;
            --chart-1: 12 76% 61%;
            --chart-2: 173 58% 39%;
            --chart-3: 197 37% 24%;
            --chart-4: 43 74% 66%;
            --chart-5: 27 87% 67%;
        }

        .dark {
            --background: 20 14.3% 4.1%;
            --foreground: 0 0% 95%;
            --card: 24 9.8% 10%;
            --card-foreground: 0 0% 95%;
            --popover: 0 0% 9%;
            --popover-foreground: 0 0% 95%;
            --primary: 346.8 77.2% 49.8%;
            --primary-foreground: 355.7 100% 97.3%;
            --secondary: 240 3.7% 15.9%;
            --secondary-foreground: 0 0% 98%;
            --muted: 0 0% 15%;
            --muted-foreground: 240 5% 64.9%;
            --accent: 12 6.5% 15.1%;
            --accent-foreground: 0 0% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 0 85.7% 97.3%;
            --border: 240 3.7% 15.9%;
            --input: 240 3.7% 15.9%;
            --ring: 346.8 77.2% 49.8%;
            --chart-1: 220 70% 50%;
            --chart-2: 160 60% 45%;
            --chart-3: 30 80% 55%;
            --chart-4: 280 65% 60%;
            --chart-5: 340 75% 55%;
        }
    }
}

.loader {
    border: 16px solid #f3f3f3;
    border-top: 16px solid #3498db;
    border-radius: 50%;
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@layer base {
    * {
        @apply m-0 border-border p-0 font-sans text-sm;
    }
    body {
        @apply bg-gray-100 text-foreground dark:bg-background;
        @apply select-none;
        @apply font-mono antialiased;
    }

    .draglayer {
        @apply bg-background;
        -webkit-app-region: drag;
    }
}
