import { fetchUser } from "@/api/user/authApi";
import { useQuery } from "@tanstack/react-query";

/**
 * A custom hook for fetching user data.
 *
 * @param {number} userid - The ID of the user to fetch.
 * @returns {Object} An object containing:
 *   - data: The fetched user data.
 *   - isLoading: <PERSON><PERSON><PERSON> indicating if data is being loaded.
 *   - isError: <PERSON><PERSON><PERSON> indicating if an error occurred.
 *   - error: Any error that occurred during the fetch.
 *   - refetch: Function to refetch the data.
 */
export const useFetchUser = (userid: number) => {
    return useQuery({
        queryKey: ["userid", userid],
        queryFn: () => fetchUser(userid),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
};
