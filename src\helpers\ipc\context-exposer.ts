import { ipc<PERSON><PERSON><PERSON> } from "electron";
import { exposeThemeContext } from "./theme/theme-context";
import { exposeWindowContext } from "./window/window-context";

export default function exposeContexts() {
    const preloadLog = {
        info: (message: any) => {
            console.log(`[Preload ${new Date().toISOString()}] INFO: ${message}`);
        },
        warn: (message: any) => {
            console.warn(`[Preload ${new Date().toISOString()}] WARN: ${message}`);
        },
    };

    // Set up network status monitoring
    window.addEventListener("online", () => {
        preloadLog.info("Network connection restored");
        ipcRenderer.send("online-status-changed", "online");
    });

    window.addEventListener("offline", () => {
        preloadLog.warn("Network connection lost");
        ipcRenderer.send("online-status-changed", "offline");
    });

    // Log initial network status
    preloadLog.info(`Initial network status: ${navigator.onLine ? "online" : "offline"}`);

    exposeWindowContext();
    exposeThemeContext();
}
