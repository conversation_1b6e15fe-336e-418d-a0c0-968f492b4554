import { But<PERSON> } from "@/components/ui/button";
import React, { useEffect, useState } from "react";


import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, MoreHorizontal, UserPlus, Wallet, Wallet2 } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
// import { DataTable } from "@/components/Customtable/data-table";
import { DataTable } from "@/components/ui/data-table";
import { CardComponent } from "../confirmed-balances/Card";
import { useQuery } from "@tanstack/react-query";
import { useUnconfirmedBalances } from "@/hooks/accountsHooks/use-unconfirmed-balances";
import { useAuth } from "@/contexts/AuthContexts";
import SkeletonComponent from "./skeleton";
import _ from "lodash";
import { sumBalances, totalClients } from "@/lib/utils";

interface UnconfirmedBalance {
    balance: number;
    accountId: number;
    email: string;
    name: string;
    phone: string;
    accessorKey: string;
}



export const columns: ColumnDef<UnconfirmedBalance>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value: any) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value: any) => row.toggleSelected(!!value)}
                aria-label="Select row"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "balance",
        // header: "Balance",
        header: () => <h2 className="text-left font-bold">Balance</h2>,
        cell: ({ row }) => {
            const balance = parseFloat(row.getValue("balance"));
            const formatted = new Intl.NumberFormat("en-GB", {
                style: "currency",
                currency: "GHS",
            }).format(balance);

            return <div className="font-medium">{formatted}</div>;
        },
    },

    {
        accessorKey: "accountId",
        header: () => <h2 className="text-left font-bold">Account ID</h2>,

    },
    {
        accessorKey: "email",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                    className="hover:bg-transparent"
                >
                    Email
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
    },
    {
        accessorKey: "name",
        header: "Name",
    },
    {
        accessorKey: "phone",
        header: "Phone Number",
    },
];



 function UnconfirmedBalances() {

    const {user } =  useAuth()

    const accountID = user?.useraccount?.accountid

    const { data, isLoading } = useUnconfirmedBalances(accountID);
    // const [data, setData] = useState<Payment[]>([]);

    
      const transformData = (data: any[]): any[] => {
        return data.map((item) => ({
            balance: item[0],
            accountId: item[1],
            email: item[2],
            name: item[3],
            phone: item[4],
        }));
    };




    const transformedData = data ? transformData(data?.data?.flat()) : [];


    const totalBalance = sumBalances(transformedData);
     const formattedTotalBalance = new Intl.NumberFormat("en-GB", {
         style: "currency",
         currency: "GHS",
     }).format(totalBalance);

     const totalNumberOfClients = totalClients(transformedData)
  

    if (isLoading) {
        return <SkeletonComponent />
    }

    return (
        // <div className="flex h-screen items-center justify-center">
        <div className="mx-0 h-screen w-full">
            <main className="flex flex-1 flex-col pb-4">
                <div className="grid grid-cols-3 gap-6">
                    <CardComponent
                        title="Total Clients"
                        value={totalNumberOfClients}
                        icon={<UserPlus className="h-4 w-4 text-blue-500" />}
                    />
                    <CardComponent
                        title="Total Unconfirmed Balances"
                        value={formattedTotalBalance}
                        icon={<Wallet className="h-4 w-4 text-blue-500" />}
                    />
                    <CardComponent
                        title="Actual Balances"
                        value="20"
                        icon={<Wallet2 className="h-4 w-4 text-blue-500" />}
                    />
                </div>
            </main>
            <DataTable
                columns={columns}
                data={transformedData}
                title="Unconfirmed Balances"
                filterKey="name"
            />
        </div>
    );
}

export default UnconfirmedBalances;
