import React from "react";
import { PencilLine } from "lucide-react";
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import EditCampaignForm from "./edit-campaign-form";

interface TransactionDetailsDialogProps {
    campaign: any; // Replace 'any' with the actual type of your user object
}

const EditCampaignDialog: React.FC<TransactionDetailsDialogProps> = ({ campaign }) => {
    // console.log("user", typeof user);
    return <EditCampaignForm data={campaign} />;
};

export default EditCampaignDialog;
