import React, { Suspense } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { EyeIcon, SendHorizontal } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
// import { CampaignsProps } from "./server-side-table";
import { isNil } from "es-toolkit";
import { CampaignsProps } from "@/components/campaigns/campaigns-table/server-side-table";

// interface CampaignTableColumnsProps {
//     viewCampaign?: (campaign: CampaignsProps) => void;
//     ApproveCampaignDialogLazy: React.ComponentType<{ campaign: CampaignsProps }>;
//     EditCampaignDialogLazy: React.ComponentType<{ campaign: CampaignsProps }>;
// }

export const dashboardCampaignsTableColumns = (): ColumnDef<CampaignsProps>[] => [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title=" ID" />,
        cell: ({ row }) => <div className="w-[50px]">{row.getValue("id")}</div>,
        enableSorting: true,
        enableHiding: false,
    },
    {
        accessorKey: "senderid",
        // header: "Sender ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Sender ID" />,

        cell: ({ row }) => (
            <div className="uppercase">
                {isNil(row.getValue("senderid")) ? (
                    <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                        No sender id
                    </span>
                ) : (
                    row.getValue("senderid")
                )}
            </div>
        ),
    },
    {
        accessorKey: "legacyusername",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Legacy Username" />,

        cell: ({ row }) => (
            <div className="lowercase">
                {" "}
                {isNil(row.getValue("legacyusername")) ? (
                    <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                        No username
                    </span>
                ) : (
                    row.getValue("legacyusername")
                )}
            </div>
        ),
    },
    {
        accessorKey: "accountid",
        //  header: "Account ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,

        cell: ({ row }) => <div className="lowercase">{row.getValue("accountid")}</div>,
    },
    // {
    //     accessorKey: "itcmerchantid",
    //     //  header: "Merchant ID",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="Merchant ID" />,

    //     cell: ({ row }) => (
    //         <div>
    //             {isNil(row.getValue("itcmerchantid")) ? (
    //                 <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
    //                     No merchant id
    //                 </span>
    //             ) : (
    //                 row.getValue("itcmerchantid")
    //             )}
    //         </div>
    //     ),
    // },
    // {
    //     accessorKey: "itcapikey",
    //     //  header: "ITC Api key",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="ITC Api key" />,

    //     cell: ({ row }) => (
    //         <div>
    //             {isNil(row.getValue("itcapikey")) ? (
    //                 <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
    //                     No ITC api key
    //                 </span>
    //             ) : (
    //                 row.getValue("itcapikey")
    //             )}
    //         </div>
    //     ),
    // },
    // {
    //     accessorKey: "isapproved",
    //     // header: "Approved",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="Approved" />,

    //     cell: ({ row }) => {
    //         const approved = row.getValue("isapproved");
    //         return <p className="font-medium">{approved ? "Approved" : "Not Approved"}</p>;
    //     },
    // },
    // {
    //     accessorKey: "allowedcreditapi",
    //     //  header: "Credit Api",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="Credit Api" />,

    //     cell: ({ row }) => {
    //         const creditapi = row.getValue("allowedcreditapi");
    //         return (
    //             <p className="">
    //                 {creditapi ? (
    //                     <Button variant="destructive" size="sm">
    //                         Allowed
    //                     </Button>
    //                 ) : (
    //                     <Button
    //                         variant="ghost"
    //                         size="sm"
    //                         className="bg-gray-100 py-1 dark:bg-background"
    //                     >
    //                         Not Allowed
    //                     </Button>
    //                 )}
    //             </p>
    //         );
    //     },
    // },
    // {
    //     accessorKey: "isactive",
    //     // header: "Status",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,

    //     cell: ({ row }) => {
    //         const active = row.getValue("isactive");
    //         return (
    //             <p
    //                 className={`flex items-center justify-center rounded-sm border px-1 py-0.5 text-xs ${
    //                     active ? "border-green-500 text-green-500" : "border-red-500 text-red-500"
    //                 }`}
    //             >
    //                 {active ? "Active" : "Inactive"}
    //             </p>
    //         );
    //     },
    // },

    {
        accessorKey: "createddate",
        header: "Created At",
        cell: ({ row }) => {
            const date = new Date(row.getValue("createddate"));
            return <div>{format(date, "PPP")}</div>;
        },
    },
    {
        id: "actions",
        enableHiding: false,
        header: "Action",
        cell: ({ row }) => {
            const campaign = row.original;
            return (
                <div className="flex items-center gap-2">
                    {campaign.isapproved ? (
                        <>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            variant="icon"
                                            // onClick={() => viewCampaign && viewCampaign(campaign)}
                                        >
                                            <EyeIcon className="h-4 w-4 text-primary" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>View campaign</TooltipContent>
                                </Tooltip>
                            </TooltipProvider>

                            {/* <Suspense fallback={<div>Loading...</div>}>
                                <EditCampaignDialogLazy campaign={campaign} />
                            </Suspense> */}
                        </>
                    ) : (
                        <Suspense fallback={<div>Loading...</div>}>
                            {/* <ApproveCampaignDialogLazy campaign={campaign} /> */}
                        </Suspense>
                    )}
                </div>
            );
        },
    },
];

export default dashboardCampaignsTableColumns;
