import { createBulkBankAccounts } from "@/api/bank/bankaccountsApi";
import { createBulkCampaigns } from "@/api/campaigns/appsApi";
import { useMutation } from "@tanstack/react-query";

interface BulkUploadParams {
    data: any[];
    onProgress?: (count: number) => void;
}

interface BulkUploadResult {
    successful: number;
    failed: number;
    errors: { record: any; error: any }[];
}

export const useBulkBankAccounts = () => {
    return useMutation<BulkUploadResult, Error, BulkUploadParams>({
        mutationFn: async ({ data, onProgress }) => {
            const result = await createBulkBankAccounts(data, onProgress);
            return result;
        },
    });
};
