import { useState } from "react";
import { SortingState } from "@tanstack/react-table";

interface PaginationState {
    pageIndex: number;
    pageSize: number;
}

interface UseServerSidePaginationProps {
    onPaginationChange: (pageIndex: number, pageSize: number) => void;
    onSortingChange: (sorting: SortingState) => void;
    onGlobalFilterChange: (filter: string) => void;
    initialPageIndex?: number;
    initialPageSize?: number;
}

export function useServerSidePagination({
    onPaginationChange,
    onSortingChange,
    onGlobalFilterChange,
    initialPageIndex = 0,
    initialPageSize = 10,
}: UseServerSidePaginationProps) {
    const [pagination, setPagination] = useState<PaginationState>({
        pageIndex: initialPageIndex,
        pageSize: initialPageSize,
    });
    const [sorting, setSorting] = useState<SortingState>([]);
    const [globalFilter, setGlobalFilter] = useState("");

    const handlePaginationChange = (
        updater: PaginationState | ((state: PaginationState) => PaginationState)
    ) => {
        setPagination((prevState) => {
            const newState = typeof updater === "function" ? updater(prevState) : updater;
            onPaginationChange(newState.pageIndex, newState.pageSize);
            return newState;
        });
    };

    const handleSortingChange = (newSorting: SortingState) => {
        setSorting(newSorting);
        onSortingChange(newSorting);
    };

    const handleGlobalFilterChange = (newFilter: string) => {
        setGlobalFilter(newFilter);
        onGlobalFilterChange(newFilter);
    };

    return {
        pagination,
        sorting,
        globalFilter,
        handlePaginationChange,
        handleSortingChange,
        handleGlobalFilterChange,
    };
}
