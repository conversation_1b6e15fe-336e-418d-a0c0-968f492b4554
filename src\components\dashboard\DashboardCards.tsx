import React from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    DropdownMenu,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    DropdownMenuContent,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { DashboardCardProps } from "@/lib/interfaces";
import { Dot, MoreVertical } from "lucide-react";

const DashboardCard: React.FC<DashboardCardProps> = ({
    icon: Icon,
    iconColor,
    iconBgColor,
    title,
    value,
    selectOptions,
    menuOptions,
    extraStyles,
}) => {
    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                    <div className={`rounded-full p-2 ${iconBgColor}`}>
                        <Icon className={`h-4 w-4 ${iconColor}`} />
                    </div>
                    <CardTitle className="text-sm font-semibold">{title}</CardTitle>
                </div>
                {selectOptions && (
                    <Select>
                        <SelectTrigger>
                            <SelectValue placeholder={selectOptions[0]?.label || "Select"} />
                        </SelectTrigger>
                        <SelectContent>
                            {selectOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                )}
                {menuOptions && (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button size="icon" variant="icon" className="h-6 w-6">
                                <MoreVertical className="h-3.5 w-3.5 text-gray-600 dark:text-white" />
                                <span className="sr-only">More</span>
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            {menuOptions.map((option) => (
                                <React.Fragment key={option.id}>
                                    <DropdownMenuItem onClick={option.action}>
                                        {option.label}
                                    </DropdownMenuItem>
                                    {option.id < menuOptions.length - 1 && (
                                        <DropdownMenuSeparator />
                                    )}
                                </React.Fragment>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                )}
            </CardHeader>
            <CardContent className="w-full">
                <div
                    className={`text-2xl font-bold ${
                        extraStyles
                            ? "rounded-sm bg-gray-100 p-2 text-center text-gray-700 dark:bg-black dark:text-white"
                            : "text-gray-700 dark:text-white"
                    }`}
                >
                    {value}
                </div>
            </CardContent>
        </Card>
    );
};

export default DashboardCard;
