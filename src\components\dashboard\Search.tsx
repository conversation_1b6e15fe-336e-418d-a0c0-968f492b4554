import React from "react";
import { ArrowLeft, ArrowRight, CircleUser, Home, House, Search } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";

import { useAuth } from "@/contexts/AuthContexts";

import ToggleTheme from "../ToggleTheme";
import { canGoBack, goBack, goForward } from "@/helpers/window_helpers";
import { useNavigation } from "react-router-dom";

export default function SearchComponent() {
    const { user, login, logout } = useAuth();

    // console.log('user data', user)
    // const routeToDashboard = () => {
    //     const router = useNavigation();
    // };

    return (
        <header className="fixed left-0 right-0 top-0 z-30 ml-20 flex h-[50px] items-center gap-4 border-b bg-muted/40 bg-white px-6 dark:bg-background">
            <div className="flex w-full flex-1">
                <div className="flex gap-2">
                    <Button
                        variant="icon"
                        className="h-5 w-5 text-muted-foreground"
                        size="icon"
                        // onClick="/dashboard"
                    >
                        <Home className="h-5 w-5 text-muted-foreground" />
                    </Button>
                    <Button
                        variant="icon"
                        className="h-5 w-5 text-muted-foreground"
                        size="icon"
                        onClick={goBack}
                        disabled={!canGoBack}
                    >
                        <ArrowLeft className="h-5 w-5 text-muted-foreground" />
                    </Button>

                    <Button
                        variant="icon"
                        className="h-5 w-5 text-muted-foreground"
                        size="icon"
                        onClick={goForward}
                    >
                        <ArrowRight className="h-5 w-5 text-muted-foreground" />
                    </Button>
                </div>
                {/* <form>
                        <div className="relative flex items-center">
                            
                            <Search className="absolute left-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                type="search"
                                placeholder="Search..."
                                className="w-full appearance-none bg-background pl-8 shadow-none md:w-2/3 lg:w-1/3"
                            />
                        </div>
                    </form> */}
            </div>
            <div className="flex items-center gap-x-4">
                {/* <NotificationIcon notificationCount={notificationCount} /> */}

                <ToggleTheme />
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="secondary" size="icon" className="rounded-full bg-primary">
                            <CircleUser className="h-5 w-5 text-white" />
                            <span className="sr-only">Toggle user menu</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel>My Account</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>Settings</DropdownMenuItem>
                        <DropdownMenuItem>Support</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={logout}>Logout</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </header>
    );
}
