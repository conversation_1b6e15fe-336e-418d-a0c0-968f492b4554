import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useAuth } from "@/contexts/AuthContexts";
import redde from "@/assets/images/redde-logo.svg";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";

const formSchema = z.object({
    email: z.string().email(),
    password: z.string().min(6),
    macaddress: z.string(),
});

function Login() {
    const { login, macAddress, user } = useAuth();
    const { toast } = useToast();
    const navigate = useNavigate();

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: "",
            password: "",
            macaddress: macAddress || "",
        },
    });

    useEffect(() => {
        form.setValue("macaddress", macAddress);
        // if (user) navigate("/dashboard");
    }, [macAddress]);

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            await login(values);
            toast({ title: "Success", description: "Login successful" });
            navigate("/dashboard");
        } catch (error: any) {
            const errorstatus = error?.response?.status;
            switch (errorstatus) {
                case 401:
                    toast({
                        title: `${error.response.data.status}`,
                        description: `${error.response.data.message}`,
                    });
                    break;
                default:
                    toast({ title: "Error", description: "Login failed" });
            }
        }
    };

    // React Query provides the isLoading state automatically through your useAuth hook.
    const isLoading = form.formState.isSubmitting;

    return (
        <div>
            <Card className="w-[400px] rounded-lg p-4">
                <CardContent>
                    <div className="flex w-full flex-col items-center justify-center">
                        <img src={redde} alt="redde logo" />
                        <span className="font-extrabold text-gray-700 dark:text-white">
                            Sign into your account!!
                        </span>
                    </div>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Email</FormLabel>
                                        <Input type="email" {...field} placeholder="Email" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="password"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Password</FormLabel>
                                        <Input type="password" {...field} placeholder="Password" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <Button
                                type="submit"
                                size="default"
                                className="w-full"
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <span className="flex items-center justify-center">
                                        <svg
                                            className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v8H4z"
                                            ></path>
                                        </svg>
                                        Loading...
                                    </span>
                                ) : (
                                    "Submit"
                                )}
                            </Button>
                        </form>
                    </Form>
                </CardContent>
            </Card>
        </div>
    );
}

export default React.memo(Login);
