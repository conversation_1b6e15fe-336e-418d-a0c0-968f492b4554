import { useState } from "react";
import { useApiStatus } from "@/api/hooks/useApiStatus";
import { PENDING, SUCCESS, ERROR } from "@/api/constants/apiStatus";
import { UseApiConfig } from "@/lib/interfaces";

type ApiFunction<T = unknown> = (...args: unknown[]) => T | Promise<T>;

/**
 * Custom hook for managing API requests and state.
 * @param fn - The function making the API request.
 * @param config - Configuration options including initial data.
 * @returns An object containing the API response data, status, and error.
 */
export function useApi<TData = unknown, TError = unknown>(
    fn: ApiFunction<TData>,
    config: UseApiConfig<TData> = {}
) {
    const { initialData } = config;
    const [data, setData] = useState<TData | undefined>(initialData);
    const [error, setError] = useState<TError | unknown>();
    const { status, setStatus, ...normalizedStatuses } = useApiStatus();

    /**
     * Executes the API function and manages the state transitions.
     * @param args - Arguments to pass to the API function.
     */
    const exec = async <A>(...args: A[]) => {
        try {
            setStatus(PENDING);
            const data = await fn(...args);
            setData(data);
            setStatus(SUCCESS);
            return { data, error: null };
        } catch (error) {
            setError(error);
            setStatus(ERROR);
            return { error, data: null };
        }
    };

    return {
        data,
        setData,
        status,
        setStatus,
        error,
        exec,
        ...normalizedStatuses,
    };
}
