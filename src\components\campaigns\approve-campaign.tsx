import React from "react";
import { format } from "date-fns";
import { formatCurrency } from "@/lib/utils";
import {
    ArrowDown,
    ArrowUp,
    ReceiptCent,
    EyeIcon,
    PlusCircleIcon,
    PlusIcon,
    SendHorizonalIcon,
} from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import AddCampaignForm from "./new-campain-form";
import { useApproveCampaign } from "@/hooks/campaignHooks/use-approve-campaign";
import { useToast } from "../ui/use-toast";

interface TransactionDetailsDialogProps {
    campaign: any; // Replace 'any' with the actual type of your user object
}

const ApproveCampaignDialog: React.FC<TransactionDetailsDialogProps> = ({ campaign }) => {
    const [open, setOpen] = React.useState(false);
    const { approveCampaignMutation, isLoading, isError, error, isSuccess } = useApproveCampaign();

    const { toast } = useToast();

    const handleApproveCampaign = async (event: React.MouseEvent) => {
        event.preventDefault();

        try {
            await approveCampaignMutation(campaign?.id);
            await toast({
                title: "Success",
                description: "Campaign approved successfully",
            });
            setOpen(false); // Close dialog on success
        } catch (error) {
            // console.error("Failed to approve campaign:", error);
            await toast({
                title: `Error (${error || "unknown"})`,
                description: "Failed to approve campaign",
                variant: "destructive",
            });
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <SendHorizonalIcon className="h-4 w-4 bg-gray-100 text-primary dark:bg-background" />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">
                            Approve Campaign
                        </DialogTitle>
                        <DialogDescription className="">
                            Approve campaign for{" "}
                            <span className="font-bold uppercase text-primary">
                                {campaign?.senderid}
                            </span>
                        </DialogDescription>
                    </DialogHeader>
                </DialogHeader>

                {isError && (
                    <div className="mb-4 text-sm text-red-500">
                        Failed to approve campaign: {(error as Error)?.message}
                    </div>
                )}

                {isSuccess && (
                    <div className="mb-4 text-sm text-green-500">
                        Campaign approved successfully!
                    </div>
                )}

                <Button
                    className="w-full"
                    variant="default"
                    color="primary"
                    onClick={handleApproveCampaign}
                    disabled={isLoading}
                >
                    {isLoading ? (
                        <div className="flex items-center gap-2">
                            <span className="animate-spin">⏳</span>
                            Approving...
                        </div>
                    ) : (
                        "Approve"
                    )}
                </Button>
            </DialogContent>
        </Dialog>
    );
};

export default ApproveCampaignDialog;
