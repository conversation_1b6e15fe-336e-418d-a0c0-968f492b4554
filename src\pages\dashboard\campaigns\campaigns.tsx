// AddUser.js
import React, { useState } from "react";

import ServerSideCampaignsTable from "@/components/campaigns/campaigns-table/server-side-table";
import { useFetchCampaigns } from "@/hooks/campaignHooks/use-fetch-campaigns";
import _ from "lodash";
import { ServerSideDataTable } from "@/components/ui/server-side-data-table";
import dashboardCampaignsTableColumns from "./columns";
import { DataTable } from "@/components/ui/data-table";
import { Link, NavLink } from "react-router-dom";
import { DataTableShort } from "@/components/ui/data-tables-short";

const DashboardCampaigns: React.FC = () => {
    const {
        data,
        pagination,
        keyword,
        sorting,
        isLoading,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
    } = useFetchCampaigns();

    const approvedCampaigns = _.filter(data?.content, { isapproved: true });

    // const unapprovedCampaigns = _.filter(data?.content, { isapproved: false });

    //: TODO: Add filter to the table
    //: TODO: Add a  view campaign details page
    //: TODO: Add a modal to edit a campaign
    const columns = dashboardCampaignsTableColumns();

    // console.log("campaigns", approvedCampaigns);

    return (
        <div className="">
            {/* <ServerSideCampaignsTable
                data={approvedCampaigns}
                title="Campaigns"
                loading={isLoading}
                filterable={false}
                pagination={pagination}
                pageCount={data?.totalPages || 0}
                keyword={keyword}
                sorting={sorting}
                onPaginationChange={onPaginationChange}
                onKeywordChange={onKeywordChange}
                onSortingChange={onSortingChange}
            /> */}

            {/* <ServerSideDataTable
                columns={columns}
                data={approvedCampaigns || []}
                title="Campaigns"
                loading={isLoading}
                pagination={pagination}
                pageCount={data?.totalPages || 0}
                sorting={sorting}
                onPaginationChange={onPaginationChange}
                onSortingChange={onSortingChange}
                globalFilter={keyword}
                onGlobalFilterChange={onKeywordChange}
                // manual
            /> */}

            <NavLink to="/redde-actions">
                <DataTableShort
                    columns={columns}
                    data={approvedCampaigns || []}
                    title="Approved campaigns"
                    filterKey="accountId"
                />
            </NavLink>
        </div>
    );
};

export default DashboardCampaigns;
