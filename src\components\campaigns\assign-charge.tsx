import React from "react";
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

import AssignChargeForm from "./assign";

const AssignCharge: React.FC<{ campaign: any }> = ({ campaign }) => {
    return (
        <AssignChargeForm
            campaign={campaign}
            onClose={() => {
                document
                    .querySelector('[data-state="open"] [data-close]')
                    ?.dispatchEvent(new MouseEvent("click"));
            }}
        />
    );
};

export default AssignCharge;
