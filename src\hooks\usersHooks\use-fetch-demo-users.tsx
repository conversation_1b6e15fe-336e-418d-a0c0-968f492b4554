import { useState } from "react";
import { fetchAllDemoUsers, fetchAllUsers } from "@/api/user/authApi";
import { useQuery } from "@tanstack/react-query";
import { UsersApiResponse } from "@/lib/interfaces";

/**
 * A custom hook for fetching and managing demo users data.
 *
 * @param {number} initialPageSize - The initial number of items per page. Default is 10.
 * @returns {Object} An object containing:
 *   - data: The fetched demo users data.
 *   - pagination: Current pagination state.
 *   - keyword: The current search keyword.
 *   - isLoading: Boolean indicating if data is being loaded.
 *   - isError: <PERSON>olean indicating if an error occurred.
 *   - error: Any error that occurred during the fetch.
 *   - onPaginationChange: Function to update pagination.
 *   - onKeywordChange: Function to update the search keyword.
 *   - refetch: Function to refetch the data.
 */
export const useFetchDemoUsers = (initialPageSize: number = 10) => {
    // State for pagination
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });

    // State for search keyword
    const [keyword, setKeyword] = useState("");

    // Fetch demo users data using react-query
    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        queryKey: ["demo-users", pagination.pageIndex, pagination.pageSize],
        queryFn: () => fetchAllDemoUsers(pagination.pageIndex, pagination.pageSize, keyword),
    });

    /**
     * Updates the pagination state.
     * @param {Object} newPagination - The new pagination state.
     */
    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    /**
     * Updates the search keyword state.
     * @param {string} newKeyword - The new search keyword.
     */
    const onKeywordChange = (newKeyword: string) => {
        setKeyword(newKeyword);
    };

    // Return an object with all necessary data and functions
    return {
        data: data?.data,
        pagination,
        keyword,
        isLoading,
        isError,
        error,
        onPaginationChange,
        onKeywordChange,
        refetch,
    };
};
