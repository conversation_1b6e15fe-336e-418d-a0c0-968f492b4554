import React, { useState } from "react";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import clsx from "clsx";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const tabs = [
    { name: "Confirmed Balances", href: "#", current: true },
    { name: "Unconfirmed Balances", href: "#", current: false },
    { name: "Pending Settlements", href: "#", current: false },
    { name: "Transaction Balances", href: "#", current: false },
    { name: "Brands Balances", href: "#", current: false },
];

interface SummaryTabsProps {
    tabContents: {
        [key: string]: React.ReactNode;
    };
}

export const SummaryTabs: React.FC<SummaryTabsProps> = React.memo(({ tabContents }) => {
    const [currentTab, setCurrentTab] = useState(tabs[0].name);

    return (
        <div>
            <Tabs defaultValue={currentTab} className="flex h-screen">
                {/* <div className=""> */}
                <div className="h-screen bg-white dark:bg-card">
                    <TabsList className="flex flex-col items-start">
                        {tabs.map((tab) => (
                            <TabsTrigger
                                key={tab.name}
                                value={tab.name}
                                className={clsx(
                                    currentTab === tab.name
                                        ? "rounded-none border-red-500 text-red-600"
                                        : "dar:hover:text-gray-100 border-transparent text-gray-500 hover:text-gray-700 dark:text-white",
                                    "my-1.5 whitespace-nowrap border-b-2 py-2.5 text-sm font-medium"
                                )}
                                aria-current={currentTab === tab.name ? "page" : undefined}
                                onClick={() => setCurrentTab(tab.name)}
                            >
                                {tab.name}
                            </TabsTrigger>
                        ))}
                    </TabsList>
                </div>

                {/* </div> */}
                <div className="ml-4 w-full">
                    {tabs.map((tab) => (
                        <TabsContent key={tab.name} value={tab.name}>
                            {tabContents[tab.name]}
                        </TabsContent>
                    ))}
                </div>
            </Tabs>
        </div>
    );
});
