import { fetchAllReviewTransactions, fetchFailedTransactions } from "@/api/accounts/accountsApi";
import { useMutation } from "@tanstack/react-query";

export interface FailedTransactionFilter {
    key: string;
    value: any;
    value2?: any;
}

export type TransactionResponseField = {
    accountid: number;
    actualamount: number;
    amount: number;
    balance: number;
    brand: string;
    brandgroup: string;
    brandinvoice: string;
    brandtransid: string;
    callbacksuccess: boolean;
    campaignid: number;
    channel: string;
    charge: number;
    clientreference: string;
    clienttransid: string;
    description: string;
    fromui: boolean;
    id: number;
    isavailable: boolean;
    issettled: boolean;
    legacyid: string;
    legacyusername: string;
    nickname: string;
    queueid: number;
    settleddate: string;
    status: string;
    statusreason: string;
    statusupdatedate: string;
    transactiondate: string;
    transday: string;
    transtype: string;
};

/**
 * A hook that wraps the createCampaign function in a mutation for use with react-query.
 *
 * This hook returns a mutation result object from react-query, containing the mutation function and related states.
 */
export const useFetchFailedTransactions = (accountId: number) => {
    const { mutate, data, isPending, isError, error, isSuccess } = useMutation({
        mutationFn: (filters: FailedTransactionFilter[]) =>
            fetchFailedTransactions(accountId, filters),
    });

    return {
        mutate,
        data,
        isPending,
        isError,
        error,
        isSuccess,
    };
};
