import React from "react";
import {
    <PERSON><PERSON>,
    <PERSON>alogContent,
    DialogDescription,
    <PERSON>alogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CirclePlay } from "lucide-react";
import SimulateChargeForm from "./simulate-charge-form";
import AddTransactionForm from "./add-transaction-form";

// interface SimulateChargeDialogProps {
//     accountId: number;
// }

const AddTransaction: React.FC<{ campaignId: number }> = ({ campaignId }) => {
    return <AddTransactionForm campaignId={campaignId} />;
};

export default AddTransaction;
