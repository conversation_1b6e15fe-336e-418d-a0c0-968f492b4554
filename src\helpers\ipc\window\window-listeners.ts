import address from "address";
import { <PERSON><PERSON>erWindow, ipc<PERSON><PERSON> } from "electron";
import * as <PERSON><PERSON><PERSON> from "xlsx";
import { readFile } from "fs/promises";
import {
    GET_MAC_ADDRESS,
    GO_BACK,
    WIN_CLOSE_CHANNEL,
    WIN_MAXIMIZE_CHANNEL,
    WIN_MINIMIZE_CHANNEL,
    GO_FORWARD,
    CAN_GO_BACK,
    CAN_GO_FORWARD,
    READ_EXCEL_SHEET,
} from "./window-channels";

export function addWindowEventListeners(mainWindow: BrowserWindow) {
    ipcMain.handle(GET_MAC_ADDRESS, async () => {
        //   return new Promise<string>((resolve, reject) => {
        //       address.mac((err, macAddress) => {
        //           if (err) {
        //               console.error("Error fetching MAC address:", err);
        //               reject("Error");
        //           } else {
        //               resolve(macAddress);
        //           }
        //       });
        //   });

        try {
            const macAddress = await new Promise<string>((resolve, reject) => {
                address.mac((err, mac) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(mac);
                    }
                });
            });
            return macAddress;
        } catch (error) {
            console.error("Error fetching MAC address:", error);
            throw error;
        }
    });

    ipcMain.handle(WIN_MINIMIZE_CHANNEL, () => {
        mainWindow.minimize();
    });
    ipcMain.handle(WIN_MAXIMIZE_CHANNEL, () => {
        if (mainWindow.isMaximized()) {
            mainWindow.unmaximize();
        } else {
            mainWindow.maximize();
        }
    });
    ipcMain.handle(WIN_CLOSE_CHANNEL, () => {
        mainWindow.close();
    });

    ipcMain.handle(GO_BACK, () => {
        mainWindow.webContents.goBack();
        // mainWindow.webContents.navigationHistory
        // console.log("navigations history ----", navigationHistory);
        // navigationHistory.go
    });

    // handle can go back history
    ipcMain.handle(CAN_GO_BACK, () => {
        mainWindow.webContents.canGoBack();
    });

    // handles can go forward history
    ipcMain.handle(CAN_GO_FORWARD, () => {
        mainWindow.webContents.canGoForward();
    });

    // handles go back history;
    ipcMain.handle(GO_FORWARD, () => {
        mainWindow.webContents.goForward();
    });

    ipcMain.handle(READ_EXCEL_SHEET, async (_, filePath) => {
        try {
            const buffer = await readFile(filePath);
            const workbook = XLSX.read(buffer, { type: "buffer" });
            const sheetName = workbook.SheetNames[0];
            const sheet = workbook.Sheets[sheetName];
            return XLSX.utils.sheet_to_json(sheet, { header: 1 });
        } catch (error) {
            console.error("Error reading Excel file:", error);
            throw error;
        }
    });
}
