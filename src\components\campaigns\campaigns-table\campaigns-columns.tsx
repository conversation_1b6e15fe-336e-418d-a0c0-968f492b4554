import React, { Suspense, useEffect, useRef, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import {
    EyeIcon,
    CopyIcon,
    CheckIcon,
    XIcon,
    MoreHorizontal,
    EllipsisVertical,
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { CampaignsProps } from "./server-side-table";
import { isNil } from "es-toolkit";
import { More } from "iconsax-react";

interface CampaignTableColumnsProps {
    viewCampaign?: (campaign: CampaignsProps) => void;
    ApproveCampaignDialogLazy: React.ComponentType<{ campaign: CampaignsProps }>;
    EditCampaignDialogLazy: React.ComponentType<{ campaign: CampaignsProps }>;
    AssignChargeLazy: React.ComponentType<{ campaign: CampaignsProps }>;
    SimulateChargeDialogLazy: React.ComponentType<{ accountId: number }>;
    AddTransactionDialog: React.ComponentType<{ campaignId: number }>;
}
export const campaignsTableColumns = ({
    viewCampaign,
    ApproveCampaignDialogLazy,
    EditCampaignDialogLazy,
    SimulateChargeDialogLazy,
    AssignChargeLazy,
    AddTransactionDialog,
}: CampaignTableColumnsProps): ColumnDef<CampaignsProps>[] => [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title="App ID" />,
        cell: ({ row }) => <div className="w-[50px]">{row.getValue("id")}</div>,
        enableSorting: true,
        enableHiding: false,
    },
    {
        accessorKey: "accountid",
        //  header: "Account ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,

        cell: ({ row }) => <div className="lowercase">{row.getValue("accountid")}</div>,
    },
    {
        accessorKey: "legacyusername",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Legacy Username" />,

        cell: ({ row }) => (
            <div className="lowercase">
                {isNil(row.getValue("legacyusername")) ? (
                    <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                        N/A
                    </span>
                ) : (
                    row.getValue("legacyusername")
                )}
            </div>
        ),
    },

    {
        accessorKey: "description",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Description" />,

        cell: ({ row }) => (
            <div className="capitalize">
                {isNil(row.getValue("description")) ? (
                    <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                        N/A
                    </span>
                ) : (
                    row.getValue("description")
                )}
            </div>
        ),
    },

    {
        accessorKey: "apikey",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Api key" />,
        cell: ({ row }) => {
            const apiKey = row.getValue("apikey");
            return (
                <div className="flex items-center gap-2">
                    {isNil(apiKey) ? (
                        <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                            N/A
                        </span>
                    ) : (
                        <>
                            <span>{String(apiKey).slice(0, 5)}...</span>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0"
                                            onClick={() =>
                                                navigator.clipboard.writeText(String(apiKey))
                                            }
                                        >
                                            <CopyIcon className="h-3 w-3 cursor-pointer" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>Copy API key</TooltipContent>
                                </Tooltip>
                                <Tooltip>
                                    <TooltipTrigger>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0"
                                            onClick={() => {
                                                alert(apiKey); // You might want to replace this with a modal
                                            }}
                                        >
                                            <EyeIcon className="h-3 w-3 cursor-pointer" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>View full API key</TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </>
                    )}
                </div>
            );
        },
    },

    {
        accessorKey: "callbackurl",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Callback Url" />,
        cell: ({ row }) => {
            const callbackUrl = row.getValue("callbackurl");
            return (
                <div className="flex items-center gap-2">
                    {isNil(callbackUrl) ? (
                        <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                            N/A
                        </span>
                    ) : (
                        <>
                            <span>{String(callbackUrl).slice(0, 5)}...</span>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0"
                                            onClick={() =>
                                                navigator.clipboard.writeText(String(callbackUrl))
                                            }
                                        >
                                            <CopyIcon className="h-3 w-3 cursor-pointer" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>Copy Callback url</TooltipContent>
                                </Tooltip>
                                <Tooltip>
                                    <TooltipTrigger>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0"
                                            onClick={() => {
                                                alert(callbackUrl); // You might want to replace this with a modal
                                            }}
                                        >
                                            <EyeIcon className="h-3 w-3 cursor-pointer" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>View full Callback Url</TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </>
                    )}
                </div>
            );
        },
    },

    {
        accessorKey: "senderid",
        // header: "Sender ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Sender ID" />,

        cell: ({ row }) => (
            <div className="uppercase">
                {isNil(row.getValue("senderid")) ? (
                    <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                        N/A
                    </span>
                ) : (
                    row.getValue("senderid")
                )}
            </div>
        ),
    },

    {
        accessorKey: "itcmerchantid",
        //  header: "Merchant ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Merchant ID" />,

        cell: ({ row }) => {
            const merchantId = row.getValue("itcmerchantid");
            return (
                <div className="flex items-center justify-center gap-2">
                    {isNil(merchantId) ? (
                        <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                            N/A
                        </span>
                    ) : (
                        <>
                            <span>{String(merchantId).slice(0, 5)}...</span>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0"
                                            onClick={() =>
                                                navigator.clipboard.writeText(String(merchantId))
                                            }
                                        >
                                            <CopyIcon className="h-3 w-3 cursor-pointer" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>Copy merchant ID</TooltipContent>
                                </Tooltip>
                                <Tooltip>
                                    <TooltipTrigger>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0"
                                            onClick={() => {
                                                alert(merchantId); // You might want to replace this with a modal
                                            }}
                                        >
                                            <EyeIcon className="h-3 w-3 cursor-pointer" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>View full merchant ID</TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </>
                    )}
                </div>
            );
        },
    },
    {
        accessorKey: "itcapikey",
        header: ({ column }) => <DataTableColumnHeader column={column} title="ITC Api key" />,
        cell: ({ row }) => {
            const apiKey = row.getValue("itcapikey");
            return (
                <div className="flex items-center gap-2">
                    {isNil(apiKey) ? (
                        <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                            N/A
                        </span>
                    ) : (
                        <>
                            <span>{String(apiKey).slice(0, 5)}...</span>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0"
                                            onClick={() =>
                                                navigator.clipboard.writeText(String(apiKey))
                                            }
                                        >
                                            <CopyIcon className="h-3 w-3 cursor-pointer" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>Copy API key</TooltipContent>
                                </Tooltip>
                                <Tooltip>
                                    <TooltipTrigger>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0"
                                            onClick={() => {
                                                alert(apiKey); // You might want to replace this with a modal
                                            }}
                                        >
                                            <EyeIcon className="h-3 w-3 cursor-pointer" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>View full API key</TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </>
                    )}
                </div>
            );
        },
    },
    {
        accessorKey: "isapproved",
        // header: "Approved",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Approved" />,

        cell: ({ row }) => {
            // const approved = row.getValue("isapproved");
            const campaign = row.original;

            return (
                <p className="flex items-center justify-center font-medium">
                    {campaign.isapproved ? (
                        // <div className="flex items-center justify-center w-10 h-10 bg-green-500 rounded-full">
                        <Button
                            variant="outline"
                            size="icon"
                            className="h-6 w-6 rounded-full border-green-500"
                        >
                            <CheckIcon className="h-4 w-4 text-green-500" />
                        </Button>
                    ) : (
                        // </div>
                        <>
                            {
                                <Suspense fallback={<div>Loading...</div>}>
                                    <ApproveCampaignDialogLazy campaign={campaign} />
                                </Suspense>
                            }
                        </>
                    )}
                </p>
            );
        },
    },
    {
        accessorKey: "allowedcreditapi",
        //  header: "Credit Api",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Credit Api" />,

        cell: ({ row }) => {
            const creditapi = row.getValue("allowedcreditapi");
            return (
                <p className="">
                    {creditapi ? (
                        <span className="flex items-center gap-1">
                            <CheckIcon className="h-4 w-4 text-green-500" />
                            <span>Allowed</span>
                        </span>
                    ) : (
                        <span className="flex items-center gap-1">
                            <XIcon className="h-4 w-4 text-primary" />
                            <span>Not Allowed</span>
                        </span>
                    )}
                </p>
            );
        },
    },
    {
        accessorKey: "isactive",
        // header: "Status",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,

        cell: ({ row }) => {
            const active = row.getValue("isactive");
            return (
                <p
                    className={`flex items-center justify-center rounded-sm border px-1 py-0.5 text-xs ${
                        active ? "border-green-500 text-green-500" : "border-red-500 text-red-500"
                    }`}
                >
                    {active ? "Active" : "Inactive"}
                </p>
            );
        },
    },

    {
        id: "actions",
        enableHiding: false,
        header: "Action",
        cell: ({ row }) => {
            const campaign = row.original;
            const [showActions, setShowActions] = useState(false);
            const actionsRef = useRef<HTMLDivElement>(null);

            const accountId = campaign?.id;
            return (
                <div className="relative flex items-center" ref={actionsRef}>
                    {/* More Icon Button */}
                    {/* <button
                        onClick={() => setShowActions(!showActions)}
                        className={`transition-transform duration-300 ${
                            showActions ? "translate-x-2" : ""
                        }`}
                    >
                        <EllipsisVertical className="h-5 w-5 cursor-pointer" />
                    </button> */}

                    {/* Actions Container */}
                    {/* {showActions && ( */}
                    <div className="flex items-center gap-2">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        variant="icon"
                                        onClick={() => viewCampaign && viewCampaign(campaign)}
                                        className="cursor-pointer"
                                    >
                                        <EyeIcon className="h-4 w-4 text-primary" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View campaign</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Suspense fallback={<div>Loading...</div>}>
                                        <EditCampaignDialogLazy campaign={campaign} />
                                    </Suspense>
                                </TooltipTrigger>
                                <TooltipContent>Edit campaign</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Suspense fallback={<div>Loading...</div>}>
                                        <SimulateChargeDialogLazy
                                            accountId={Number(campaign?.id)}
                                        />
                                    </Suspense>
                                </TooltipTrigger>
                                <TooltipContent>Simulate Charge</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Suspense fallback={<div>Loading...</div>}>
                                        <AssignChargeLazy campaign={campaign} />
                                    </Suspense>
                                </TooltipTrigger>
                                <TooltipContent>Assign Charge</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Suspense fallback={<div>Loading...</div>}>
                                        <AddTransactionDialog campaignId={Number(campaign?.id)} />
                                    </Suspense>
                                </TooltipTrigger>
                                <TooltipContent>Add Transaction</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    {/* )} */}
                </div>
            );
        },
    },
];

export default campaignsTableColumns;
