import React from 'react'
import { Skeleton } from "@/components/ui/skeleton";


type Props = {}

const SkeletonComponent = (props: Props) => {
    
    return (
        <div className="mx-0 h-screen w-full">
            <main className="flex flex-1 flex-col pb-4">
                <div className="grid grid-cols-3 gap-6">
                    <Skeleton className="dark:bg-gray-600 h-24 w-full bg-gray-200" />
                    <Skeleton className="dark:bg-gray-600 h-24 w-full bg-gray-200" />
                    <Skeleton className="dark:bg-gray-600 h-24 w-full bg-gray-200" />
                </div>
                <div className="mt-8">
                    <Skeleton className="dark:bg-gray-600 mb-4 h-96 w-full bg-gray-200" />   
                </div>
            </main>
        </div>
    ); 
}
export default SkeletonComponent