import { useState } from "react";
/**
 * Interface representing the state of pagination.
 */
export interface PaginationState {
    /** The current page index (zero-based). */
    pageIndex: number;
    /** The number of items per page. */
    pageSize: number;
}

/**
 * A custom hook for managing pagination state.
 *
 * @param {number} initialSize - The initial page size. Defaults to 50.
 * @returns {Object} An object containing:
 *   - pagination: The current pagination state.
 *   - onPaginationChange: A function to update the pagination state.
 */
export function usePagination(initialSize = 50) {
    const [pagination, setPagination] = useState<PaginationState>({
        pageSize: initialSize,
        pageIndex: 0,
    });

    /**
     * Updates the pagination state.
     *
     * @param {PaginationState} newPagination - The new pagination state to set.
     */
    const onPaginationChange = (newPagination: PaginationState) => {
        setPagination(newPagination);
    };

    return {
        pagination,
        onPaginationChange,
    };
}
