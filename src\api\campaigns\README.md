# Campaigns and Apps API Interaction Documentation

This module provides functions for interacting with campaigns and apps associated with user accounts in the system. It includes functions to fetch apps, manage campaigns, and perform various campaign-related actions like approval and search.

## Table of Contents

- [fetchApps](#fetchapps)
- [fetchAllCampaigns](#fetchallcampaigns)
- [approveCampaign](#approvecampaign)
- [viewCampaign](#viewcampaign)

---

### `fetchApps`

Fetches a list of apps associated with a given account ID. This function sends a GET request to the API to retrieve the apps, with support for additional query parameters.

#### Parameters
- `accountId: IAccountIdParams`  
  The ID of the account whose apps are to be fetched.

- `params: any`  
  Additional parameters to be included with the request (e.g., filters).

#### Returns
- `Promise<any>`  
  A promise that resolves to the API response data containing the apps.

#### Example Usage
```typescript
const accountId = { id: "12345" };
const params = { platform: "iOS" };

const apps = await fetchApps(accountId, params);
console.log(apps);  // Logs the list of apps associated with the account
```

---

### `fetchAllCampaigns`

Fetches all campaigns with optional pagination and search functionality. The campaigns can be filtered using search keywords, and the results are paginated.

#### Parameters
- `page: number`  
  The page number for pagination.

- `pageSize: number`  
  The number of items per page.

- `keyword?: string`  
  (Optional) The search keyword to filter campaigns.

#### Returns
- `Promise<any>`  
  A promise that resolves to the API response data containing the campaigns.

#### Example Usage
```typescript
const campaigns = await fetchAllCampaigns(1, 10, "summer sale");
console.log(campaigns);  // Logs the list of campaigns matching the search criteria
```

---

### `approveCampaign`

Approves a campaign by its ID. This function sends a GET request to the API to approve the campaign, updating its status to approved.

#### Parameters
- `campainId: number`  
  The ID of the campaign to be approved.

#### Returns
- `Promise<any>`  
  A promise that resolves to the API response indicating the approval status.

#### Example Usage
```typescript
const approvalStatus = await approveCampaign(123);
console.log(approvalStatus);  // Logs the response indicating the campaign approval status
```

---

### `viewCampaign`

Fetches the details of a specific campaign by its ID. This function sends a GET request to the API to retrieve the campaign's details.

#### Parameters
- `campainId: number`  
  The ID of the campaign to be fetched.

#### Returns
- `Promise<any>`  
  A promise that resolves to the API response data containing the campaign details.

#### Example Usage
```typescript
const campaignDetails = await viewCampaign(123);
console.log(campaignDetails);  // Logs the detailed information about the campaign
```

---

### Notes
- The `accountId` parameter used in `fetchApps` is structured according to the `IAccountIdParams` interface, typically containing a unique identifier for the account.
- The `params` parameter in `fetchApps` allows flexibility in filtering the apps by different criteria, such as platform or status.
- The `approveCampaign` function simplifies the approval process for campaigns, enabling users to update the status with a single API call.