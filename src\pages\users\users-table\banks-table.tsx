import React, { Suspense, useCallback, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ServerSideDataTable } from "@/components/ui/server-side-data-table";
import { Input } from "@/components/ui/input";
import { debounce } from "lodash";
import { TooltipProvider, TooltipTrigger, Tooltip, TooltipContent } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { EyeIcon } from "lucide-react";
import AddBankAccountDialog from "@/components/banks/add-bank-account-dialog";
import { useAuth } from "@/contexts/AuthContexts";
import EditBankAccount from "@/components/banks/EditBankAccount";

export interface IBankProps {
    accountid: number;
    accountname: string;
    createdby: number;
    createddate: string;
    sortcode: string;
    swiftcode: string;
    bankname: string;
    branch: string;
    updateddate: string;
    accountnumber: string;
}

// type walletProps = { data: any; filterable?: boolean; title?: string };
type bankProps = {
    // columns: ColumnDef<any, any>[];
    data: any;
    filterable?: boolean;
    isDemo?: boolean;
    bulk?: string;
    title?: string;
    accountId?: number;
    loading?: boolean;
    pagination?: any;
    pageCount?: number;
    keyword?: string;
    onPaginationChange?: (newPagination: any) => void;
    onKeywordChange?: (newKeyword: string) => void;
    sorting?: any;

    onSortingChange: (newSorting: any) => void;
};

export default function BankAccountsTable({
    data,
    filterable = true,
    title,
    loading,
    pagination,
    pageCount,
    accountId,
    sorting,

    onPaginationChange,
    onKeywordChange,
    onSortingChange,
}: bankProps) {
    const [searchTerm, setSearchTerm] = useState("");

    const { user } = useAuth();
    console.log("accountId", accountId);

    const debouncedSearch = useCallback(
        debounce((value: string) => {
            onKeywordChange(value);
        }, 500),
        [onKeywordChange]
    );

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        debouncedSearch(value);
    };

    const columns: ColumnDef<IBankProps>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-[2px]"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-[2px]"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },

        {
            accessorKey: "id",
            header: "ID",
            cell: ({ row }) => <p className="text-xs font-bold">{row.getValue("id")}</p>,
        },

        {
            accessorKey: "accountnumber",
            header: "Account Number",
            cell: ({ row }) => <div className="lowercase">{row.getValue("accountnumber")}</div>,
        },

        {
            accessorKey: "accountid",
            header: "Account ID",
            cell: ({ row }) => <div className="lowercase">{row.getValue("accountid")}</div>,
        },
        {
            accessorKey: "accountname",
            header: "Account Name",
            cell: ({ row }) => <div>{row.getValue("accountname")}</div>,
        },

        {
            accessorKey: "bankname",
            header: "Bank Name",
            cell: ({ row }) => <div className="">{row.getValue("bankname")}</div>,
        },

        {
            accessorKey: "branch",
            header: "Branch",
            cell: ({ row }) => <div className="">{row.getValue("branch")}</div>,
        },
        {
            accessorKey: "sortcode",
            header: "Sort Code",
            cell: ({ row }) => <div className="">{row.getValue("sortcode")}</div>,
        },

        {
            accessorKey: "createdby",
            header: "Created By",
            cell: ({ row }) => <div className="">{row.getValue("createdby")}</div>,
        },
        {
            accessorKey: "createddate",
            header: "Created Date",
            cell: ({ row }) => <div className="">{row.getValue("createddate")}</div>,
        },

        {
            id: "actions",
            enableHiding: false,
            header: "Action",
            cell: ({ row }) => {
                const user = row.original;
                return (
                    <div className="flex items-center gap-2">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button variant="icon">
                                        <EyeIcon className="h-4 w-4 text-primary" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View Bank Account</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <Suspense fallback={<div>Loading...</div>}>
                            <EditBankAccount
                                data={data}
                                accountId={accountId}
                                bankAccountData={user}
                                mode="edit"
                            />
                        </Suspense>
                    </div>
                );
            },
        },
    ];

    return (
        <Card className="mx-auto w-full overflow-y-auto overflow-x-hidden py-8">
            <CardContent>
                {filterable && (
                    <Card className="my-4">
                        <CardHeader>Filter</CardHeader>
                        <CardContent>
                            <div className="my-4 grid w-full grid-cols-2 items-center gap-x-4">
                                <Input
                                    type="text"
                                    placeholder="Search bank accounts..."
                                    value={searchTerm}
                                    onChange={handleSearchChange}
                                />
                                {/* <Button variant="default">Add Bank Account</Button> */}
                                <AddBankAccountDialog user={user} accountId={accountId} />
                            </div>
                        </CardContent>
                    </Card>
                )}
                <ServerSideDataTable
                    columns={columns}
                    data={data || []}
                    title={title ?? ""}
                    loading={loading}
                    pagination={pagination}
                    pageCount={pageCount}
                    sorting={sorting}
                    onSortingChange={onSortingChange}
                    onPaginationChange={onPaginationChange}
                />
            </CardContent>
        </Card>
    );
}
