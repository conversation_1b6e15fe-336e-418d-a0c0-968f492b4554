import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { useNavigate } from "react-router-dom";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useEditUser } from "@/api/hooks/use-edit-user";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { PencilLine } from "lucide-react";
import { log } from "console";

const formSchema = z.object({
    accountid: z.number().optional(),
    createdby: z.number().optional(),
    email: z.string().email("Please enter a valid email").optional(),
    expirydate: z.string().optional(),
    iscompany: z.boolean().optional(),
    iscustomer: z.boolean().optional(),
    isactive: z.boolean().optional(),
    mobilenumber: z.string().optional(),
    name: z.string().min(1, "Name is required"),
    islocked: z.boolean().optional(),
    userid: z.number().optional(),
    profileid: z.number().optional(),
});

function EditUserForm(data: any) {
    // console.log("🚀 ~ EditUserForm ~ data:", data)
    // console.log("🚀 ~ EditUserForm ~ user:", data?.user)
    // console.log("🚀 ~ EditUserForm ~ name:", data?.user?.name)
    // const { login, macAddress, user } = useAuth();
    const [isModalOpen, setIsModalOpen] = useState(false);

    const { toast } = useToast();
    const navigate = useNavigate();
    const editUser = useEditUser();

    // Add console.log to debug the data prop
    // console.log("User data props:", data);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            accountid: data?.user?.accountid,
            createdby: data?.user?.createdby,
            email: data?.user?.email || "",
            expirydate: data?.user?.expirydate,
            iscompany: data?.user?.iscompany || false,
            iscustomer: data?.user?.iscustomer || false,
            isactive: data?.user?.isactive || false,
            mobilenumber: data?.user?.mobilenumber || "",
            name: data?.user?.name || "",
            islocked: data?.user?.islocked || false,
            userid: data?.user?.userid,
            profileid: data?.user?.profileid,
        },
    });

    // Reset form when data changes
    useEffect(() => {
        if (data?.user) {
            form.reset({
                accountid: data?.user?.accountid,
                createdby: data?.user?.createdby,
                email: data?.user?.email || "",
                expirydate: data?.user?.expirydate,
                iscompany: data?.user?.iscompany || false,
                iscustomer: data?.user?.iscustomer || false,
                isactive: data?.user?.isactive || false,
                mobilenumber: data?.user?.mobilenumber || "",
                name: data?.user?.name || "",
                islocked: data?.user?.islocked || false,
                userid: data?.user?.userid,
                profileid: data?.user?.profileid,
            });
        }
    }, [data, form]);

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        console.log("values", values);
        try {
            // pass user id as well
            const result = await editUser.mutateAsync(values);

            toast({
                title: "Success",
                description: "User updated successfully",
            });

            setIsModalOpen(false);
        } catch (error: any) {
            const errorStatus = error?.response?.status;
            const errorMessage =
                error?.response?.data?.message || error.message || "Failed to update user";

            toast({
                title: `Error (${errorStatus || "unknown"})`,
                description: errorMessage,
                variant: "destructive",
            });

            setIsModalOpen(false);
        }
    };

    // React Query provides the isLoading state automatically through your useAuth hook.
    const isLoading = form.formState.isSubmitting;

    return (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <PencilLine className="h-4 w-4 text-green-500" />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">Edit User</DialogTitle>
                    </DialogHeader>
                </DialogHeader>
                <div>
                    <Form {...form}>
                        <form className="space-y-6">
                            {/* Display readonly information */}
                            <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                <div>
                                    <FormLabel className="text-sm font-medium text-gray-600 dark:text-gray-400">User ID</FormLabel>
                                    <p className="text-sm font-semibold">{data?.user?.userid}</p>
                                </div>
                                <div>
                                    <FormLabel className="text-sm font-medium text-gray-600 dark:text-gray-400">Account ID</FormLabel>
                                    <p className="text-sm font-semibold">{data?.user?.accountid}</p>
                                </div>
                                <div>
                                    <FormLabel className="text-sm font-medium text-gray-600 dark:text-gray-400">Created By</FormLabel>
                                    <p className="text-sm font-semibold">{data?.user?.createdby}</p>
                                </div>
                                <div>
                                    <FormLabel className="text-sm font-medium text-gray-600 dark:text-gray-400">Last Login</FormLabel>
                                    <p className="text-sm font-semibold">{data?.user?.lastlogin ? new Date(data.user.lastlogin).toLocaleString() : 'Never'}</p>
                                </div>
                            </div>

                            {/* Basic Information - 2 columns */}
                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">Name</FormLabel>
                                            <Input type="text" {...field} placeholder="Name" />
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="email"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">Email</FormLabel>
                                            <Input type="email" {...field} placeholder="Email" />
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            {/* Contact & Profile - 2 columns */}
                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="mobilenumber"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">
                                                Mobile Number
                                            </FormLabel>
                                            <Input type="tel" {...field} placeholder="Mobile Number" />
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="profileid"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">Profile ID</FormLabel>
                                            <Input
                                                type="number"
                                                {...field}
                                                placeholder="Profile ID"
                                                onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                                            />
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>


                            {/* Status Switches - 2x2 grid */}
                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="iscompany"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-row items-center justify-between p-3 border rounded-lg">
                                            <div className="space-y-0.5">
                                                <FormLabel className="text-sm font-medium">Is Company</FormLabel>
                                            </div>
                                            <FormControl>
                                                <Switch
                                                    checked={field.value}
                                                    onCheckedChange={field.onChange}
                                                />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="iscustomer"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-row items-center justify-between p-3 border rounded-lg">
                                            <div className="space-y-0.5">
                                                <FormLabel className="text-sm font-medium">Is Customer</FormLabel>
                                            </div>
                                            <FormControl>
                                                <Switch
                                                    checked={field.value}
                                                    onCheckedChange={field.onChange}
                                                />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="isactive"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-row items-center justify-between p-3 border rounded-lg">
                                            <div className="space-y-0.5">
                                                <FormLabel className="text-sm font-medium">Is Active</FormLabel>
                                            </div>
                                            <FormControl>
                                                <Switch
                                                    checked={field.value}
                                                    onCheckedChange={field.onChange}
                                                />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="islocked"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-row items-center justify-between p-3 border rounded-lg">
                                            <div className="space-y-0.5">
                                                <FormLabel className="text-sm font-medium">Is Locked</FormLabel>
                                            </div>
                                            <FormControl>
                                                <Switch
                                                    checked={field.value}
                                                    onCheckedChange={field.onChange}
                                                />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                            </div>



                            <Button
                                type="submit"
                                size="default"
                                className="w-full"
                                disabled={isLoading}
                                onClick={form.handleSubmit(onSubmit)}
                            >
                                {isLoading ? (
                                    <span className="flex items-center justify-center">
                                        <svg
                                            className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v8H4z"
                                            ></path>
                                        </svg>
                                        Loading...
                                    </span>
                                ) : (
                                    "Submit"
                                )}
                            </Button>
                        </form>
                    </Form>
                </div>
            </DialogContent>
        </Dialog>
    );
}

export default React.memo(EditUserForm);
