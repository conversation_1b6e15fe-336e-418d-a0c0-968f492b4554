import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { useNavigate } from "react-router-dom";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useEditUser } from "@/api/hooks/use-edit-user";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { PencilLine } from "lucide-react";

const formSchema = z.object({
    accountid: z.number().optional(),
    createdby: z.number().optional(),
    email: z.string().optional(),
    expirydate: z.string().optional(),
    iscompany: z.boolean().optional(),
    mobilenumber: z.string().optional(),
    name: z.string().optional(),
    islocked: z.boolean().optional(),
    password: z.string().optional(),
    userid: z.number().optional(),
});

function EditUserForm(data: any) {
    console.log("🚀 ~ EditUserForm ~ data:", data)
    // const { login, macAddress, user } = useAuth();
    const [isModalOpen, setIsModalOpen] = useState(false);

    const { toast } = useToast();
    const navigate = useNavigate();
    const editUser = useEditUser();

    // Add console.log to debug the data prop
    // console.log("User data props:", data);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            accountid: data?.data?.accountid,
            createdby: data?.data?.createdby,
            email: data?.data?.email,
            expirydate: data?.data?.expirydate,
            iscompany: data?.data?.iscompany,
            mobilenumber: data?.data?.mobilenumber,
            name: data?.data?.name,
            islocked: data?.data?.islocked,
            password: data?.data?.password,
            userid: data?.data?.userid,
        },
    });

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            const result = await editUser.mutateAsync(values);

            toast({
                title: "Success",
                description: "User updated successfully",
            });

            setIsModalOpen(false);
            // navigate("/campaigns");
        } catch (error: any) {
            const errorStatus = error?.response?.status;
            const errorMessage =
                error?.response?.data?.message || error.message || "Failed to update user";

            toast({
                title: `Error (${errorStatus || "unknown"})`,
                description: errorMessage,
                variant: "destructive",
            });

            setIsModalOpen(false);
        }
    };

    // React Query provides the isLoading state automatically through your useAuth hook.
    const isLoading = form.formState.isSubmitting;

    return (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <PencilLine className="h-4 w-4 text-green-500" />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">Edit User</DialogTitle>
                        {/* <DialogDescription className="">Add a new user  </DialogDescription> */}
                    </DialogHeader>
                </DialogHeader>
                <div>
                    <Form {...form}>
                        <form className="space-y-4">
                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Name</FormLabel>
                                        <Input type="text" {...field} placeholder="Name" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Email</FormLabel>
                                        <Input type="text" {...field} placeholder="Email" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="mobilenumber"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Mobile Number
                                        </FormLabel>
                                        <Input type="text" {...field} placeholder="Mobile Number" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="iscompany"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-center justify-between p-4">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">Is Company</FormLabel>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="islocked"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-center justify-between p-4">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">Is Locked</FormLabel>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />

                            <Button
                                type="submit"
                                size="default"
                                className="w-full"
                                disabled={isLoading}
                                onClick={form.handleSubmit(onSubmit)}
                            >
                                {isLoading ? (
                                    <span className="flex items-center justify-center">
                                        <svg
                                            className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v8H4z"
                                            ></path>
                                        </svg>
                                        Loading...
                                    </span>
                                ) : (
                                    "Submit"
                                )}
                            </Button>
                        </form>
                    </Form>
                </div>
            </DialogContent>
        </Dialog>
    );
}

export default React.memo(EditUserForm);
