import { Typography } from "@/components/Typography";
import React, { useState, useEffect, useRef, Suspense } from "react";
import BannerAction from "../redde-actions/banner";
import ServerSideTransactionTable from "./table/server-side-trans-table";
import { useAuth } from "@/contexts/AuthContexts";

import { useFetchReviewTransactions } from "@/hooks/transactionHooks/use-fetch-review-transactions";
import { ReviewTransactionFilter } from "@/hooks/transactionHooks/use-fetch-review-transactions";
import { DataTable } from "@/components/ui/data-table";
import { ArrowUpDown, CopyIcon, EllipsisVertical, EyeIcon } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { transformFailedData, transformReviewData } from "@/lib/utils";
import { Tooltip, TooltipContent } from "@/components/ui/tooltip";
import { TooltipProvider } from "@/components/ui/tooltip";
import { But<PERSON> } from "@/components/ui/button";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { TooltipTrigger } from "@/components/ui/tooltip";
import { isNil } from "lodash";
import { format } from "date-fns";
import Sidebar from "@/pages/users/sidebar";
import {
    FailedTransactionFilter,
    useFetchFailedTransactions,
} from "@/hooks/transactionHooks/use-fetch-failed-transactions";
import ServerSideFailedTransactionTable from "./table/server-side-failed-trans-table";
import TransactionActions from "@/components/dashboard/transactions-table/transaction-details";

export interface FilterState {
    legacyusername?: string;
    transtype?: string;
    brand?: string;
    brandgroup?: string;
    channel?: string;
    status?: string;
    isavailable?: boolean;
    issettled?: boolean;
    campaignid?: string;
    brandtransid?: string;
    charge?: string;
    legacyid?: string;
    transday?: string;
    nickname?: string;
    onFilterChange?: (filters: FilterState) => void;
}
interface ReviewTransaction {
    internalId: number;
    transId: number;
    brandtransid: string;
    username: string;
    appid: string;
    brandgroup: string;
    channel: string;
    transtype: string;
    amount: number;
    actualamount: number;
    charge: number;
    clienttransid: string;
    clienttransref: string;
    status: string;
    statusreason: string;
    transdate: string;
    statusupdatedate: string;
    isconfirmed: string;
    callbacksuccess: string;
    nickname: string;
    description: string;
}

export const columns: ColumnDef<ReviewTransaction>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title=" Transaction ID" />,
        // cell: ({ row }) => <div className="w-[50px]">{row.getValue("transId")}</div>,
        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("id")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    row.getValue("id")
                )}
            </div>
        ),
        enableSorting: true,
        enableHiding: false,
    },
    {
        accessorKey: "amount",
        // header: "Sender ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Amount" />,

        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("amount"));
            const formatted = new Intl.NumberFormat("en-GB", {
                style: "currency",
                currency: "GHS",
            }).format(amount);
            return (
                <div className="">
                    {isNil(row.getValue("amount")) ? (
                        <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                            N/A
                        </span>
                    ) : (
                        <p className="font-medium">{formatted}</p>
                    )}
                </div>
            );
        },
    },

    {
        accessorKey: "actualamount",
        // header: "Sender ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Actual Amount" />,

        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("actualamount"));
            const formatted = new Intl.NumberFormat("en-GB", {
                style: "currency",
                currency: "GHS",
            }).format(amount);
            return (
                <div className="">
                    {isNil(row.getValue("actualamount")) ? (
                        <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                            N/A
                        </span>
                    ) : (
                        <p className="font-medium">{formatted}</p>
                    )}
                </div>
            );
        },
    },
    {
        accessorKey: "nickname",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Nickname " />,

        cell: ({ row }) => (
            <div className="capitalize">
                {isNil(row.getValue("nickname")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    row.getValue("nickname")
                )}
            </div>
        ),
    },

    {
        accessorKey: "charge",
        // header: "Sender ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Charge" />,

        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("charge"));
            const formatted = new Intl.NumberFormat("en-GB", {
                style: "currency",
                currency: "GHS",
            }).format(amount);
            return (
                <div className="">
                    {isNil(row.getValue("charge")) ? (
                        <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                            N/A
                        </span>
                    ) : (
                        <p className="font-medium">{formatted}</p>
                    )}
                </div>
            );
        },
    },

    {
        accessorKey: "clienttransref",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Client Trans Ref" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("clienttransref")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    // row.getValue("clienttransref")
                    <>
                        <span>{String(row.getValue("clienttransref")).slice(0, 5)}...</span>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() =>
                                            navigator.clipboard.writeText(
                                                String(row.getValue("clienttransref"))
                                            )
                                        }
                                    >
                                        <CopyIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Copy</TooltipContent>
                            </Tooltip>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() => {
                                            alert(String(row.getValue("clienttransref"))); // You might want to replace this with a modal
                                        }}
                                    >
                                        <EyeIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View Client Trans Ref</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </>
                )}
            </div>
        ),
    },

    {
        accessorKey: "clienttransid",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Client Trans ID" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("clienttransid")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    <>
                        <span>{String(row.getValue("clienttransid")).slice(0, 5)}...</span>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() =>
                                            navigator.clipboard.writeText(
                                                String(row.getValue("clienttransid"))
                                            )
                                        }
                                    >
                                        <CopyIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Copy</TooltipContent>
                            </Tooltip>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() => {
                                            alert(String(row.getValue("clienttransid"))); // You might want to replace this with a modal
                                        }}
                                    >
                                        <EyeIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View Client Trans ID</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </>
                )}
            </div>
        ),
    },

    {
        accessorKey: "brandtransid",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Brand Trans ID" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("brandtransid")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    <>
                        <span>{String(row.getValue("brandtransid")).slice(0, 5)}...</span>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() =>
                                            navigator.clipboard.writeText(
                                                String(row.getValue("brandtransid"))
                                            )
                                        }
                                    >
                                        <CopyIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Copy</TooltipContent>
                            </Tooltip>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() => {
                                            alert(String(row.getValue("brandtransid"))); // You might want to replace this with a modal
                                        }}
                                    >
                                        <EyeIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View Brand Trans ID</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </>
                )}
            </div>
        ),
    },

    {
        accessorKey: "brandgroup",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Brand Group" />,

        cell: ({ row }) => (
            <div className="uppercase">
                {isNil(row.getValue("brandgroup")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    row.getValue("brandgroup")
                )}
            </div>
        ),
    },
    {
        accessorKey: "username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Username" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("username")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A</span>
                ) : (
                    row.getValue("username")
                )}
            </div>
        ),
    },
    {
        accessorKey: "appid",
        header: ({ column }) => <DataTableColumnHeader column={column} title="App ID" />,

        cell: ({ row }) => <div className="lowercase">{row.getValue("appid")}</div>,
    },

    {
        accessorKey: "channel",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Channel" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("channel")) ? (
                    <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                        No Channel
                    </span>
                ) : (
                    row.getValue("channel")
                )}
            </div>
        ),
    },

    {
        accessorKey: "status",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,

        cell: ({ row }) => {
            const active = row.getValue("status");
            return (
                <div
                    className={`flex items-center justify-center ${
                        active === "PAID"
                            ? "rounded-md border border-green-500 text-green-500"
                            : active === "FAILED"
                              ? "rounded-md border border-red-500 text-red-500"
                              : "text-orange-500"
                    }`}
                >
                    <p>{active}</p>
                </div>
            );
        },
    },

    {
        accessorKey: "statusreason",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Status Reason" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("statusreason")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    <>
                        <span>{String(row.getValue("statusreason")).slice(0, 5)}...</span>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() => {
                                            alert(String(row.getValue("statusreason"))); // You might want to replace this with a modal
                                        }}
                                    >
                                        <EyeIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View Status Reason</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </>
                )}
            </div>
        ),
    },

    {
        accessorKey: "transdate",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction Date" />,
        cell: ({ row }) => {
            const date = new Date(row.getValue("transdate"));
            return <div>{format(date, "PPP")}</div>;
        },
    },
    {
        id: "actions",
        enableHiding: false,
        header: "Action",
        cell: ({ row }) => {
            const transaction = row.original;
            const id = row.getValue("id");

            return (
                <div className="relative flex">
                    <div className="space-x-1 p-2">
                        <TransactionActions user={transaction} data={id} isReview={true} />
                    </div>
                </div>
            );
        },
    },
];

export const failedColums: ColumnDef<ReviewTransaction>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title=" Transaction ID" />,
        // cell: ({ row }) => <div className="w-[50px]">{row.getValue("transId")}</div>,
        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("transId")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    row.getValue("transId")
                )}
            </div>
        ),
        enableSorting: true,
        enableHiding: false,
    },
    {
        accessorKey: "amount",
        // header: "Sender ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Amount" />,

        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("amount"));
            const formatted = new Intl.NumberFormat("en-GB", {
                style: "currency",
                currency: "GHS",
            }).format(amount);
            return (
                <div className="">
                    {isNil(row.getValue("amount")) ? (
                        <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                            N/A
                        </span>
                    ) : (
                        <p className="font-medium">{formatted}</p>
                    )}
                </div>
            );
        },
    },

    {
        accessorKey: "actualamount",
        // header: "Sender ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Actual Amount" />,

        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("actualamount"));
            const formatted = new Intl.NumberFormat("en-GB", {
                style: "currency",
                currency: "GHS",
            }).format(amount);
            return (
                <div className="">
                    {isNil(row.getValue("actualamount")) ? (
                        <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                            N/A
                        </span>
                    ) : (
                        <p className="font-medium">{formatted}</p>
                    )}
                </div>
            );
        },
    },

    {
        accessorKey: "charge",
        // header: "Sender ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Charge" />,

        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("charge"));
            const formatted = new Intl.NumberFormat("en-GB", {
                style: "currency",
                currency: "GHS",
            }).format(amount);
            return (
                <div className="">
                    {isNil(row.getValue("charge")) ? (
                        <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                            N/A
                        </span>
                    ) : (
                        <p className="font-medium">{formatted}</p>
                    )}
                </div>
            );
        },
    },

    {
        accessorKey: "clienttransref",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Client Trans Ref" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("clienttransref")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    // row.getValue("clienttransref")
                    <>
                        <span>{String(row.getValue("clienttransref")).slice(0, 5)}...</span>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() =>
                                            navigator.clipboard.writeText(
                                                String(row.getValue("clienttransref"))
                                            )
                                        }
                                    >
                                        <CopyIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Copy</TooltipContent>
                            </Tooltip>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() => {
                                            alert(String(row.getValue("clienttransref"))); // You might want to replace this with a modal
                                        }}
                                    >
                                        <EyeIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View Client Trans Ref</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </>
                )}
            </div>
        ),
    },

    {
        accessorKey: "clienttransid",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Client Trans ID" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("clienttransid")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    <>
                        <span>{String(row.getValue("clienttransid")).slice(0, 5)}...</span>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() =>
                                            navigator.clipboard.writeText(
                                                String(row.getValue("clienttransid"))
                                            )
                                        }
                                    >
                                        <CopyIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Copy</TooltipContent>
                            </Tooltip>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() => {
                                            alert(String(row.getValue("clienttransid"))); // You might want to replace this with a modal
                                        }}
                                    >
                                        <EyeIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View Client Trans ID</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </>
                )}
            </div>
        ),
    },

    {
        accessorKey: "brandtransid",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Brand Trans ID" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("brandtransid")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    <>
                        <span>{String(row.getValue("brandtransid")).slice(0, 5)}...</span>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() =>
                                            navigator.clipboard.writeText(
                                                String(row.getValue("brandtransid"))
                                            )
                                        }
                                    >
                                        <CopyIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Copy</TooltipContent>
                            </Tooltip>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() => {
                                            alert(String(row.getValue("brandtransid"))); // You might want to replace this with a modal
                                        }}
                                    >
                                        <EyeIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View Brand Trans ID</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </>
                )}
            </div>
        ),
    },
    {
        accessorKey: "nickname",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Nickname " />,

        cell: ({ row }) => (
            <div className="capitalize">
                {isNil(row.getValue("nickname")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    row.getValue("nickname")
                )}
            </div>
        ),
    },
    {
        accessorKey: "brandgroup",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Brand Group" />,

        cell: ({ row }) => (
            <div className="uppercase">
                {isNil(row.getValue("brandgroup")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    row.getValue("brandgroup")
                )}
            </div>
        ),
    },
    {
        accessorKey: "username",
        // header: "Sender ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Username" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("username")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A</span>
                ) : (
                    row.getValue("username")
                )}
            </div>
        ),
    },
    {
        accessorKey: "appid",
        //  header: "Account ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="App ID" />,

        cell: ({ row }) => <div className="lowercase">{row.getValue("appid")}</div>,
    },

    {
        accessorKey: "channel",
        // header: "Sender ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Channel" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("channel")) ? (
                    <span className="font-medium capitalize text-gray-600 dark:text-gray-200">
                        No Channel
                    </span>
                ) : (
                    row.getValue("channel")
                )}
            </div>
        ),
    },

    {
        accessorKey: "status",
        // header: "Status",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,

        cell: ({ row }) => {
            const active = row.getValue("status");
            // console.log("status =>", active);
            return (
                <div
                    className={`flex items-center justify-center ${
                        active === "PAID"
                            ? "rounded-md border border-green-500 text-green-500"
                            : active === "FAILED"
                              ? "rounded-md border border-red-500 text-red-500"
                              : "text-orange-500"
                    }`}
                >
                    <p>{active}</p>
                </div>
            );
        },
    },

    {
        accessorKey: "statusreason",
        // header: "Legacy Username",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Status Reason" />,

        cell: ({ row }) => (
            <div className="">
                {isNil(row.getValue("statusreason")) ? (
                    <span className="font-medium text-gray-600 dark:text-gray-200">N/A </span>
                ) : (
                    <>
                        <span>{String(row.getValue("statusreason")).slice(0, 5)}...</span>

                        <TooltipProvider>
                            {/* <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() =>
                                            navigator.clipboard.writeText(
                                                String(row.getValue("statusreason"))
                                            )
                                        }
                                    >
                                        <CopyIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Copy</TooltipContent>
                            </Tooltip> */}
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                        onClick={() => {
                                            alert(String(row.getValue("statusreason"))); // You might want to replace this with a modal
                                        }}
                                    >
                                        <EyeIcon className="h-3 w-3 cursor-pointer" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View Status Reason</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </>
                )}
            </div>
        ),
    },

    {
        accessorKey: "transactiondate",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction Date" />,
        cell: ({ row }) => {
            const date = new Date(row.getValue("transactiondate"));

            return <div>{format(date, "PPP")}</div>;
        },
    },
    {
        id: "actions",
        enableHiding: false,
        header: "Action",
        cell: ({ row }) => {
            const transaction = row.original;
            const id = row.getValue("id");

            return (
                <div className="relative flex">
                    <div className="space-x-1 p-2">
                        <TransactionActions user={transaction} data={id} />
                    </div>
                </div>
            );
        },
    },
];
const Transactions = () => {
    const [selectedSection, setSelectedSection] = useState("Transactions");
    const deferredSelectedSection = React.useDeferredValue(selectedSection);
    const { user } = useAuth();
    const userId = user?.accountid ?? 0;
    const { mutate, data: mutationData, isPending, isError } = useFetchReviewTransactions(userId);
    const {
        mutate: mutateFailedTransactions,
        data: failedTransactionsData,
        isPending: isFailedTransactionsLoading,
        isError: isFailedTransactionsError,
    } = useFetchFailedTransactions(userId);
    // Format today's date as YYYY-MM-DD
    const today = new Date().toISOString().split("T")[0];

    const transformedData = React.useMemo(() => {
        if (!mutationData?.data) return [];
        // Make sure we're handling the data structure correctly
        const dataToTransform = Array.isArray(mutationData.data) ? mutationData.data : [];
        return transformReviewData(dataToTransform);
    }, [mutationData]);
    // console.log("transformed data", transformedData);

    const transformedFailedData = React.useMemo(() => {
        if (!failedTransactionsData?.data) return [];
        const dataToTransform = Array.isArray(failedTransactionsData.data)
            ? failedTransactionsData.data
            : [];
        return transformFailedData(dataToTransform);
    }, [failedTransactionsData]);
    // Initialize with default transday filter
    // console.log("transformed failed data", transformFailedData);

    const [filters, setFilters] = useState<FilterState>({
        transday: today,
    });

    // console.log("filters", filters);

    // Set initial filter when component mounts

    useEffect(() => {
        // Only add transday filter if it's explicitly set
        const initialFilter: ReviewTransactionFilter[] = [];
        mutate(initialFilter);
    }, [mutate]);

    useEffect(() => {
        // Only add transday filter if it's explicitly set
        const initialFilter: FailedTransactionFilter[] = [];
        mutateFailedTransactions(initialFilter);
    }, [mutateFailedTransactions]);

    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 50,
    });

    const formatFiltersForApi = (filters: FilterState): ReviewTransactionFilter[] => {
        const apiFilters: ReviewTransactionFilter[] = [];

        // Remove the default date behavior
        Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== "" && value !== null) {
                apiFilters.push({
                    key: key.toLowerCase(),
                    value: value,
                });
            }
        });

        return apiFilters;
    };

    const onFilterChange = (filters: FilterState) => {
        console.log("filters", filters);

        setFilters(filters);
        const apiFilters = formatFiltersForApi(filters);
        mutate(apiFilters);
    };

    const onFilterFailedChange = (newFilters: FilterState) => {
        setFilters(newFilters);
        const apiFilters = formatFiltersForApi(newFilters);
        mutateFailedTransactions(apiFilters);
    };

    const links = ["Transactions", "Failed Transactions"];

    return (
        <div
            className="flex h-screen w-full"
            style={{
                scrollbarWidth: "none",
                msOverflowStyle: "none",
            }}
        >
            <div className="fixed h-full w-[10%] bg-white shadow-2xl dark:bg-card">
                <Sidebar
                    links={links}
                    selected={deferredSelectedSection}
                    onSelect={setSelectedSection}
                />
            </div>
            <div className="ml-[10%] w-[90%] overflow-y-auto px-4 py-4">
                {deferredSelectedSection === "Transactions" && (
                    <>
                        <div className="flex w-full flex-col p-10">
                            <div className="my-3 w-full border-b border-gray-300 pb-2">
                                <Typography variant="large" text="Review Transactions" />
                            </div>
                            <BannerAction text="Manage all transaction here!" />
                        </div>

                        <div className="m-10 my-4 rounded-lg bg-card p-10">
                            <ServerSideTransactionTable
                                data={transformedData}
                                isDemo={false}
                                title="Transactions"
                                filterable={true}
                                pagination={pagination}
                                columns={columns}
                                loading={isPending}
                                onFilterChange={onFilterChange}
                            />
                        </div>
                    </>
                )}

                {deferredSelectedSection === "Failed Transactions" && (
                    <>
                        <div className="flex w-full flex-col p-10">
                            <div className="my-3 w-full border-b border-gray-300 pb-2">
                                <Typography variant="large" text="Review Transactions" />
                            </div>
                            <BannerAction text="Manage all failed transactions here!" />
                        </div>

                        <div className="m-10 my-4 rounded-lg bg-card p-10">
                            <ServerSideFailedTransactionTable
                                data={transformedFailedData}
                                isDemo={false}
                                title="Failed Transactions"
                                filterable={true}
                                pagination={pagination}
                                columns={failedColums}
                                loading={isFailedTransactionsLoading}
                                onFilterChange={onFilterFailedChange}
                            />
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

export default Transactions;
