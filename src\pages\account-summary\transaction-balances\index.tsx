import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import React, { useEffect, useState } from "react";


import { ColumnDef } from "@tanstack/react-table";
import {
    ArrowUpDown,
    BadgeCent,
    Diff,
    MoreHorizontal,
    Settings,
    UserPlus,
    WalletIcon,
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
// import { DataTable } from "@/components/Customtable/data-table";
import { DataTable } from "@/components/ui/data-table";
import { CardComponent } from "../confirmed-balances/Card";
import Banner from "@/components/Banner";
import { Card, CardContent } from "@/components/ui/card";

export type Payment = {
    id: string;
    amount: string | number;
    brand: "mtn" | "voda" | "tigo";
    tcharge: string | number;
    bcharge: string | number;
    diff: string | number;
    settlement: string | number;
};

export const columns: ColumnDef<Payment>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value: any) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value: any) => row.toggleSelected(!!value)}
                aria-label="Select row"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "brand",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                    className="hover:bg-transparent"
                >
                    Brand
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
    },
    {
        accessorKey: "amount",
        header: "Merchant Amount",
    },
    {
        accessorKey: "tcharge",
        header: "Total Charge",
    },
    {
        accessorKey: "bcharge",
        header: "Brand Charge",
    },
    {
        accessorKey: "diff",
        header: "Difference",
    },
    {
        accessorKey: "settlement",
        header: "Expected Settlement",
    },
];

async function getData(): Promise<Payment[]> {
    const amt = 311189;
    return [
        {
            id: "728ed52f",
            amount: amt.toLocaleString("en-US", {
                minimumFractionDigits: 3,
            }),
            brand: "voda",
            tcharge: "1234",
            bcharge: "1234",
            diff: "1234",
            settlement: "1234",
        },
        // ...
    ];
}
function TransactionBalances() {
    const [data, setData] = useState<Payment[]>([]);
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
        async function fetchData() {
            const result = await getData();
            setData(result);
            setLoading(false);
        }

        fetchData();
    }, []);

    if (loading) {
        return <div>Loading...</div>;
    }
    return (
        <div className="mx-0  w-full space-y-5">
            <Banner
                title="AMOUNT OWED FROM BRANDS"
                subTitle="Note: Report include today's transactions"
            />
            <main className="flex flex-1 flex-col">
                <div className="grid grid-cols-5 gap-6">
                    <CardComponent
                        title="Merchant Clients"
                        value="200"
                        icon={<UserPlus className="h-4 w-4 text-blue-500" />}
                    />
                    <CardComponent
                        title="Total Charge"
                        value="20"
                        icon={<BadgeCent className="h-4 w-4 text-blue-500" />}
                    />
                    <CardComponent
                        title="Brand Charge"
                        value="0"
                        icon={<BadgeCent className="h-4 w-4 text-blue-500" />}
                    />
                    <CardComponent
                        title="Difference"
                        value="0"
                        icon={<Diff className="h-4 w-4 text-blue-500" />}
                    />
                    <CardComponent
                        title="Expected Settlement"
                        value="0"
                        icon={<Settings className="h-4 w-4 text-blue-500" />}
                    />
                </div>
            </main>
            <div className=" mx-auto  ">
                <Card>
                    <CardContent>

                    </CardContent>
                </Card>
                <DataTable columns={columns} data={data} title="" filterKey="bcharge"/>
            </div>
        </div>
    );
}

export default TransactionBalances;
