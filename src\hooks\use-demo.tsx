import { DemoContext } from "@/contexts/DemoContext";
import { useContext } from "react";
/**
 * A custom hook for accessing the demo context.
 *
 * This hook provides access to the DemoContext, which is expected to contain
 * state and functions related to toggling demo functionality in the application.
 *
 * @returns {Object} The context value from DemoContext
 * @throws {Error} If used outside of a DemoProvider
 *
 * @example
 * const { isDemoMode, toggleDemoMode } = useDemoToggle();
 */
const useDemoToggle = () => {
    const context = useContext(DemoContext);
    if (!context) {
        throw new Error("useDemoToggle must be used within a DemoProvider");
    }
    return context;
};

export default useDemoToggle;
