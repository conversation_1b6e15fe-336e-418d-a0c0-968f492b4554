import React, { Suspense } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { EyeIcon } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export interface TransactionProps {
    id: number;
    accountid: number;
    amount: number;
    actualamount: number;
    balance: number;
    brand: string;
    brandgroup: string;
    brandinvoice: string;
    brandtransid: string;
    campaignid: number;
    channel: string;
    charge: number;
    clientreference: string;
    clienttransid: string;
    description: string;
    status: string;
    transactiondate: string;
    transtype: string;
    statusupdatedate: string;
    settleddate: string;
    issettled: boolean;
}

interface TransTableColumnsProps {
    viewTransaction: (transaction: TransactionProps) => void;
}

export const transTableColumns = ({
    viewTransaction,
}: TransTableColumnsProps): ColumnDef<TransactionProps>[] => [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
        cell: ({ row }) => <div className="w-[80px]">{row.getValue("id")}</div>,
        enableSorting: true,
        enableHiding: false,
    },
    {
        accessorKey: "clienttransid",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction ID" />,
        cell: ({ row }) => <div className="font-medium">{row.getValue("clienttransid")}</div>,
    },
    {
        accessorKey: "amount",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Amount" />,
        cell: ({ row }) => (
            <div className="font-medium">
                ₱
                {Number(row.getValue("amount")).toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                })}
            </div>
        ),
    },
    {
        accessorKey: "brand",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Brand" />,
        cell: ({ row }) => <div className="uppercase">{row.getValue("brand")}</div>,
    },
    {
        accessorKey: "channel",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Channel" />,
        cell: ({ row }) => <div className="capitalize">{row.getValue("channel")}</div>,
    },
    {
        accessorKey: "status",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
        cell: ({ row }) => {
            const status = row.getValue("status") as string;
            return (
                <div
                    className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ${
                        status.toLowerCase() === "success"
                            ? "bg-green-50 text-green-700 ring-green-600/20"
                            : status.toLowerCase() === "pending"
                              ? "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
                              : "bg-red-50 text-red-700 ring-red-600/20"
                    } `}
                >
                    {status}
                </div>
            );
        },
    },
    {
        accessorKey: "transactiondate",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction Date" />,
        cell: ({ row }) => {
            const date = new Date(row.getValue("transactiondate"));
            return <div>{format(date, "PPp")}</div>;
        },
    },
    {
        accessorKey: "issettled",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Settlement" />,
        cell: ({ row }) => {
            const settled = row.getValue("issettled");
            return (
                <div
                    className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ${
                        settled
                            ? "bg-blue-50 text-blue-700 ring-blue-600/20"
                            : "bg-gray-50 text-gray-700 ring-gray-600/20"
                    } `}
                >
                    {settled ? "Settled" : "Unsettled"}
                </div>
            );
        },
    },
    {
        id: "actions",
        enableHiding: false,
        header: "Action",
        cell: ({ row }) => {
            const transaction = row.original;
            return (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                variant="icon"
                                onClick={() => viewTransaction(transaction)}
                                className="h-8 w-8"
                            >
                                <EyeIcon className="h-4 w-4 text-primary" />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>View Transaction</TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            );
        },
    },
];

export default transTableColumns;
