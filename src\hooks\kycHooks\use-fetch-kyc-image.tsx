import { kycDocumentImage } from "@/api/kyc/kycApis";
import { useQuery } from "@tanstack/react-query";

/**
 * A custom hook for fetching user data.
 *
 * @param {number} userid - The ID of the user to fetch.
 * @returns {Object} An object containing:
 *   - data: The fetched user data.
 *   - isLoading: Boolean indicating if data is being loaded.
 *   - isError: <PERSON>olean indicating if an error occurred.
 *   - error: Any error that occurred during the fetch.
 *   - refetch: Function to refetch the data.
 */
export const useFetchKycImage = (accountid: number, documentid: number) => {
    return useQuery({
        queryKey: ["kycimage", accountid, documentid],
        queryFn: () => kycDocumentImage(accountid, documentid),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
};
