// src/components/TrayMenuComponent.tsx
import React from "react";
import { CircleUser, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import ToggleTheme from "../ToggleTheme";
import { useAuth } from "@/contexts/AuthContexts";

export function TrayMenuComponent() {
    const { user, login, logout } = useAuth();

    return (
        <div className="relative flex flex-col">
            <header className="sticky top-0 z-30 flex h-[60px] items-center gap-4 border-b bg-muted/40 px-6">
                <div className="w-full flex-1">
                    <form>
                        <div className="relative flex items-center">
                            <Search className="absolute left-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                type="search"
                                placeholder="Search..."
                                className="w-full appearance-none bg-background pl-8 shadow-none md:w-2/3 lg:w-1/3"
                            />
                        </div>
                    </form>
                </div>
                <div className="flex items-center gap-x-4">
                    <ToggleTheme />
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button
                                variant="secondary"
                                size="icon"
                                className="rounded-full bg-primary"
                            >
                                <CircleUser className="h-5 w-5 text-white" />
                                <span className="sr-only">Toggle user menu</span>
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>My Account</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>Settings</DropdownMenuItem>
                            <DropdownMenuItem>Support</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={logout}>Logout</DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </header>
        </div>
    );
}
