import React, { useState, useEffect } from "react";
import { Upload, Trash2, X, Check<PERSON>ircle2, <PERSON><PERSON><PERSON><PERSON>, Badge } from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import * as XLSX from "xlsx";
import { Progress } from "@/components/ui/progress";
import { toast } from "./use-toast";
import { useBulkCreateCampaigns } from "@/hooks/campaignHooks/use-bulk-create-campaigns";
import { useBulkBankAccounts } from "@/hooks/bankHooks/use-bulk-bank-accounts";

type ExcelRow = Record<string, string | number>;

const isExcelFile = (file: File) => {
    return file.name.match(/\.(xlsx|xls)$/i);
};

export function BulkBankAccounts() {
    // const [isModalOpen, setIsModalOpen] = useState(false);
    const [excelData, setExcelData] = useState<ExcelRow[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [headers, setHeaders] = useState<string[]>([]);
    const [progress, setProgress] = useState(0);
    const [processedCount, setProcessedCount] = useState(0);
    const [isProcessingFile, setIsProcessingFile] = useState(false);
    const bulkCreateCampaigns = useBulkCreateCampaigns();
    const bulkCreateBankAccounts = useBulkBankAccounts();
    const [processedResults, setProcessedResults] = useState<
        Array<{
            data: Record<string, string | number>;
            status: "success" | "failed";
            error?: string;
        }>
    >([]);
    const processExcelFile = async (file: File) => {
        setIsProcessingFile(true);
        try {
            const reader = new FileReader();

            await new Promise((resolve) => {
                reader.onload = (e) => {
                    const data = new Uint8Array(e.target?.result as ArrayBuffer);
                    const workbook = XLSX.read(data, { type: "array" });

                    // Get the first worksheet
                    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

                    // Convert to JSON
                    const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });

                    if (jsonData && jsonData.length > 0) {
                        const headers = jsonData[0] as string[];
                        setHeaders(headers);

                        const rows = jsonData.slice(1).map((row: any) => {
                            const obj: ExcelRow = {};
                            headers.forEach((header, index) => {
                                obj[header] = row[index] ?? "";
                            });
                            return obj;
                        });

                        setExcelData((current) => [...current, ...rows]);
                    }
                    resolve(null);
                };
                reader.readAsArrayBuffer(file);
            });
        } catch (error) {
            console.error("Error processing Excel file:", error);
            toast({
                title: "Error processing Excel file",
                description: "Please try again",
                variant: "destructive",
            });
        } finally {
            setIsProcessingFile(false);
        }
    };

    const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        const invalidFiles = files.filter((file) => !isExcelFile(file));

        if (invalidFiles.length > 0) {
            toast({
                title: "Error processing Excel file",
                description: "Please upload only Excel files (.xlsx, .xls)",
                variant: "destructive",
            });
            e.target.value = "";
            return;
        }

        for (const file of files) {
            await processExcelFile(file);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDrop = async (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();

        const files = Array.from(e.dataTransfer.files);
        const invalidFiles = files.filter((file) => !isExcelFile(file));

        if (invalidFiles.length > 0) {
            toast({
                title: "Error processing Excel file",
                description: "Please upload only Excel files (.xlsx, .xls)",
                variant: "destructive",
            });
            return;
        }

        for (const file of files) {
            await processExcelFile(file);
        }
    };

    const removeRow = (index: number) => {
        setExcelData((prevData) => prevData.filter((_, i) => i !== index));
    };

    const handleSubmit = async () => {
        if (excelData.length === 0) {
            toast({
                title: "No data to submit",
                description: "Please upload some data",
                variant: "destructive",
            });
            return;
        }

        setIsSubmitting(true);
        setProgress(0);
        setProcessedCount(0);
        setProcessedResults([]);

        try {
            // Create an onProgress callback
            // const onProgress = (count: number) => {
            //     setProcessedCount(count);
            //     const progressPercentage = (count / excelData.length) * 100;
            //     setProgress(progressPercentage);
            // };
            const onProgress = (
                count: number,
                result?: {
                    data: Record<string, string | number>;
                    status: "success" | "failed";
                    error?: string;
                }
            ) => {
                setProcessedCount(count);
                const progressPercentage = (count / excelData.length) * 100;
                setProgress(progressPercentage);

                // if (result) {
                //     setProcessedResults((prev) => [...prev, result]);
                // }
                if (result) {
                    setProcessedResults((prev) => {
                        // Check if this result already exists to avoid duplicates
                        const exists = prev.some(
                            (r) => JSON.stringify(r.data) === JSON.stringify(result.data)
                        );
                        if (!exists) {
                            return [...prev, result];
                        }
                        return prev;
                    });
                }
            };

            // Pass the callback to the mutation
            const result = await bulkCreateBankAccounts.mutateAsync({
                data: excelData,
                onProgress,
            });

            if (processedResults.length === 0) {
                const defaultResults = excelData.map((data, index) => ({
                    data,
                    status: index < result.successful ? ("success" as const) : ("failed" as const),
                    error: index >= result.successful ? "Failed to process" : undefined,
                }));
                setProcessedResults(defaultResults);
            }

            // Show success message
            toast({
                title: "Bulk Upload Complete",
                description: `Successfully processed ${result.successful} items. Failed: ${result.failed}`,
                variant: result.failed > 0 ? "destructive" : "default",
            });

            // Clear data and close modal on success
            if (result.failed === 0) {
                setExcelData([]);
                // setIsModalOpen(false);
            }
        } catch (error) {
            // console.error("Error in bulk upload:", error);
            const failedResults = excelData.map((data) => ({
                data,
                status: "failed" as const,
                error: `Failed to process, ${error instanceof Error ? error.message : "Unknown error"}`,
            }));
            setProcessedResults(failedResults);
            toast({
                title: "Error",
                description: "Failed to process bulk upload",
                variant: "destructive",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const clearAllData = () => {
        setExcelData([]);
        setHeaders([]);
    };

    return (
        <div className="grid gap-4 py-4">
            <div
                className="relative cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-12 transition-colors hover:border-gray-400"
                onClick={() => document.getElementById("file-upload")?.click()}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
            >
                <div className="flex flex-col items-center justify-center gap-2">
                    {isProcessingFile ? (
                        <>
                            <div className="h-12 w-12 animate-spin rounded-full border-4 border-gray-200 border-t-primary" />
                            <p className="text-sm text-gray-500">Processing Excel file...</p>
                        </>
                    ) : (
                        <>
                            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-50">
                                <Upload className="h-6 w-6 text-gray-600" />
                            </div>
                            <h3 className="text-lg font-semibold">Drag Excel files here</h3>
                            <p className="text-sm text-gray-500">
                                Click to upload or drag and drop
                            </p>
                            <p className="text-xs text-gray-400">
                                Supports: .xlsx, .xls (max 10MB)
                            </p>
                        </>
                    )}
                    <input
                        id="file-upload"
                        type="file"
                        multiple
                        accept=".xlsx,.xls"
                        onChange={handleFileUpload}
                        className="hidden"
                    />
                </div>
            </div>

            {excelData.length > 0 && (
                <>
                    <div className="flex justify-end">
                        <Button
                            variant="destructive"
                            size="sm"
                            onClick={clearAllData}
                            className="gap-2"
                        >
                            <X className="h-4 w-4" />
                            Clear All
                        </Button>
                    </div>

                    <div className="mt-4 max-h-[400px] overflow-auto">
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        {Object.keys(excelData[0]).map((header) => (
                                            <TableHead key={header} className="whitespace-nowrap">
                                                {header}
                                            </TableHead>
                                        ))}
                                        <TableHead className="whitespace-nowrap">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {excelData.map((row, index) => (
                                        <TableRow key={index}>
                                            {Object.keys(row).map((key) => (
                                                <TableCell
                                                    key={`${index}-${key}`}
                                                    className="whitespace-nowrap"
                                                >
                                                    {String(row[key])}
                                                </TableCell>
                                            ))}
                                            <TableCell>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => removeRow(index)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                    {isSubmitting && (
                        <div className="space-y-2">
                            <Progress value={progress} className="w-full" />
                            <p className="text-center text-sm text-gray-500">
                                Processing {processedCount} of {excelData.length} items
                            </p>
                        </div>
                    )}
                    <Button onClick={handleSubmit} disabled={isSubmitting} className="w-full">
                        {isSubmitting ? "Processing..." : "Submit"}
                    </Button>
                </>
            )}

            {processedResults.length > 0 && (
                <div className="mt-4">
                    <h3 className="mb-4 text-lg font-semibold">Processed Results</h3>
                    <div className="mb-4 flex gap-4">
                        {/* <div className="flex items-center gap-2">
                            <Badge className="text-green-800 bg-green-100">
                                Success:{" "}
                                {processedResults.filter((r) => r.status === "success").length}
                            </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                            <Badge className="text-red-800 bg-red-100">
                                Failed:{" "}
                                {processedResults.filter((r) => r.status === "failed").length}
                            </Badge>
                        </div> */}
                    </div>

                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Status</TableHead>
                                    {Object.keys(processedResults[0]?.data || {}).map((header) => (
                                        <TableHead key={header} className="whitespace-nowrap">
                                            {header}
                                        </TableHead>
                                    ))}
                                    <TableHead>Message</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {processedResults.map((result, index) => (
                                    <TableRow
                                        key={index}
                                        // className={
                                        //     result.status === "failed" ? "bg-red-50" : "bg-green-50"
                                        // }
                                    >
                                        <TableCell>
                                            {result.status === "success" ? (
                                                <CheckCircle2 className="h-5 w-5 text-green-600" />
                                            ) : (
                                                <XCircle className="h-5 w-5 text-red-600" />
                                            )}
                                        </TableCell>
                                        {Object.values(result.data).map((value, i) => (
                                            <TableCell key={i} className="whitespace-nowrap">
                                                {String(value)}
                                            </TableCell>
                                        ))}
                                        <TableCell>
                                            {result.status === "failed" ? (
                                                <span className="text-red-600">
                                                    {result.error || "Failed to process"}
                                                </span>
                                            ) : (
                                                <span className="text-green-600">
                                                    Successfully processed
                                                </span>
                                            )}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                </div>
            )}
        </div>
    );
}
