import { DashboardChart } from "@/components/dashboard/DashboardChart";

import { useAuth } from "@/contexts/AuthContexts";
import React, { FC, lazy, LazyExoticComponent, Suspense, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Wallet, UserPlus, Donut, Users } from "lucide-react";
import ServerSideUsersTable from "../users/users-table/server-side-table";
import { useFetchUsers } from "@/hooks/usersHooks/use-fetch-users";
import { DashboardCardProps } from "@/lib/interfaces";
import { useFullBalances } from "@/hooks/accountsHooks/use-fullbalance";
import { sumBalances, transformData } from "@/lib/utils";
import { CardComponent } from "../account-summary/confirmed-balances/Card";
import { CalendarDateRangePicker } from "@/components/ui/date-range-picker";
import { Button } from "@/components/ui/button";
import DashboardCampaigns from "./campaigns/campaigns";

const DashboardCardLazy: LazyExoticComponent<FC<DashboardCardProps>> = lazy(
    () => import("@/components/dashboard/DashboardCards")
);
function Dashboard() {
    const { user } = useAuth();
    const navigate = useNavigate();

    const accountID = user?.useraccount?.accountid;
    const { data: fullBalance } = useFullBalances(accountID);
    const transformedData = fullBalance ? transformData(fullBalance.data.flat()) : [];
    // console.log("full balance", transformedData);
    const totalBalance = sumBalances(transformedData);

    const formattedTotalBalance = new Intl.NumberFormat("en-GB", {
        style: "currency",
        currency: "GHS",
    }).format(totalBalance);
    const {
        data,
        pagination,
        keyword,
        isLoading,
        onPaginationChange,
        onKeywordChange,
        sorting,
        onSortingChange,
    } = useFetchUsers(50);

    useEffect(() => {
        if (!user) {
            navigate("/"); // Redirect to login if not authenticated
        }
    }, [user, navigate]);
    return (
        <>
            {/* {navigation.state === "loading" && <h1> Loading Dashboard</h1>} */}

            <div className="mt-16 space-y-2 px-6 py-4">
                <div className="flex items-center justify-between space-y-2">
                    <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
                    <div className="flex items-center space-x-2"></div>
                </div>

                {/* <div className="grid grid-cols-4 gap-4 py-4"> */}
                <div className="grid w-full grid-cols-1 items-center justify-center gap-5 lg:grid-cols-2">
                    {/* {dashInfo.map((dash) => ( */}
                    <Suspense fallback={<div>Loading...</div>}>
                        <CardComponent
                            title="Total Confirmed Balances"
                            value={formattedTotalBalance}
                            icon={<Wallet className="h-4 w-4 text-blue-500" />}
                        />
                    </Suspense>
                    <Suspense fallback={<div>Loading...</div>}>
                        <CardComponent
                            title="Total Users"
                            value={data?.totalElements}
                            icon={<Users className="h-4 w-4 text-primary" />}
                        />
                    </Suspense>
                    {/* ))} */}
                </div>
                <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                    <DashboardChart />
                    <DashboardCampaigns />
                </div>
                {/* <TransactionsTable data={[]} filterable={true} title="Transactions"/> */}

                <ServerSideUsersTable
                    // columns={columns}
                    data={data}
                    title="Users"
                    loading={isLoading}
                    filterable={false}
                    pagination={pagination}
                    pageCount={data?.totalPages || 0}
                    keyword={keyword}
                    onPaginationChange={onPaginationChange}
                    onKeywordChange={onKeywordChange}
                    sorting={sorting}
                    onSortingChange={onSortingChange}
                />
            </div>
        </>
    );
}

export default Dashboard;
