import React, { useEffect } from "react";
// import { useNavigate } from "react-router-dom";

interface ErrorPageProps {
    statusCode: number;
    message: string;
}

const ErrorPage: React.FC<ErrorPageProps> = ({ statusCode, message }) => {
    // const navigate = useNavigate();

    useEffect(() => {
        // Log the error to your preferred error tracking service
        console.error(`Error ${statusCode}: ${message}`);
    }, [statusCode, message]);

    return (
        <div className="flex h-screen flex-col items-center justify-center text-center">
            <h1 className="mb-4 text-4xl font-bold">Oops! Something went wrong</h1>
            <p className="mb-8 text-xl">
                Error {statusCode}: {message}
            </p>
            {/* <button
                onClick={() => navigate("/")}
                className="rounded bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
            >
                Go back to homepage
            </button> */}
        </div>
    );
};

export default ErrorPage;
