import React, { Suspense } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { EyeIcon, CopyIcon, CheckIcon, XIcon } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { isNil } from "es-toolkit";
import { CampaignChargesProps, ChargeProfile } from "./campaign-charges-table";

interface ChargeProfileDetails {
    id: number;
    brandgroup: string;
    brand: string;
    chargemodel: string;
    chargetype: string;
    lowerbound: number;
    merchantcharge: number | null;
    payercharge: number;
    transtype: string;
    upperbound: number | null;
}

export const ChargeProfileDetailsColumn = (): ColumnDef<ChargeProfileDetails>[] => [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
        cell: ({ row }) => <div className="w-[50px]">{row.getValue("id")}</div>,
        enableSorting: true,
        enableHiding: false,
    },
    {
        accessorKey: "brand",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Brand" />,
        cell: ({ row }) => <div className="capitalize">{row.getValue("brand")}</div>,
    },
    {
        accessorKey: "brandgroup",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Brand Group" />,
        cell: ({ row }) => <div className="capitalize">{row.getValue("brandgroup")}</div>,
    },
    {
        accessorKey: "chargemodel",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Charge Model" />,
        cell: ({ row }) => <div className="">{row.getValue("chargemodel")}</div>,
    },
    {
        accessorKey: "lowerbound",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Lower Bound" />,
        cell: ({ row }) => <div className="">{row.getValue("lowerbound")}</div>,
    },

    {
        accessorKey: "merchantcharge",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Merchant Charge" />,
        cell: ({ row }) => {
            const merchantCharge: any = row.getValue("merchantcharge");
            return <div className="">{merchantCharge === null ? "No Charge" : merchantCharge}</div>;
        },
    },
    {
        accessorKey: "payercharge",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Payer Charge" />,
        cell: ({ row }) => <div className="">{row.getValue("payercharge")}</div>,
    },
    {
        accessorKey: "transtype",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Trans Type" />,
        cell: ({ row }) => <div className="">{row.getValue("transtype")}</div>,
    },
];

export default ChargeProfileDetailsColumn;
