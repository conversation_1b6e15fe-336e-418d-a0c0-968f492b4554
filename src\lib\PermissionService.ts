type PermissionAction =
    | "canview"
    | "canadd"
    | "canedit"
    | "candelete"
    | "canprint"
    | "canviewlog"
    | "canapprove";

interface Permission {
    authitemid: string;
    authitemname: string;
    [key: string]: any;
}

export class PermissionService {
    private static permissions: Permission[] = [];

    static setPermissions(userPermissions: Permission[]) {
        this.permissions = userPermissions;
    }

    static hasPermission(authItemId: string, action: PermissionAction): boolean {
        const permission = this.permissions.find((p) => p.authitemid === authItemId);
        return permission ? permission[action] : false;
    }

    static canAccessModule(authItemId: string): boolean {
        return this.hasPermission(authItemId, "canview");
    }
}
