import { combineReducers, configureStore } from "@reduxjs/toolkit";
// import counterSlice from "./features/CounterSlice";
// import AuthSlice from "./features/AuthSlice";
import { persistReducer, persistStore } from "redux-persist";
import reducers from "./reducers";
import storage from "redux-persist/lib/storage";

const persistConfig = {
    key: "root",
    storage,
    //   whitelist: ["counter", "", "student"],
};

const persistedReducer = persistReducer(persistConfig, reducers);

const store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({ serializableCheck: false }),
});
const { dispatch } = store;

const persister = persistStore(store);
export { store, persister, dispatch };
