// AddUser.js
import React, { useState } from "react";

import ServerSideCampaignsTable from "@/components/campaigns/campaigns-table/server-side-table";
import { useFetchCampaigns } from "@/hooks/campaignHooks/use-fetch-campaigns";
import _ from "lodash";

const Campaigns: React.FC = () => {
    const {
        data,
        pagination,
        keyword,
        sorting,
        isLoading,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
    } = useFetchCampaigns(50);

    return (
        <div className="">
            <ServerSideCampaignsTable
                data={data?.content}
                title="Campaigns"
                loading={isLoading}
                filterable={true}
                pagination={pagination}
                pageCount={data?.totalPages || 0}
                keyword={keyword}
                sorting={sorting}
                onPaginationChange={onPaginationChange}
                onKeywordChange={onKeywordChange}
                onSortingChange={onSortingChange}
            />
        </div>
    );
};

export default Campaigns;
