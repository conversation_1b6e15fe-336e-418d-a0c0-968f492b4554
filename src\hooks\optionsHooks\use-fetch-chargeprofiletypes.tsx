import { fetchChargeProfiles, fetchChargeProfileTypes } from "@/api/options/allOptions";
import { useQuery } from "@tanstack/react-query";

export const useFetchChargeProfileTypes = () => {
    return useQuery({
        queryKey: ["chargeprofiletypes"],
        queryFn: () => fetchChargeProfileTypes(),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
};
export const useFetchChargeProfiles = (keyword?: string) => {
    return useQuery({
        queryKey: ["chargeprofiles", keyword],
        queryFn: () => fetchChargeProfiles(keyword),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
};
