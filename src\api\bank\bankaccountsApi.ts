import { IAccountIdParams, IResponseParams } from "@/lib/interfaces";
import { api } from "../api";

// Gets Account balances for every customer on Redde
/**
 * Fetches the bank accounts associated with a given account ID.
 *
 * @param {IAccountIdParams} accountId - The ID of the account whose bank accounts are to be fetched.
 * @param {any} params - Additional parameters to be passed with the request.
 * @returns {Promise<any>} A promise that resolves to the API response data containing the bank accounts.
 */

export const fetchBankAccounts = async (
    accountId: IAccountIdParams,
    page: number,
    pageSize: number,
    keyword: string,
    sorting: any
) => {
    try {
        const response = await api.get(
            `/accounts/${accountId}/bankaccounts?page=${page}&size=${pageSize}&sort=desc,&search=${keyword}`
        );
        return response?.data;
    } catch (error) {
        throw error;
    }
};

/**
 * Fetches the bank accounts associated with a given account ID.
 *
 * @param {bankAccountId} bankAccountId - The ID of the bank account to be fetched.
 * @param {IAccountIdParams} accountId - The ID of the account whose bank accounts are to be fetched.
 * @returns {Promise<any>} A promise that resolves to the API response data containing the bank accounts.
 */
export const fetchBankAccount = async (accountId: IAccountIdParams, bankAccountId: number) => {
    try {
        const response = await api.get(`/accounts/${accountId}/bankaccounts/${bankAccountId}`);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const createBankAccount = async (accountId: number, data: any) => {
    try {
        const response = await api.post(`/accounts/${accountId}/bankaccounts`, data);
        return response?.data;
        // await console.log("data", data, accountId);
    } catch (error) {
        throw error;
    }
};

interface BankAccountData {
    accountname: string;
    accountnumber: string;
    bankid: number;
    branchid: number;
    sortcode?: string;
    swiftcode?: string;
}
interface BankAccount {
    accountid: number;
    accountname: string;
    accountnumber: string;
    bankid: number;
    branchid: number;
    createdby: number;
    createddate: string;
    id: number;
    sortcode: string;
    swiftcode: string;
    updatedby: number;
    updateddate: string;
}

interface ApiResponse {
    data: BankAccount[];
    message: string;
    status: string;
}

interface Campaign {
    accountid: number;
    allowcreditapi: boolean;
    apikey: string;
    apikeyexpirydate: string;
    approvedby: number;
    approveddate: string;
    autopayout: boolean;
    callbackurl: string;
    createby: number;
    createddate: string;
    description: string;
    enableotp: boolean;
    enddate: string;
    id: number;
    ipwhitelist: string;
    isactive: boolean;
    isapproved: boolean;
    isenhanced: boolean;
    itcapikey: string;
    itcmerchantid: string;
    itcproductid: string;
    legacyusername: string;
    payoutaccountid: number;
    senderid: string;
    settlementcallbackurl: string;
    startdate: string;
    transnotif: boolean;
    transnotiftype: string;
    updatedby: number;
    updateddate: string;
}
export const editBankAccount = async (
    accountId: number,
    bankAccountId: number,
    data: BankAccountData
) => {
    try {
        const response = await api.put(
            `/accounts/${accountId}/bankaccounts/${bankAccountId}`,
            data
        );
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const delayedCreateBankAccount = async (bankAccountData: any) => {
    // Simulate API delay
    const payload = {
        accountid: bankAccountData?.accountid,
        accountname: bankAccountData?.accountname,
        accountnumber: bankAccountData?.accountnumber,
        bankid: bankAccountData?.bankid,
        branchid: bankAccountData?.branchid,
        createdby: bankAccountData?.createdby,
        createddate: bankAccountData?.createddate,
        sortcode: bankAccountData?.sortcode,
        swiftcode: bankAccountData?.swiftcode,
    };
    const accountId = payload?.accountid;
    await new Promise((resolve) => setTimeout(resolve, 1000));
    const response = await api.post<ApiResponse>(`/accounts/${accountId}/bankaccounts`, payload);
    const bankAccountId = response.data.data[0]?.id;
    if (!bankAccountId) {
        throw new Error("Bank account ID not found in the response");
    }
    return { response, bankAccountId, success: true };
};

export const delayedUpdateCampaign = async (bankAccountData: any, bankAccountId: any) => {
    // Simulate API delay
    const campaignid = bankAccountData?.campaignid;
    const accountId = bankAccountData?.accountid;
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const currentCampaignResponse = await api.get<Campaign>(`/campaigns/${campaignid}`);
    const currentCampaign = currentCampaignResponse.data;

    const updatedCampaign = {
        ...currentCampaign,
        payoutaccountid: bankAccountId,
        autopayout: true,
        accountid: accountId,
        description: bankAccountData?.description || currentCampaign?.description,
    };

    console.log("ref", updatedCampaign);
    const response = await api.put(`/campaigns/${campaignid}`, updatedCampaign);

    // const response = await api.put(`/campaigns/${campaignid}`, {
    //     payoutaccountid: bankAccountId,
    //     accountid: accountId,
    //     description: bankAccountData?.description,
    // });

    return { response, success: true };
};

// New function to simulate bulk creation
export const createBulkBankAccounts = async (
    bankAccounts: any[],
    onProgress?: (count: number) => void
) => {
    let successful = 0;
    let failed = 0;
    const errors: { record: any; error: any }[] = [];

    // In production, this would be a single API call
    // For now, we'll process them sequentially and return progress
    for (let i = 0; i < bankAccounts.length; i++) {
        try {
            // await delayedCreateBankAccount(bankAccounts[i]);
            const { bankAccountId } = await delayedCreateBankAccount(bankAccounts[i]);
            successful++;
            await delayedUpdateCampaign(bankAccounts[i], bankAccountId);
            // Call the progress callback with the current count
            onProgress?.(successful);
        } catch (error) {
            failed++;
            errors.push({ record: bankAccounts[i], error });
            // Still call progress even for failures
            onProgress?.(successful + failed);
        }
    }

    return { successful, failed, errors };
};
