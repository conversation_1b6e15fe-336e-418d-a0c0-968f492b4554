import React, { useState } from "react";
import { <PERSON>evronRight, ChevronLeft, Loader2 } from "lucide-react";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { useFetchChargeProfileTypes } from "@/hooks/optionsHooks/use-fetch-chargeprofiletypes";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useFetchChargeTypes } from "@/hooks/optionsHooks/use-fetch-charge-types";
import { useFetchBrands } from "@/hooks/optionsHooks/use-fetch-brands";
import { useFetchTransactionTypes } from "@/hooks/optionsHooks/use-fetch-transtypes";
import { useFetchBrandGroups } from "@/hooks/optionsHooks/use-fetch-brandgroups";
import { useFetchChargeModels } from "@/hooks/optionsHooks/use-fetch-chargemodels";
import { chargesProfile, createCampaignCharges, editChargesProfile } from "@/api/campaigns/appsApi";
import { toast, useToast } from "../ui/use-toast";

// Define Zod schema for ChargeProfileDetail
const chargeProfileDetailSchema = z.object({
    brand: z.string().min(1, "Brand is required"),
    brandgroup: z.string().min(1, "Brand group is required"),
    chargemodel: z.string().min(1, "Charge model is required"),
    chargetype: z.string().min(1, "Charge type is required"),
    id: z.number().optional(),
    lowerbound: z.number().min(0),
    merchantcharge: z.number().min(0),
    payercharge: z.number().min(0),
    transtype: z.string().min(1, "Transaction type is required"),
    upperbound: z.number().min(0),
});

// Define main form schema
// const formSchema = z.object({
//     id: z.number().min(1, "Charge ID is required"),
//     campaignid: z.number(),
//     chargeprofile: z.object({
//         chargeprofiledetails: z
//             .array(
//                 z.object({
//                     brand: z.string().min(1, "Brand is required"),
//                     brandgroup: z.string().min(1, "Brand group is required"),
//                     chargemodel: z.string().min(1, "Charge model is required"),
//                     chargetype: z.string().min(1, "Charge type is required"),
//                     lowerbound: z.number().min(0, "Lower bound must be 0 or greater"),
//                     merchantcharge: z.number().min(0, "Merchant charge must be 0 or greater"),
//                     payercharge: z.number().min(0, "Payer charge must be 0 or greater"),
//                     transtype: z.string().min(1, "Transaction type is required"),
//                     upperbound: z.number().min(0, "Upper bound must be 0 or greater"),
//                 })
//             )
//             .min(1, "At least one charge profile detail is required"),
//         createdby: z.number(),
//         description: z.string().min(1, "Description is required"),
//         isactive: z.boolean(),
//         name: z.string().min(1, "Name is required"),
//     }),
//     chargeprofileid: z.number().min(1, "Charge profile ID is required"),
//     chargeprofiletype: z.string().min(1, "Charge profile type is required"),
//     startdate: z.string().min(1, "Start date is required"),
//     enddate: z.string().min(1, "End date is required"),
//     createdby: z.number(),
// });
const formSchema = z.object({
    chargeprofiledetails: z
        .array(
            z.object({
                brand: z.string().min(1, "Brand is required"),
                brandgroup: z.string().min(1, "Brand group is required"),
                chargemodel: z.string().min(1, "Charge model is required"),
                chargetype: z.string().min(1, "Charge type is required"),
                lowerbound: z.number().min(0, "Lower bound must be 0 or greater"),
                merchantcharge: z.number().min(0, "Merchant charge must be 0 or greater"),
                payercharge: z.number().min(0, "Payer charge must be 0 or greater"),
                transtype: z.string().min(1, "Transaction type is required"),
                upperbound: z.number().min(0, "Upper bound must be 0 or greater"),
            })
        )
        .min(1, "At least one charge profile detail is required"),
    createdby: z.number(),
    description: z.string().min(1, "Description is required"),
    isactive: z.boolean(),
    name: z.string().min(1, "Name is required"),
});

type FormValues = z.infer<typeof formSchema>;

type ChargeProfileDetail = {
    brand: string;
    brandgroup: string;
    chargemodel: string;
    chargetype: string;
    id?: number;
    lowerbound: number;
    merchantcharge: number;
    payercharge: number;
    transtype: string;
    upperbound: number;
};

type StepType = {
    title: string;
    description: string;
};

export interface FormState {
    // campaignid: number;
    // chargeprofile: {
    chargeprofiledetails: ChargeProfileDetail[];
    createdby: number;
    createddate?: string;
    description: string;
    // id?: number;
    isactive: boolean;
    name: string;
    updatedby?: number;
    updateddate?: string;
    // };
    // chargeprofileid: number;
    // chargeprofiletype: string;
    // createdby: number;
    // createddate?: string;
    // enddate?: string;
    // // id?: number;
    // startdate?: string;
    // updatedby?: number;
    // updateddate?: string;
}

interface ChargeProfileStepperDialogProps {
    // onOpenChange: (open: boolean) => void;
    // onSubmit: (payload: FormState[]) => Promise<void> | void;
    user: any;
    campaignId: number;
    data?: any;
}

const initialState: FormState = {
    // campaignid: 0,
    // chargeprofile: {
    chargeprofiledetails: [],
    createdby: 0,
    description: "",
    createddate: "",
    isactive: true,
    name: "",
    // },
    // id: 0,
    // chargeprofileid: 0,
    // chargeprofiletype: "",
    // createdby: 0,
};
const steps: StepType[] = [
    { title: "Profile Details", description: "Enter profile name and description" },
    { title: "Charge Details", description: "Add charge profile details" },
    { title: "Review", description: "Review and confirm your entries" },
];

const AddSingleCharge: React.FC<ChargeProfileStepperDialogProps> = ({
    // onOpenChange,
    // onSubmit,
    user,
    data,
    campaignId,
}) => {
    // console.log("🚀 ~ data:", data);
    const userId = user?.useraccount?.userid ?? 0;
    const [activeStep, setActiveStep] = React.useState(0);
    const { data: chargeProfileTypes, isLoading: isLoadingTypes } = useFetchChargeProfileTypes();
    const { data: chargeTypes, isLoading: isLoadingChargeTypes } = useFetchChargeTypes();
    const { data: brands, isLoading: isLoadingBrands } = useFetchBrands();
    const { data: transTypes, isLoading: isLoadingTransTypes } = useFetchTransactionTypes();
    const { data: brandGroups, isLoading: isLoadingBrandGroups } = useFetchBrandGroups();
    const { data: chargeModels, isLoading: isLoadingChargeModels } = useFetchChargeModels();

    const { toast } = useToast();

    // console.log("brand groups", brandGroups);

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            chargeprofiledetails: [],
            createdby: userId,
            description: data?.description || "",
            isactive: data?.isactive || true,
            name: data?.name || "",
        },
    });

    const resetForm = () => {
        form.reset(initialState);
    };

    const handleNext = async () => {
        let isValid = false;
        switch (activeStep) {
            case 0:
                // Validate Profile Details
                isValid = await form.trigger(["name", "description", "isactive"]);
                break;
            case 1:
                // Validate Charge Details
                isValid = form.getValues()?.chargeprofiledetails?.length > 0;
                if (!isValid) {
                    form.setError("chargeprofiledetails", {
                        type: "manual",
                        message: "Please add at least one charge profile detail",
                    });
                }
                break;
            case 2:
                // Submit form
                try {
                    isValid = await form.trigger();
                    if (isValid) {
                        const currentDate = new Date().toISOString();
                        const payload = {
                            ...form.getValues(),
                            // createddate: currentDate,
                            // updateddate: currentDate,
                        };

                        // await onSubmit(payload);
                        // await createCampaignCharges(campaignId, payload);
                        if (data) {
                            const resp = await editChargesProfile(data?.id, payload);
                            if (resp.status === "success") {
                                toast({
                                    title: resp?.message,
                                    description: "Charge profile updated successfully!",
                                });
                            } else {
                                toast({
                                    title: resp?.message,
                                    description:
                                        "Failed to updating charge profile. Please try again!",
                                });
                            }
                            resetForm();
                            setChargeProfileDetails([]);
                        } else {
                            const resp = await chargesProfile(payload);
                            if (resp.status === "success") {
                                toast({
                                    title: resp?.message,
                                    description: "Charge profile created successfully!",
                                });
                            } else {
                                toast({
                                    title: resp?.message,
                                    description:
                                        "Failed to create charge profile. Please try again!",
                                });
                            }
                            resetForm();
                            setChargeProfileDetails([]);
                        }
                        return;
                    }
                } catch (error: any) {
                    const errorStatus = error?.response?.status;
                    const errorMessage =
                        error?.response?.data?.message || error.message || "Failed to add charge";

                    toast({
                        title: `Error (${errorStatus || "unknown"})`,
                        description: errorMessage,
                        variant: "destructive",
                    });
                    // console.error("Submission error:", error);
                }
                break;
        }

        if (isValid) {
            setActiveStep((prev) => prev + 1);
        }
    };

    const handleBack = () => {
        if (activeStep > 0) {
            setActiveStep((prev) => prev - 1);
        }
    };

    // Add state for charge profile details
    const [chargeProfileDetails, setChargeProfileDetails] = useState<ChargeProfileDetail[]>([]);

    // Add these new state and functions after the existing state declarations
    const [editingIndex, setEditingIndex] = useState<number | null>(null);

    const onEditDetail = (index: number) => {
        const detailToEdit = chargeProfileDetails[index];
        detailForm.reset(detailToEdit);
        setEditingIndex(index);
    };

    const onDeleteDetail = (index: number) => {
        const newDetails = chargeProfileDetails.filter((_, i) => i !== index);
        setChargeProfileDetails(newDetails);
        form.setValue("chargeprofiledetails", newDetails);
    };

    // Form for adding new charge profile detail
    const detailForm = useForm<ChargeProfileDetail>({
        resolver: zodResolver(chargeProfileDetailSchema),
        defaultValues: {
            brand: data?.chargeprofiledetails?.[0]?.brand || "",
            brandgroup: data?.chargeprofiledetails?.[0]?.brandgroup || "",
            chargemodel: data?.chargeprofiledetails?.[0]?.chargemodel || "",
            chargetype: data?.chargeprofiledetails?.[0]?.chargetype || "",
            lowerbound: data?.chargeprofiledetails?.[0]?.lowerbound ?? 0,
            merchantcharge: data?.chargeprofiledetails?.[0]?.merchantcharge ?? 0,
            payercharge: data?.chargeprofiledetails?.[0]?.payercharge ?? 0,
            transtype: data?.chargeprofiledetails?.[0]?.transtype || "",
            upperbound: data?.chargeprofiledetails?.[0]?.upperbound ?? 0,
        },
    });

    const onAddDetail = (data: ChargeProfileDetail) => {
        if (editingIndex !== null) {
            // Update existing detail
            const newDetails = [...chargeProfileDetails];
            newDetails[editingIndex] = data;
            setChargeProfileDetails(newDetails);
            form.setValue("chargeprofiledetails", newDetails);
            setEditingIndex(null);
        } else {
            // Add new detail
            setChargeProfileDetails((prev) => [...prev, data]);
            form.setValue("chargeprofiledetails", [...chargeProfileDetails, data]);
        }
        detailForm.reset();
    };

    // Step 1 Content
    // const renderBasicInfoStep = () => (
    //     <div className="px-4 space-y-4">
    //         <div className="grid grid-cols-1 gap-4">
    //             <FormField
    //                 control={form.control}
    //                 name="id"
    //                 render={({ field }) => (
    //                     <FormItem>
    //                         <FormLabel>Charge ID</FormLabel>
    //                         <FormControl>
    //                             <Input
    //                                 {...field}
    //                                 onChange={(e) => field.onChange(Number(e.target.value))}
    //                             />
    //                         </FormControl>
    //                         <FormMessage />
    //                     </FormItem>
    //                 )}
    //             />
    //         </div>
    //         <div className="grid grid-cols-2 gap-4">
    //             <FormField
    //                 control={form.control}
    //                 name="chargeprofileid"
    //                 render={({ field }) => (
    //                     <FormItem>
    //                         <FormLabel>Charge Profile ID</FormLabel>
    //                         <FormControl>
    //                             <Input
    //                                 {...field}
    //                                 onChange={(e) => field.onChange(Number(e.target.value))}
    //                             />
    //                         </FormControl>
    //                         <FormMessage />
    //                     </FormItem>
    //                 )}
    //             />

    //             <FormField
    //                 control={form.control}
    //                 name="chargeprofiletype"
    //                 render={({ field }) => (
    //                     <FormItem className="w-full">
    //                         <FormLabel>Charge Profile Type</FormLabel>
    //                         <Select onValueChange={field.onChange} value={field.value}>
    //                             <FormControl>
    //                                 <SelectTrigger className="w-full">
    //                                     <SelectValue placeholder="Select charge profile type" />
    //                                 </SelectTrigger>
    //                             </FormControl>
    //                             <SelectContent>
    //                                 {isLoadingTypes ? (
    //                                     <SelectItem value="loading">Loading...</SelectItem>
    //                                 ) : (
    //                                     chargeProfileTypes?.data?.map((type, index) => (
    //                                         <SelectItem key={index} value={type}>
    //                                             {type}
    //                                         </SelectItem>
    //                                     ))
    //                                 )}
    //                             </SelectContent>
    //                         </Select>
    //                         <FormMessage />
    //                     </FormItem>
    //                 )}
    //             />
    //         </div>

    //         <div className="grid grid-cols-2 gap-4">
    //             <FormField
    //                 control={form.control}
    //                 name="startdate"
    //                 render={({ field }) => (
    //                     <FormItem>
    //                         <FormLabel>Start Date</FormLabel>
    //                         <FormControl>
    //                             <Input type="datetime-local" {...field} className="" />
    //                         </FormControl>
    //                         <FormMessage />
    //                     </FormItem>
    //                 )}
    //             />

    //             <FormField
    //                 control={form.control}
    //                 name="enddate"
    //                 render={({ field }) => (
    //                     <FormItem>
    //                         <FormLabel>End Date</FormLabel>
    //                         <FormControl>
    //                             <Input type="datetime-local" {...field} />
    //                         </FormControl>
    //                         <FormMessage />
    //                     </FormItem>
    //                 )}
    //             />
    //         </div>
    //     </div>
    // );

    // Step 2 Content
    const renderProfileDetailsStep = () => (
        <div className="space-y-4 px-4">
            <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                    <FormItem>
                        <FormLabel>Profile Name</FormLabel>
                        <FormControl>
                            <Input {...field} />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                )}
            />

            <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                    <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                            <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                )}
            />

            <FormField
                control={form.control}
                name="isactive"
                render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                            <FormLabel>Active Status</FormLabel>
                        </div>
                        <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                    </FormItem>
                )}
            />
        </div>
    );

    // Render charge profile details table and form
    const renderChargeProfileDetailsStep = () => (
        <div className="space-y-6 px-4">
            <Card>
                <CardHeader className="my-4">
                    <CardTitle>Add Charge Profile Detail</CardTitle>
                </CardHeader>
                <CardContent>
                    <Form {...detailForm}>
                        {/* <form className="space-y-4"> */}
                        <div className="space-y-4">
                            <div className="grid grid-cols-4 gap-2">
                                {/* Brand */}
                                <FormField
                                    control={detailForm.control}
                                    name="brand"
                                    render={({ field }) => (
                                        <FormItem className="w-full">
                                            <FormLabel>Brand</FormLabel>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="w-full">
                                                        <SelectValue placeholder="Select brand" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {isLoadingBrands ? (
                                                        <SelectItem value="loading">
                                                            Loading...
                                                        </SelectItem>
                                                    ) : (
                                                        brands?.data?.map(
                                                            (brand: any, index: any) => (
                                                                <SelectItem
                                                                    key={index}
                                                                    value={brand}
                                                                >
                                                                    {brand}
                                                                </SelectItem>
                                                            )
                                                        )
                                                    )}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* Brand Group */}
                                <FormField
                                    control={detailForm.control}
                                    name="brandgroup"
                                    render={({ field }) => (
                                        <FormItem className="w-full">
                                            <FormLabel>Brand Group</FormLabel>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="w-full">
                                                        <SelectValue placeholder="Select brand group" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {isLoadingBrandGroups ? (
                                                        <SelectItem value="loading">
                                                            Loading...
                                                        </SelectItem>
                                                    ) : (
                                                        brandGroups?.data?.map(
                                                            (brandgroup: any, index: any) => (
                                                                <SelectItem
                                                                    key={index}
                                                                    value={brandgroup}
                                                                >
                                                                    {brandgroup}
                                                                </SelectItem>
                                                            )
                                                        )
                                                    )}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* Transaction Type */}
                                <FormField
                                    control={detailForm.control}
                                    name="transtype"
                                    render={({ field }) => (
                                        <FormItem className="w-full">
                                            <FormLabel>Transaction Type</FormLabel>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="w-full">
                                                        <SelectValue placeholder="Select Transaction Type" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {isLoadingTransTypes ? (
                                                        <SelectItem value="loading">
                                                            Loading...
                                                        </SelectItem>
                                                    ) : (
                                                        transTypes?.data?.map(
                                                            (type: any, index: any) => (
                                                                <SelectItem
                                                                    key={index}
                                                                    value={type}
                                                                >
                                                                    {type}
                                                                </SelectItem>
                                                            )
                                                        )
                                                    )}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                {/* Charge Type */}
                                <FormField
                                    control={detailForm.control}
                                    name="chargetype"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Charge Type</FormLabel>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="w-full">
                                                        <SelectValue placeholder="Select Charge Type" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {isLoadingChargeTypes ? (
                                                        <SelectItem value="loading">
                                                            Loading...
                                                        </SelectItem>
                                                    ) : (
                                                        chargeTypes?.data?.map(
                                                            (type: any, index: any) => (
                                                                <SelectItem
                                                                    key={index}
                                                                    value={type}
                                                                >
                                                                    {type}
                                                                </SelectItem>
                                                            )
                                                        )
                                                    )}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            {/* Charge Model */}
                            <div className="grid grid-cols-2 gap-4">
                                {/* <FormField
                                    control={detailForm.control}
                                    name="chargemodel"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Charge Model</FormLabel>
                                            <FormControl>
                                                <Input {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                /> */}

                                {/* Charge Type */}

                                {/* <FormField
                                    control={detailForm.control}
                                    name="chargetype"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Charge Type</FormLabel>
                                            <FormControl>
                                                <Input {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                /> */}
                            </div>

                            {/* Lower Bound */}
                            <div className="grid grid-cols-3 gap-4">
                                <FormField
                                    control={detailForm.control}
                                    name="lowerbound"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Lower Bound</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    onChange={(e) =>
                                                        field.onChange(Number(e.target.value))
                                                    }
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={detailForm.control}
                                    name="upperbound"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Upper Bound</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    onChange={(e) =>
                                                        field.onChange(Number(e.target.value))
                                                    }
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={detailForm.control}
                                    name="chargemodel"
                                    render={({ field }) => (
                                        <FormItem className="w-full">
                                            <FormLabel>Charge Model</FormLabel>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="w-full">
                                                        <SelectValue placeholder="Select Charge Model" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {isLoadingChargeModels ? (
                                                        <SelectItem value="loading">
                                                            Loading...
                                                        </SelectItem>
                                                    ) : (
                                                        chargeModels?.data?.map(
                                                            (type: any, index: any) => (
                                                                <SelectItem
                                                                    key={index}
                                                                    value={type}
                                                                >
                                                                    {type}
                                                                </SelectItem>
                                                            )
                                                        )
                                                    )}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={detailForm.control}
                                    name="merchantcharge"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Merchant Charge</FormLabel>
                                            <FormControl>
                                                <Input
                                                    // type="number"
                                                    {...field}
                                                    onChange={(e) =>
                                                        field.onChange(Number(e.target.value))
                                                    }
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={detailForm.control}
                                    name="payercharge"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Payer Charge</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    onChange={(e) =>
                                                        field.onChange(Number(e.target.value))
                                                    }
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <Button type="submit" onClick={detailForm.handleSubmit(onAddDetail)}>
                                {editingIndex !== null ? "Update Detail" : "Add Detail"}
                            </Button>
                        </div>

                        {/* </form> */}
                    </Form>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="my-4">
                    <CardTitle>Charge Profile Details</CardTitle>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader className="bg-gray-100 dark:bg-gray-800">
                            <TableRow className="">
                                <TableHead className="text-nowrap">Brand</TableHead>
                                <TableHead className="text-nowrap">Brand Group</TableHead>
                                <TableHead className="text-nowrap">Charge Model</TableHead>
                                <TableHead className="text-nowrap">Charge Type</TableHead>
                                <TableHead className="text-nowrap">Lower Bound</TableHead>
                                <TableHead className="text-nowrap">Upper Bound</TableHead>
                                <TableHead className="text-nowrap">Merchant Charge</TableHead>
                                <TableHead className="text-nowrap">Payer Charge</TableHead>
                                <TableHead className="text-nowrap">Transaction Type</TableHead>
                                <TableHead className="text-nowrap">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {chargeProfileDetails.map((detail, index) => (
                                <TableRow key={index}>
                                    <TableCell className="text-nowrap">{detail.brand}</TableCell>
                                    <TableCell className="text-nowrap">
                                        {detail.brandgroup}
                                    </TableCell>
                                    <TableCell className="text-nowrap">
                                        {detail.chargemodel}
                                    </TableCell>
                                    <TableCell className="text-nowrap">
                                        {detail.chargetype}
                                    </TableCell>
                                    <TableCell className="text-nowrap">
                                        {detail.lowerbound}
                                    </TableCell>
                                    <TableCell className="text-nowrap">
                                        {detail.upperbound}
                                    </TableCell>
                                    <TableCell className="text-nowrap">
                                        {detail.merchantcharge}
                                    </TableCell>
                                    <TableCell className="text-nowrap">
                                        {detail.payercharge}
                                    </TableCell>
                                    <TableCell className="text-nowrap">
                                        {detail.transtype}
                                    </TableCell>
                                    <TableCell className="text-nowrap">
                                        <div className="flex gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => onEditDetail(index)}
                                            >
                                                Edit
                                            </Button>
                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => onDeleteDetail(index)}
                                            >
                                                Delete
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                    {form.formState.errors?.chargeprofiledetails && (
                        <p className="mt-2 text-sm text-red-500">
                            {form.formState.errors.chargeprofiledetails.message}
                        </p>
                    )}
                </CardContent>
            </Card>
        </div>
    );

    // Update the renderStepContent function
    const renderStepContent = () => {
        switch (activeStep) {
            case 0:
                return renderProfileDetailsStep();
            case 1:
                return renderChargeProfileDetailsStep();
            case 2:
                return (
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-center text-3xl font-extrabold text-primary">
                                    Review Your Submission
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {/* <h3 className="my-3 text-lg font-extrabold text-gray-800 uppercase dark:text-gray-50">
                                        Basic Information
                                    </h3>
                                    <div className="grid grid-cols-2 gap-4 pb-4 border-b border-gray-200">
                                        <div>
                                            <p className="font-bold text-gray-700 uppercase dark:text-gray-50">
                                                Charge ID
                                            </p>
                                            <p>{form.getValues("id")}</p>
                                        </div>
                                        <div>
                                            <p className="font-bold text-gray-700 uppercase dark:text-gray-50">
                                                Campaign Id{" "}
                                            </p>
                                            <p>{form.getValues("campaignid")}</p>
                                        </div>
                                        <div>
                                            <p className="font-bold text-gray-700 uppercase dark:text-gray-50">
                                                Charge Profile ID
                                            </p>
                                            <p>{form.getValues("chargeprofileid")}</p>
                                        </div>
                                        <div>
                                            <p className="font-bold text-gray-700 dark:text-gray-50">
                                                Charge Profile Type
                                            </p>
                                            <p>{form.getValues("chargeprofiletype")}</p>
                                        </div>
                                        <div>
                                            <p className="font-bold text-gray-700 dark:text-gray-50">
                                                Created By{" "}
                                            </p>
                                            <p>{form.getValues("createdby")}</p>
                                        </div>

                                        <div>
                                            <p className="font-bold text-gray-700 dark:text-gray-50">
                                                Start Date
                                            </p>
                                            <p>{form.getValues("startdate")}</p>
                                        </div>

                                        <div>
                                            <p className="font-bold text-gray-700 uppercase dark:text-gray-50">
                                                End Date
                                            </p>
                                            <p>{form.getValues("enddate")}</p>
                                        </div>
                                    </div> */}

                                    <h3 className="my-3 text-lg font-extrabold uppercase text-gray-800 dark:text-gray-50">
                                        Profile Details
                                    </h3>
                                    <div className="grid grid-cols-2 gap-4 border-b border-gray-200 pb-4">
                                        <div>
                                            <p className="font-bold uppercase text-gray-700 dark:text-gray-50">
                                                Name
                                            </p>
                                            <p>{form.getValues("name")}</p>
                                        </div>

                                        <div>
                                            <p className="font-bold uppercase text-gray-700 dark:text-gray-50">
                                                Is Active
                                            </p>
                                            <p>{form.getValues("isactive") ? "Yes" : "No"}</p>
                                        </div>
                                        <div>
                                            <p className="font-bold uppercase text-gray-700 dark:text-gray-50">
                                                Description
                                            </p>
                                            <p>{form.getValues("description")}</p>
                                        </div>
                                    </div>

                                    <h3 className="my-3 text-lg font-extrabold uppercase text-gray-800 dark:text-gray-50">
                                        Charge Profile Details
                                    </h3>
                                    <Table>
                                        <TableHeader className="bg-gray-100 dark:bg-gray-800">
                                            <TableRow>
                                                <TableHead className="text-nowrap">Brand</TableHead>
                                                <TableHead className="text-nowrap">
                                                    Brand Group
                                                </TableHead>
                                                <TableHead className="text-nowrap">
                                                    Charge Model
                                                </TableHead>
                                                <TableHead className="text-nowrap">
                                                    Charge Type
                                                </TableHead>
                                                <TableHead className="text-nowrap">
                                                    Lower Bound
                                                </TableHead>
                                                <TableHead className="text-nowrap">
                                                    Upper Bound
                                                </TableHead>
                                                <TableHead className="text-nowrap">
                                                    Merchant Charge
                                                </TableHead>
                                                <TableHead className="text-nowrap">
                                                    Payer Charge
                                                </TableHead>
                                                <TableHead className="text-nowrap">
                                                    Transaction Type
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            <>
                                                {chargeProfileDetails.length > 0 ? (
                                                    chargeProfileDetails.map((detail, index) => (
                                                        <TableRow key={index}>
                                                            <TableCell className="text-nowrap">
                                                                {detail.brand}
                                                            </TableCell>
                                                            <TableCell className="text-nowrap">
                                                                {detail.brandgroup}
                                                            </TableCell>
                                                            <TableCell className="text-nowrap">
                                                                {detail.chargemodel}
                                                            </TableCell>
                                                            <TableCell className="text-nowrap">
                                                                {detail.chargetype}
                                                            </TableCell>
                                                            <TableCell className="text-nowrap">
                                                                {detail.lowerbound}
                                                            </TableCell>
                                                            <TableCell className="text-nowrap">
                                                                {detail.upperbound}
                                                            </TableCell>
                                                            <TableCell className="text-nowrap">
                                                                {detail.merchantcharge}
                                                            </TableCell>
                                                            <TableCell className="text-nowrap">
                                                                {detail.payercharge}
                                                            </TableCell>
                                                            <TableCell className="text-nowrap">
                                                                {detail.transtype}
                                                            </TableCell>
                                                        </TableRow>
                                                    ))
                                                ) : (
                                                    <TableRow>
                                                        <TableCell
                                                            colSpan={9}
                                                            className="text-center"
                                                        >
                                                            No data
                                                        </TableCell>
                                                    </TableRow>
                                                )}
                                            </>
                                        </TableBody>
                                    </Table>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <Card>
            <CardContent className="max-h-screen w-full overflow-y-auto overflow-x-hidden p-10">
                {/* <DialogHeader> */}
                <p className="flex items-center justify-center py-5 text-lg font-bold">
                    {data ? "Edit Charge Profile" : "Create New Charge Profile"}
                </p>
                {/* </DialogHeader> */}

                <div className="overflow-x-auto py-4">
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            {steps.map((step, index) => (
                                <div key={index} className="flex items-center">
                                    <div
                                        className={`flex flex-col items-center ${index === activeStep ? "text-primary" : "text-gray-400"}`}
                                    >
                                        <div
                                            className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                                                index === activeStep
                                                    ? "border-primary bg-blue-50"
                                                    : "border-gray-300"
                                            }`}
                                        >
                                            {index + 1}
                                        </div>
                                        <div className="mt-2 text-sm font-medium">{step.title}</div>
                                    </div>
                                    {index < steps.length - 1 && (
                                        <div className="mx-4 h-0.5 w-32 bg-gray-200" />
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>

                    <Form {...form}>
                        <form className="space-y-6">
                            {renderStepContent()}

                            <div className="flex justify-between">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleBack}
                                    disabled={activeStep === 0}
                                    className="flex items-center gap-2"
                                >
                                    <ChevronLeft size={16} />
                                    Back
                                </Button>

                                <Button
                                    type="button"
                                    onClick={handleNext}
                                    className="flex items-center gap-2"
                                    disabled={form.formState.isSubmitting}
                                >
                                    {form.formState.isSubmitting ? (
                                        <span className="flex items-center justify-center">
                                            <svg
                                                className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                            >
                                                <circle
                                                    className="opacity-25"
                                                    cx="12"
                                                    cy="12"
                                                    r="10"
                                                    stroke="currentColor"
                                                    strokeWidth="4"
                                                ></circle>
                                                <path
                                                    className="opacity-75"
                                                    fill="currentColor"
                                                    d="M4 12a8 8 0 018-8v8H4z"
                                                ></path>
                                            </svg>
                                            Loading...
                                        </span>
                                    ) : (
                                        <>
                                            {activeStep === steps.length - 1
                                                ? data
                                                    ? "Update"
                                                    : "Submit"
                                                : "Next"}
                                            {activeStep !== steps.length - 1 && (
                                                <ChevronRight size={16} />
                                            )}
                                        </>
                                    )}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </div>
            </CardContent>
        </Card>
    );
};

export default AddSingleCharge;
