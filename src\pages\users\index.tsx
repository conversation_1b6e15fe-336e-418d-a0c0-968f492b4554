// import { DashboardCardComponent } from "@/components/dashboard/DashboardCards";
import React, { useId, useEffect } from "react";

import DashboardCard from "@/components/dashboard/DashboardCards";
import { DashboardChart } from "@/components/dashboard/DashboardChart";
import { SearchComponent } from "@/components/dashboard/Search";
import TransactionsTable from "@/components/dashboard/transactions-table";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContexts";
import { useNavigate, useNavigation } from "react-router-dom";
import { Wallet, Donut, Users2 } from "lucide-react";
import { useFetchUsers } from "@/hooks/usersHooks/use-fetch-users";
import { totalUsers } from "@/lib/utils";
import { filter } from "lodash";

import BreadCrumbComponent from "@/components/BreadCrumbComponent";
import ServerSideUsersTable from "./users-table/server-side-table";
// TODO: Debug the filtering and rows per page bug

function UsersPage() {
    // const id = useId();
    // const { data, isLoading, isError } = useFetchUsers(50);
    const {
        data,
        pagination,
        keyword,
        sorting,
        isLoading,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
    } = useFetchUsers(50);

    // console.log("data", data);

    const memoizedUserObject = React.useMemo(() => data, [data?.content]);

    const transformedData = data ? data?.content : [];
    const pageCount = data ? data?.totalElements : 0;

    const companies = filter(transformedData, { iscompany: true });
    const clients = filter(transformedData, { iscompany: false });

    const totalNumberOfCompanies = totalUsers(companies, "userid");

    const totalNumberOfCustomers = totalUsers(clients, "userid");

    const totalNumberOfClients = totalUsers(transformedData, "userid");

    const dashInfo = [
        {
            id: 1,
            icon: Wallet,

            iconColor: "text-blue-500",
            iconBgColor: "bg-blue-100",
            title: "Total Users",
            value: pageCount,
            selectOptions: [
                { label: "Today", value: "today" },
                { label: "Weekly", value: "weekly" },
                { label: "Monthly", value: "monthly" },
            ],
        },

        {
            id: 2,
            icon: Donut,

            iconColor: "text-green-500",
            iconBgColor: "bg-green-100",
            title: "Total Companies",
            value: totalNumberOfCompanies,
            menuOptions: [{ label: "View", action: () => console.log("Edit clicked") }],
        },

        {
            id: 3,
            icon: Users2,

            iconColor: "text-red-500",
            iconBgColor: "bg-red-100",
            title: "Total Clients",
            value: totalNumberOfCustomers,
            menuOptions: [{ label: "View", action: () => console.log("Edit clicked") }],
        },
    ];

    const breadcrumbItems = [{ label: "Home", href: "/dashboard" }, { label: "Users" }];

    return (
        <>
            <div className="space-y-4 px-6 py-4">
                <BreadCrumbComponent items={breadcrumbItems} />

                <div className="grid grid-cols-4 gap-x-8">
                    {dashInfo.map((dash) => (
                        <DashboardCard
                            key={dash.id}
                            icon={dash.icon}
                            iconColor={dash.iconColor}
                            iconBgColor={dash.iconBgColor}
                            title={dash.title}
                            value={dash.value}
                            selectOptions={dash.selectOptions}
                        />
                    ))}
                </div>

                <ServerSideUsersTable
                    data={memoizedUserObject}
                    title="Users"
                    loading={isLoading}
                    filterable={true}
                    pagination={pagination}
                    bulk="adduser"
                    pageCount={data?.totalPages || 0}
                    keyword={keyword}
                    onPaginationChange={onPaginationChange}
                    onKeywordChange={onKeywordChange}
                    onSortingChange={onSortingChange}
                    sorting={sorting}
                />
            </div>
        </>
    );
}

export default UsersPage;
