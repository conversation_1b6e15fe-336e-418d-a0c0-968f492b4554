import { useMacAddress } from "@/hooks/use-macaddress";
import { AuthProviderProps, LoginRequest, LoginResponse } from "@/lib/interfaces";
import React, { createContext, useContext, useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import useDemoToggle from "@/hooks/use-demo";
import { setApiBaseUrl } from "@/api/api";
import { useLoginUser } from "@/hooks/usersHooks/use-auth";
import { PermissionService } from "@/lib/PermissionService";
// import { PermissionService } from "@/services/PermissionService";

interface AuthContextType {
    user: LoginResponse | null;
    login: (data: LoginRequest) => Promise<void>;
    logout: () => void;
    macAddress: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
    const [user, setUser] = useState<LoginResponse | null>(null);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [permissions, setPermissions] = useState([]);
    const queryClient = useQueryClient();
    const { macAddress } = useMacAddress();
    const loginMutation = useLoginUser(false);
    const demoLoginMutation = useLoginUser(true);

    const navigate = useNavigate();
    const { isDemo } = useDemoToggle();

    useEffect(() => {
        setApiBaseUrl(isDemo);
    }, [isDemo]);

    useEffect(() => {
        try {
            const storedUser = localStorage.getItem("user");
            const storedPermissions = localStorage.getItem("userPermissions");
            const checkTokenActiveState = localStorage.getItem("tokenState");

            if (storedUser) {
                setUser(JSON.parse(storedUser));
            }

            if (storedPermissions) {
                const parsedPermissions = JSON.parse(storedPermissions);
                setPermissions(parsedPermissions);
                PermissionService.setPermissions(parsedPermissions);
                setIsAuthenticated(true);
            }

            if (checkTokenActiveState === "expired") {
                setUser(null);
                setPermissions([]);
                setIsAuthenticated(false);
                localStorage.removeItem("user");
                localStorage.removeItem("userPermissions");
                queryClient.removeQueries(["user"]);
                queryClient.removeQueries(["token"]);
                localStorage.removeItem("token");
                navigate("/");
            }
        } catch (error) {
            console.log("error from unconfirmed balances page=>", error);
        }
    }, []);

    const login = async (data: LoginRequest) => {
        const userData = await loginMutation.mutateAsync(data);
        const demoUserData = await demoLoginMutation.mutateAsync(data);
        const currentTime = new Date();
        const expiresOn = new Date(userData.accessToken.expiresOn);

        if (expiresOn <= currentTime) {
            throw new Error("Token expired");
        }

        setUser(userData);
        const userPermissions = userData.useraccount.profile.permissions;
        setPermissions(userPermissions);
        setIsAuthenticated(true);
        localStorage.setItem("user", JSON.stringify(userData.useraccount));
        localStorage.setItem("token", userData.accessToken.token);
        localStorage.setItem("userPermissions", JSON.stringify(userPermissions));
        queryClient.setQueryData(["user"], userData.useraccount);
        queryClient.setQueryData(["token"], userData.accessToken.token);

        // Set up permissions
        PermissionService.setPermissions(userPermissions);
    };

    const logout = () => {
        setUser(null);
        setPermissions([]);
        setIsAuthenticated(false);
        localStorage.removeItem("user");
        localStorage.removeItem("userPermissions");
        queryClient.removeQueries(["user"]);
        queryClient.removeQueries(["token"]);
        localStorage.removeItem("token");
        navigate("/");
    };

    return (
        <AuthContext.Provider value={{ user, login, logout, macAddress }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = (): AuthContextType => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
};
