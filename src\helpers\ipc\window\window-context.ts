import {
    WIN_MINIMIZE_CHANNEL,
    WIN_MAXIMIZE_CHANNEL,
    WIN_CLOSE_CHANNEL,
    GET_MAC_ADDRESS,
    GO_BACK,
    GO_FORWARD,
    CAN_GO_FORWARD,
    CAN_GO_BACK,
} from "./window-channels";

export function exposeWindowContext() {
    const { contextBridge, ipcRenderer } = window.require("electron");
    contextBridge.exposeInMainWorld("electronWindow", {
        minimize: () => ipcRenderer.invoke(WIN_MINIMIZE_CHANNEL),
        maximize: () => ipcRenderer.invoke(WIN_MAXIMIZE_CHANNEL),
        close: () => ipcRenderer.invoke(WIN_CLOSE_CHANNEL),
    });
    contextBridge.exposeInMainWorld("electron", {
        getMacAddress: () => ipcRenderer.invoke(GET_MAC_ADDRESS),
        onMacAddress: (callback: (mac: string) => void) =>
            ipcRenderer.on("mac-address-response", (event, mac) => callback(mac)),

        goBack: () => ipcRenderer.invoke(GO_BACK),
        goForward: () => ipcRenderer.invoke(GO_FORWARD),
        canGoForward: () => ipcRenderer.invoke(CAN_GO_FORWARD),
        canGoBack: () => ipcRenderer.invoke(CAN_GO_BACK),
        readExcelFile: async (filePath: string) => {
            return await ipcRenderer.invoke("read-excel-file", filePath);
        },
    });
}
