import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useAssignCharge, useSimulateCharge } from "@/hooks/campaignHooks/use-simulate-charge";
import {
    useFetchChargeProfiles,
    useFetchChargeProfileTypes,
} from "@/hooks/optionsHooks/use-fetch-chargeprofiletypes";
import { useAuth } from "@/contexts/AuthContexts";
import { FormCombobox } from "./form-combobox";
import { AddSquare } from "iconsax-react";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { useFetchCampaignProfile } from "@/hooks/campaignHooks/use-fetch-campaigns";
const formSchema = z.object({
    chargeprofiletype: z.string({
        required_error: "Charge profile type is required",
    }),
    chargeprofile: z.object({}), // Make it flexible to accept any object
    startdate: z.string({
        invalid_type_error: "Invalid type passed",
        required_error: "Start date is required",
    }),
    enddate: z.string().optional(),
});

interface SimulateChargeFormProps {
    campaign: any;
    onClose: () => void;
}

function formatDateTime(inputDateTime: string): string {
    const date = new Date(inputDateTime);

    const formattedDate = date.toISOString().slice(0, 19).replace("T", " ") + ".585";

    return formattedDate;
}

function AssignChargeForm({ campaign, onClose }: SimulateChargeFormProps) {
    const [searchTerm, setSearchTerm] = useState("");

    const [selectedChargeProfile, setSelectedChargeProfile] = useState<any>(null);
    const [isOpen, setIsOpen] = useState(false);

    const { user } = useAuth();
    const userId = user?.useraccount?.userid ?? 0;

    const { toast } = useToast();
    const [showSearch, setShowSearch] = useState(false);
    const { mutate: assignCharge, isPending: isAssigning } = useAssignCharge();
    const { data: chargeProfileTypes, isLoading: isLoadingTypes } = useFetchChargeProfileTypes();
    // const { data: chargeProfiles, isLoading: isLoadingProfiles } =
    //     useFetchChargeProfiles(searchTerm);
    const {
        data: chargeProfiles,
        pagination,
        keyword,
        sorting,
        isLoading: isLoadingProfiles,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
    } = useFetchCampaignProfile(100);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            chargeprofiletype: "",
            chargeprofile: {},
        },
    });

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        const payload = {
            campaignid: campaign?.id,
            // chargeprofile: selectedChargeProfile || {},
            chargeprofileid: selectedChargeProfile?.id || "",
            chargeprofiletype: form.getValues().chargeprofiletype,
            // createdby: userId,
            // enddate: form.getValues().enddate,
            startdate: formatDateTime(form.getValues().startdate),
        };
        assignCharge(
            {
                accountId: campaign?.id,
                campaignData: [payload],
            },
            {
                onSuccess: () => {
                    // form.reset();
                    toast({ title: "Success", description: "Charge assigned successfully" });
                    // form.reset();
                    form.resetField("chargeprofile");
                    form.resetField("chargeprofiletype");
                    form.resetField("startdate");
                    form.resetField("enddate");

                    // onClose();

                    setIsOpen(false);
                },
                onError: (error: any) => {
                    toast({
                        title: "Error",
                        description: error.message || "Failed to assign charge",
                    });
                    form.reset();
                },
            }
        );
    };

    const handleChargeProfileChange = (selectedValue: string) => {
        // Find the selected charge profile and set its details
        const profile = chargeProfiles?.content?.find(
            (profile: any) => profile.name === selectedValue
        );
        if (profile) {
            setSelectedChargeProfile(profile);
            form.setValue("chargeprofile", profile);
        } else {
            setSelectedChargeProfile(null);
            form.setValue("chargeprofile", {});
        }
    };

    // const debouncedSearch = useCallback(
    //     debounce((value: string) => {
    //         onKeywordChange(value);
    //     }, 500),
    //     [onKeywordChange]
    // );

    const isLoading = form.formState.isSubmitting || isAssigning;

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <AddSquare className="h-4 w-4 text-teal-500" />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">Assign Charge</DialogTitle>
                        <DialogDescription>Assign a charge for this campaign</DialogDescription>
                    </DialogHeader>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="chargeprofiletype"
                                render={({ field }) => (
                                    <FormItem className="w-full">
                                        <FormLabel>Charge Profile Type</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value ?? ""}
                                        >
                                            <FormControl>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select charge profile type" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {isLoadingTypes ? (
                                                    <SelectItem value="loading">
                                                        Loading...
                                                    </SelectItem>
                                                ) : (
                                                    chargeProfileTypes?.data?.map(
                                                        (type: any, index: any) => (
                                                            <SelectItem key={index} value={type}>
                                                                {type}
                                                            </SelectItem>
                                                        )
                                                    )
                                                )}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormCombobox
                                form={form}
                                name="chargeprofile"
                                label="Charge Profile"
                                // data={formattedProfiles}
                                data={chargeProfiles?.content || []}
                                isLoading={isLoadingProfiles}
                                onSearch={setSearchTerm}
                                onSelect={handleChargeProfileChange}
                                selectedValue={selectedChargeProfile}
                                onKeywordChange={onKeywordChange}
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="startdate"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Start Date</FormLabel>
                                        <FormControl>
                                            <Input type="datetime-local" {...field} className="" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="enddate"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>End Date</FormLabel>
                                        <FormControl>
                                            <Input type="datetime-local" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <Button
                            type="submit"
                            size="default"
                            className="w-full"
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <span className="flex items-center justify-center">
                                    <svg
                                        className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                    >
                                        <circle
                                            className="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            strokeWidth="4"
                                        ></circle>
                                        <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8v8H4z"
                                        ></path>
                                    </svg>
                                    Loading...
                                </span>
                            ) : (
                                "Assign Charge"
                            )}
                        </Button>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}

export default React.memo(AssignChargeForm);
