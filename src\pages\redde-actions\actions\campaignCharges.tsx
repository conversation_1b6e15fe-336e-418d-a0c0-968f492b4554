// AddUser.js
import React, { useState } from "react";

import {
    useFetchCampaignProfile,
    useFetchCampaigns,
} from "@/hooks/campaignHooks/use-fetch-campaigns";
import _ from "lodash";
import ServerSideCampaignChargesTable from "@/components/campaigns/campaigns-table/campaignCharges";

const CampaignCharges: React.FC = () => {
    const {
        data,
        pagination,
        keyword,
        sorting,
        isLoading,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
    } = useFetchCampaignProfile(100);

    const campaignProfile = _.filter(data?.content);
    // console.log("🚀 ~ campaignProfile:", campaignProfile);

    //: TODO: Add filter to the table
    //: TODO: Add a  view campaign details page
    //: TODO: Add a modal to edit a campaign

    return (
        <div className="">
            <ServerSideCampaignChargesTable
                data={campaignProfile}
                title="Campaign Profiles"
                loading={isLoading}
                filterable={true}
                pagination={pagination}
                pageCount={data?.totalPages || 0}
                keyword={keyword}
                sorting={sorting}
                onPaginationChange={onPaginationChange}
                onKeywordChange={onKeywordChange}
                onSortingChange={onSortingChange}
            />
        </div>
    );
};

export default CampaignCharges;
