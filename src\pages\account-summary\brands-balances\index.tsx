import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import React, { useEffect, useId, useState } from "react";

import {
   
    NetworkIcon,
    UserPlus,
} from "lucide-react";
// import { DataTable } from "@/components/Customtable/data-table";
import { CardComponent } from "../confirmed-balances/Card";
import Banner from "@/components/Banner";
import { useAuth } from "@/contexts/AuthContexts";
import { useBrandsTodayBalances } from "@/hooks/accountsHooks/use-brandstoday";
import { useCurrencyFormatter } from "@/hooks/use-currrency";




function BrandsBalances() {
   
   const { user } = useAuth();

   const id = useId()

   const accountID = user?.useraccount?.accountid;

   const { data, isLoading } = useBrandsTodayBalances(accountID);

    const transformData = (data: any[]): any[] => {
        return data.map((item) => ({
            balance: item[0],
            network: item[1],
            
        }));
    };



   const transformedData = data ? transformData(data.data.flat()) : [];
//    console.log("brandstoday", transformedData);

    
    return (
        <div className="mx-0 w-full space-y-5">
            <Banner
                title="Today's Balance Per Brand"
                subTitle="Note: Report include today's transactions"
            />
            <main className="flex flex-1 flex-col">
                <div className="grid grid-cols-5 gap-6">
                    {transformedData.map((transData) => (
                        <CardComponent
                            title={transData.network}
                            key={id}
                            value={useCurrencyFormatter(transData.balance)}
                            icon={<NetworkIcon className="h-4 w-4 text-blue-500" />}
                        />
                    ))}
                </div>
            </main>
        </div>
    );
}

export default BrandsBalances;
