import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { useNavigate } from "react-router-dom";
import { Textarea } from "../ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useCreateCampaign } from "@/hooks/campaignHooks/use-create-campaign";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "../ui/dialog";
import { PlusIcon } from "lucide-react";

const formSchema = z.object({
    accountid: z.number(),
    allowcreditapi: z.boolean(),
    description: z.string().optional(),
    legacyusername: z.string().optional().nullable(),
    enableotp: z.boolean().optional(),
    isactive: z.boolean().optional(),
    callbackurl: z.string().optional(),
    isenhanced: z.boolean(),
    itcapikey: z
        .string({
            required_error: "ITCAPI Key is required",
        })
        .min(1, "ITCAPI Key is required"),
    itcmerchantid: z
        .string({
            required_error: "ITCMerchant ID is required",
        })
        .min(1, "Merchant ID is required"),
    itcproductid: z
        .string({
            required_error: "Product ID is required",
        })
        .min(1, "Product ID is required"),
    senderid: z.string().optional().nullable(),
});

function AddCampaignForm(data: any) {
    // const { login, macAddress, user } = useAuth();
    const [isModalOpen, setIsModalOpen] = useState(false);

    const { toast } = useToast();
    // const navigate = useNavigate();
    const createCampaign = useCreateCampaign();

    // Add console.log to debug the data prop
    // console.log("Form data prop:", data);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            accountid: data?.data?.accountid,
            allowcreditapi: false,
            description: "",
            legacyusername: null,
            enableotp: false,
            isactive: true,
            isenhanced: false,
            itcapikey: "",
            itcmerchantid: "",
            itcproductid: "",
            senderid: null,
            callbackurl: "",
        },
    });

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        // Transform empty strings to null for legacyusername and senderid
        const transformedValues = {
            ...values,
            legacyusername: values.legacyusername?.trim() || null,
            senderid: values.senderid?.trim() || null,
        };

        try {
            const result = await createCampaign.mutateAsync(transformedValues);

            toast({
                title: "Success",
                description: "Campaign created successfully",
            });

            setIsModalOpen(false);
            // navigate("/campaigns");
        } catch (error: any) {
            const errorStatus = error?.response?.status;
            const errorMessage =
                error?.response?.data?.message || error.message || "Failed to create campaign";

            toast({
                title: `Error (${errorStatus || "unknown"})`,
                description: errorMessage,
                variant: "destructive",
            });

            setIsModalOpen(false);
        }
    };

    // React Query provides the isLoading state automatically through your useAuth hook.
    const isLoading = form.formState.isSubmitting;

    return (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <PlusIcon className="h-4 w-4 text-green-500" />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">
                            Add New Campaign
                        </DialogTitle>
                        <DialogDescription className="">
                            Add a new campaign for{" "}
                            <span className="font-bold uppercase text-primary">
                                {data.data.name}
                            </span>
                        </DialogDescription>
                    </DialogHeader>
                </DialogHeader>
                <div>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <FormField
                                control={form.control}
                                name="legacyusername"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Legacy Username
                                        </FormLabel>
                                        <Input
                                            type="text"
                                            {...field}
                                            placeholder="Legacy Username"
                                        />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="itcmerchantid"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">
                                                ITC Merchant ID / Transflow
                                            </FormLabel>
                                            <Input
                                                type="text"
                                                {...field}
                                                placeholder=" Merchant ID"
                                            />
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="itcproductid"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">
                                                ITC Product ID
                                            </FormLabel>
                                            <Input
                                                type="text"
                                                {...field}
                                                placeholder=" Product ID"
                                            />
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <FormField
                                control={form.control}
                                name="callbackurl"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Settlement Callback Url
                                        </FormLabel>
                                        <Input type="text" {...field} placeholder="Callback Url" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="itcapikey"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            {" "}
                                            ITC Api Key
                                        </FormLabel>
                                        <Input type="text" {...field} placeholder=" Api Key" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="senderid"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Sender ID</FormLabel>
                                        <Input type="text" {...field} placeholder="Sender ID" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel
                                            className="dark:text-white"
                                            htmlFor="description"
                                        >
                                            Description
                                        </FormLabel>
                                        <Textarea {...field} placeholder="Description" />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="enableotp"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-center justify-between p-4">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">Enable OTP</FormLabel>
                                            <FormDescription>
                                                Note: enabling this will activate One Time Password
                                                feature on this campaign
                                            </FormDescription>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="isactive"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-center justify-between p-2">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base"> Is Active</FormLabel>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="isenhanced"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-center justify-between p-2">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">
                                                {" "}
                                                Is Enhanced
                                            </FormLabel>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="allowcreditapi"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-center justify-between p-2">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">
                                                {" "}
                                                Allow Credit API
                                            </FormLabel>
                                            <FormDescription>
                                                Note: enabling this will activate One Time Password
                                                feature on this campaign
                                            </FormDescription>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <Button
                                type="submit"
                                size="default"
                                className="w-full"
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <span className="flex items-center justify-center">
                                        <svg
                                            className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v8H4z"
                                            ></path>
                                        </svg>
                                        Loading...
                                    </span>
                                ) : (
                                    "Submit"
                                )}
                            </Button>
                        </form>
                    </Form>
                </div>
            </DialogContent>
        </Dialog>
    );
}

export default React.memo(AddCampaignForm);
