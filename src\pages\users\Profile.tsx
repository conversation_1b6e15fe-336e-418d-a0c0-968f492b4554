import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Wallet2, Wallet2Icon, Wallet } from "lucide-react";
import { useLocation } from "react-router-dom";
import { UsersProps } from "./users-table";

import DashboardCard from "@/components/dashboard/DashboardCards";
import Sidebar from "./sidebar";
import Banner from "./Banner";
import Transaction from "./Transaction";
import Cashout from "./Cashout";
import Apps from "./Apps";
import UserProfileWindow from "./UserProfile";
import { useFetchUser } from "@/hooks/usersHooks/use-fetch-user";
import { useFetchUserBalance } from "@/hooks/accountsHooks/use-balance";
import { formatCurrency } from "@/lib/utils";
import SkeletonComponent from "../account-summary/unconfirmed-balances/skeleton";
import { useFetchTransactions } from "@/hooks/accountsHooks/use-fetch-transactions";
import TransactionsTable from "@/components/dashboard/transactions-table";
import { useFetchApps } from "@/hooks/campaignHooks/use-fetch-apps";
import { totalUsers } from "@/lib/utils";
import loadable from "@loadable/component";
import { useFetchUserDocuments } from "@/hooks/kycHooks/use-fetch-user-documents";
import { useFetchAppBalances } from "@/hooks/accountsHooks/use-fetch-app-balances";
import { BalancesCardDashboard } from "@/components/BalancesCardDashboard";
// import ServerSideTransactionsTable from "@/components/dashboard/transactions-table";

const ServerSideTransactionsTableLazy = loadable(
    () => import("@/components/dashboard/transactions-table"),
    {
        fallback: <SkeletonComponent />,
    }
);

const ServerSideKYCTable = loadable(() => import("@/components/dashboard/kyc-table"), {
    fallback: <SkeletonComponent />,
});
const UserProfile = () => {
    const location = useLocation();
    const { user } = location.state as { user: UsersProps };

    const [selectedSection, setSelectedSection] = useState("Dashboard");
    const links = ["Dashboard", "Transaction", "Apps", "Profile", "KYC"];

    // console.log("🚀 ~ UserProfile ~ data:", data)

    const userBalances = useFetchUserBalance(user.accountid);
    const userBalance = userBalances?.data?.data[0];

    if (!user) {
        return <div>User not found</div>;
    }

    // const transactions = useFetchTransactions(user.accountid, 50);
    const { data, pagination, keyword, isLoading, onPaginationChange, onKeywordChange } =
        useFetchTransactions(user.accountid, 50);

    const {
        data: docsData,
        pagination: docsPagination,
        keyword: docsKeyword,
        isLoading: docsLoading,
        onPaginationChange: docsOnPagination,
        onKeywordChange: docsOnKeywordChange,
    } = useFetchUserDocuments(50, user?.accountid);
    // const transactionsData = transactions?.data?.data;

    // console.log("user documents", docsData);

    const {
        data: apps,
        pagination: appsPagination,
        keyword: appsKeyword,
        sorting: appsSorting,
        isLoading: appsLoading,
        onPaginationChange: appsOnPaginationChange,
        onKeywordChange: appsOnKeywordChange,
        onSortingChange: appsOnSortingChange,
    } = useFetchApps(50, user.accountid);

    const { data: balances } = useFetchAppBalances(user?.accountid);

    // const balancesData = transformBalancesData(balances?.data[0]);
    // console.log("balances", balancesData);

    const appsData = apps?.content;

    const totalApps = totalUsers(appsData, "id");

    const dashInfo = [
        {
            id: 1,
            icon: Wallet,

            iconColor: "text-blue-500",
            iconBgColor: "bg-blue-100",
            title: "Available Balance",
            value: formatCurrency(userBalance?.availablebalance),
        },

        {
            id: 2,
            icon: Wallet2,
            iconColor: "text-green-500",
            iconBgColor: "bg-green-100",
            title: "Unconfirmed Balances",
            value: formatCurrency(userBalance?.unavailablebalance),
        },

        {
            id: 3,
            icon: Wallet,
            iconColor: "text-teal-500",
            iconBgColor: "bg-teal-100",
            title: "Actual Balances",
            value: formatCurrency(userBalance?.actualbalance),
            menuOptions: [{ label: "View", action: () => console.log("Edit clicked") }],
        },
        {
            id: 4,
            icon: Wallet2Icon,

            iconColor: "text-red-500",
            iconBgColor: "bg-red-100",
            title: "On Hold",
            value: formatCurrency(userBalance?.holdbalance),
            menuOptions: [{ label: "View", action: () => console.log("Edit clicked") }],
        },
    ];

    if (userBalances.isLoading) {
        return (
            <div className="m-8">
                <SkeletonComponent />;
            </div>
        );
    }

    return (
        <div className="flex h-screen w-full">
            <div className="fixed h-full w-[10%] bg-white shadow-2xl dark:bg-card">
                <Sidebar links={links} selected={selectedSection} onSelect={setSelectedSection} />
            </div>
            <div className="ml-[10%] w-[90%] overflow-y-auto px-8 py-4">
                {selectedSection === "Dashboard" && (
                    <>
                        <div className="relative my-12">
                            <Banner user={user} apps={totalApps} />
                        </div>
                        <div className="relative flex w-full justify-around">
                            <div className="grid w-full grid-cols-4 gap-4">
                                {dashInfo.map((dash) => (
                                    <DashboardCard
                                        key={dash.id}
                                        icon={dash.icon}
                                        iconColor={dash.iconColor}
                                        iconBgColor={dash.iconBgColor}
                                        title={dash.title}
                                        value={dash.value}
                                        selectOptions={dash?.selectOptions}
                                        extraStyles
                                    />
                                ))}
                            </div>
                            {/* <BalancesCardDashboard data={balancesData} /> */}
                        </div>
                        <ServerSideTransactionsTableLazy
                            data={data}
                            // title="Transactions"
                            title={`${user?.name} Transaction's`}
                            loading={isLoading}
                            filterable={false}
                            pagination={pagination}
                            pageCount={data?.totalPages || 0}
                            keyword={keyword}
                            onPaginationChange={onPaginationChange}
                            onKeywordChange={onKeywordChange}
                        />
                    </>
                )}
                {selectedSection === "Transaction" && (
                    <ServerSideTransactionsTableLazy
                        data={data}
                        title={`${user?.name} Transactions`}
                        loading={isLoading}
                        filterable={true}
                        pagination={pagination}
                        pageCount={data?.totalPages || 0}
                        keyword={keyword}
                        onPaginationChange={onPaginationChange}
                        onKeywordChange={onKeywordChange}
                    />
                )}
                {/* {selectedSection === "Cashout" && <Cashout user={user} />} */}
                {selectedSection === "Apps" && (
                    <Apps
                        filterable={true}
                        balances=""
                        apps={appsData}
                        title="Apps"
                        loading={appsLoading}
                        pagination={appsPagination}
                        pageCount={appsPagination?.totalPages || 0}
                        keyword={appsKeyword}
                        sorting={appsSorting}
                        onPaginationChange={appsOnPaginationChange}
                        onKeywordChange={appsOnKeywordChange}
                        onSortingChange={appsOnSortingChange}
                    />
                )}
                {selectedSection === "Profile" && <UserProfileWindow user={user} />}
                {selectedSection === "KYC" && (
                    <ServerSideKYCTable
                        data={docsData}
                        user={user}
                        title={`${user?.name} Documents`}
                        loading={docsLoading}
                        filterable={true}
                        pagination={docsPagination}
                        pageCount={docsData?.totalPages || 0}
                        keyword={docsKeyword}
                        onPaginationChange={docsOnPagination}
                        onKeywordChange={docsOnKeywordChange}
                    />
                )}
            </div>
        </div>
    );
};

export default UserProfile;
