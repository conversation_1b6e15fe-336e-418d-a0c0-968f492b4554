import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import React, { useEffect, useState } from "react";

("use client");

import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTable } from "@/components/Customtable/data-table";

export type Payment = {
    id: string;
    account: string;
    status: "pending" | "processing" | "success" | "failed";
    email: string;
    name: string;
    phone: number;
};


export const columns: ColumnDef<Payment>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value: any) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value: any) => row.toggleSelected(!!value)}
                aria-label="Select row"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "status",
        header: "Balance",
    },
    {
        accessorKey: "account",
        header: "Account ID",
    },
    {
        accessorKey: "email",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Email
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
    },
    {
        accessorKey: "name",
        header: "Name",
    },
    {
        accessorKey: "phone",
        header: "Phone Number",
    },
];

async function getData(): Promise<Payment[]> {
    // Fetch data from your API here.
    return [
        {
            id: "728ed52f",
            account: "Accountant",
            status: "pending",
            email: "<EMAIL>",
            name: "Kanaan Stark",
            phone: ********,
        },
        // ...
    ];
}
function Merchants() {
    // const [data, setData] = useState<Payment[]>([]);
    // const [loading, setLoading] = useState<boolean>(true);

    // useEffect(() => {
    //     async function fetchData() {
    //         const result = await getData();
    //         setData(result);
    //         setLoading(false);
    //     }

    //     fetchData();
    // }, []);

    // if (loading) {
    //     return <div>Loading...</div>;
    // }
    return (
        // <div className="mx-0 h-screen w-full space-y-16 bg-gray-100 px-20 py-24">
        //     <div className="h-28 space-y-5 rounded-lg border border-gray-200 p-4 text-black shadow-lg">
        //         <h2 className="text-lg font-bold uppercase">Manage merchants</h2>
        //     </div>
        //     <div className="container mx-auto rounded-lg border border-gray-200 py-10 shadow-lg">
        //         <DataTable columns={columns} data={data} />
        //     </div>
        // </div>
        <div>
            <p>Welcome to merchants page</p>
        </div>
    );
}

export default Merchants;
