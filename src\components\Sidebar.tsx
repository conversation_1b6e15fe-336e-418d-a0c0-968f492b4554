import React, { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";

// import redde from "@/assets/images/redde-logo.svg";
import redde from "../assets/images/redde-logo.svg";
import {
    BarChart2,
    ChevronDown,
    ChevronRight,
    CircleDollarSign,
    DollarSign,
    Home,
    Settings,
    User,
    Users,
    Settings2Icon,
} from "lucide-react";
import UserProfile from "./UserProfile";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";

const navigationData = [
    {
        id: 0,
        to: "/dashboard",
        icon: Home,
        label: "Dashboard",
        hasSubItems: false,
        subItems: [],
    },
    {
        id: 1,
        // to: "/account-summary",
        to: "/account-summary",
        icon: CircleDollarSign,
        label: "Account Summary",
        hasSubItems: false,
        subItems: [],
    },
    {
        id: 2,
        to: "/transactions",
        icon: DollarSign,
        label: "Transactions",
        hasSubItems: false,
        subItems: [],
    },
    // {
    //     id: 3,
    //     to: "/charts",
    //     icon: BarChart2,
    //     label: "Charges",
    //     hasSubItems: false,
    //     subItems: [],
    // },
    // {
    //     id: 4,
    //     to: "/merchants",
    //     icon: User,
    //     label: "Merchants",
    //     hasSubItems: false,
    //     subItems: [],
    // },
    {
        id: 5,
        to: "/users",
        icon: Users,
        label: "Users",
        hasSubItems: true,
        subItems: [],
    },
    {
        id: 6,
        to: "/redde-actions",
        icon: Settings2Icon,
        label: "Redde Actions",
        hasSubItems: false,
        subItems: [],
    },
];

const Sidebar = () => {
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [openItems, setOpenItems] = useState<string[]>([]);
    const location = useLocation();

    const toggleSidebar = () => {
        setIsCollapsed(!isCollapsed);
    };

    const handleToggleSubItems = (itemLabel: string) => {
        setOpenItems((prevOpenItems) =>
            prevOpenItems.includes(itemLabel)
                ? prevOpenItems.filter((label) => label !== itemLabel)
                : [...prevOpenItems, itemLabel]
        );
    };

    return (
        <div className="h-screen">
            <aside className="fixed left-0 z-10 h-full w-20 flex-col border border-r bg-background sm:flex">
                <nav className="mt-4 flex flex-col items-center gap-4 px-2 sm:py-5">
                    {/* <div
                        style={{ marginTop: "0%" }}
                        className="flex cursor-pointer items-center justify-center p-0"
                    >
                        <img src={redde} alt="Logo" className="h-14 w-14" />
                    </div> */}
                    {navigationData.map((item) => (
                        <div key={item.id}>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <NavLink
                                            // key={item.id}
                                            to={item.to}
                                            onClick={() =>
                                                item.hasSubItems && handleToggleSubItems(item.label)
                                            }
                                            className={({ isActive }) =>
                                                isActive
                                                    ? "mx-2 flex items-center justify-between rounded-md p-3"
                                                    : "mx-2 flex items-center justify-between rounded-md bg-none p-2 hover:bg-red-100"
                                            }
                                        >
                                            <div className="flex items-center space-x-3">
                                                <item.icon
                                                    color={
                                                        location.pathname === item.to
                                                            ? "#930006"
                                                            : "#5B6B79"
                                                    }
                                                    className="h-5 w-5"
                                                />
                                                {/* {!isCollapsed && <span>{item.label}</span>} */}
                                            </div>
                                        </NavLink>
                                    </TooltipTrigger>
                                    <TooltipContent side="right">{item.label}</TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                    ))}
                </nav>
                <nav className="mt-auto flex flex-col items-center gap-4 px-2 sm:py-5">
                    <NavLink
                        to="/settings"
                        className={({ isActive }) =>
                            isActive
                                ? "group flex h-9 w-9 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:h-8 md:w-8 md:text-base"
                                : "flex h-9 w-9 items-center justify-center rounded-lg text-muted-foreground transition-colors hover:text-foreground md:h-8 md:w-8"
                        }
                    >
                        <Settings className="h-5 w-5" />
                        <span className="sr-only">Settings</span>
                    </NavLink>
                </nav>
            </aside>
        </div>
    );
};

export default Sidebar;
