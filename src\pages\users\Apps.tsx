import React, { useCallback, useState } from "react";
import { Wallet2, Wallet2Icon, Wallet } from "lucide-react";
import DashboardCard from "@/components/dashboard/DashboardCards";
import AppsTable, { AppsTableProps } from "./users-table/apps-table";
import { debounce, totalUsers } from "@/lib/utils";
import { useFetchApps } from "@/hooks/campaignHooks/use-fetch-apps";
import { useFetchAppBalances } from "@/hooks/accountsHooks/use-fetch-app-balances";

const Apps = ({
    apps,
    balances,
    pagination,
    pageCount,
    filterable,
    title,
    keyword,
    sorting,
    loading,
    onPaginationChange,
    onKeywordChange,
    onSortingChange,
}: AppsTableProps) => {
    const activeApps = apps?.filter((app: any) => app?.isactive);
    const inactiveApps = apps?.filter((app: any) => !app?.isactive);

    const dashInfo = [
        {
            id: 1,
            icon: Wallet,

            iconColor: "text-blue-500",
            iconBgColor: "bg-blue-100",
            title: "Total Apps",
            value: totalUsers(apps, "id"),
        },

        {
            id: 2,
            icon: Wallet2,
            iconColor: "text-green-500",
            iconBgColor: "bg-green-100",
            title: "Active Apps",
            value: totalUsers(activeApps, "id"),
        },

        {
            id: 3,
            icon: Wallet,
            iconColor: "text-teal-500",
            iconBgColor: "bg-teal-100",
            title: "Inactive Apps",
            value: totalUsers(inactiveApps, "id"),
            // menuOptions: [{ label: "View", action: () => console.log("Edit clicked") }],
        },
    ];

    console.log("🚀 ~ Apps ~ balance:", balances);

    return (
        <div className="mt-10 w-full">
            <div className="grid w-full grid-cols-4 gap-x-8 p-2">
                {dashInfo.map((dash) => (
                    <DashboardCard
                        key={dash.id}
                        icon={dash.icon}
                        iconColor={dash.iconColor}
                        iconBgColor={dash.iconBgColor}
                        title={dash.title}
                        value={dash.value}
                        extraStyles
                    />
                ))}
            </div>

            <AppsTable
                apps={apps}
                filterable={filterable}
                pagination={pagination}
                pageCount={pageCount}
                keyword={keyword}
                sorting={sorting}
                loading={loading}
                title={title}
                onPaginationChange={onPaginationChange}
                onKeywordChange={onKeywordChange}
                onSortingChange={onSortingChange}
            />
        </div>
    );
};
export default Apps;
