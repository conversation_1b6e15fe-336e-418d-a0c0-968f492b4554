import { type ClassValue, clsx } from "clsx";
import _ from "lodash";
import { twMerge } from "tailwind-merge";
// import { WithAsyncFn, WithAsyncReturn } from "./types/withAsync";
// import macaddress from 'macaddress'

// const macaddress = require("macaddress");

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

/**
 * A function type that represents an async or sync function.
 */
export type WithAsyncFn<T = unknown> = () => T | Promise<T>;

/**
 * The return type of the withAsync function, encapsulating the response or error.
 */
export type WithAsyncReturn<TData, TError> = {
    response: TData | null;
    error: TError | unknown;
};

/**
 * A utility function to handle async operations and capture errors.
 * @param fn - The async function to execute.
 * @returns An object containing either the response or an error.
 */
export async function withAsync<TData = unknown, TError = unknown>(
    fn: WithAsyncFn<TData>
): Promise<WithAsyncReturn<TData, TError>> {
    try {
        if (typeof fn !== "function") throw new Error("The first argument must be a function");
        const response = await fn();
        return {
            response,
            error: null,
        };
    } catch (error) {
        return {
            error,
            response: null,
        };
    }
}

/**
 * Creates a debounced function that delays invoking the provided function until after a specified
 * wait time has elapsed since the last time the debounced function was invoked.
 *
 * This is useful in scenarios where you want to limit the rate at which a function is executed,
 * such as in handling API calls triggered by user input events like keystrokes.
 *
 * @template T - The type of the function to debounce. This should be a function type.
 *
 * @param {T} func - The function to debounce. This function will be invoked after the debounce
 * delay if no new invocations occur during the delay period.
 *
 * @param {number} wait - The number of milliseconds to delay. The function will only be called
 * after this time has elapsed since the last call.
 *
 * @returns {(...args: Parameters<T>) => void} A debounced version of the provided function. When
 * invoked, it will delay execution until after the specified wait time has elapsed since the last
 * invocation.
 *
 * @example
 * ```
 * const debouncedSave = debounce(saveData, 300);
 *
 * inputElement.addEventListener('input', (event) => {
 *     debouncedSave(event.target.value);
 * });
 * ```
 */
export function debounce<T extends (...args: any[]) => void>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;

    return (...args: Parameters<T>) => {
        if (timeout) {
            clearTimeout(timeout);
        }

        timeout = setTimeout(() => {
            func(...args);
        }, wait);
    };
}

// utility.ts

// get macaddress from client device
// export const getMacAddress = (): Promise<string> => {
//     return new Promise((resolve, reject) => {
//         macaddress.one((err: any, mac: string) => {
//             if (err) reject(err);
//             resolve(mac);
//         });
//     });
// };

export function sumBalances(data: any[]): number {
    return _.sumBy(data, "balance");
}

// Utility function to calculate total clients based on unique account IDs
export const totalClients = (data: any[]): number => {
    // Use Lodash to create an array of unique account IDs
    const uniqueAccountIds = _.uniqBy(data, "accountId");

    // Return the count of unique account IDs
    return uniqueAccountIds.length;
};

export const totalUsers = (data: any[], accessor: string): number => {
    // Use Lodash to create an array of unique account IDs
    const uniqueAccountIds = _.uniqBy(data, accessor);

    // Return the count of unique account IDs
    return uniqueAccountIds.length;
};

export function formatCurrency(
    amount: number,
    currency: string = "GHS",
    locale: string = "en-GB"
): string {
    return new Intl.NumberFormat(locale, {
        style: "currency",
        currency: currency,
    }).format(amount);
}

export const transformData = (data: any[]): any[] => {
    return data.map((item) => ({
        balance: item[0],
        accountId: item[1],
        email: item[2],
        name: item[3],
        phone: item[4],
    }));
};

// export const transformBalancesData = (data: any[]): any[] => {
//     return data.map((balance) => ({
//         amount: balance[0],
//         appId: balance[1],
//         appName: balance[2],
//     }));
// };
export const transformReviewData = (data: any[]): any[] => {
    return data.map((item) => ({
        internalId: item[0],
        id: item[1],
        brandtransid: item[16],
        username: item[4],
        appid: item[5],
        brandgroup: item[6],
        channel: item[8],
        transtype: item[9],
        amount: item[10],
        actualamount: item[11],
        charge: item[12],
        clienttransid: item[13],
        clienttransref: item[14],
        status: item[17],
        statusreason: item[18],
        transdate: item[19],
        statusupdatedate: item[20],
        isconfirmed: item[26],
        callbacksuccess: item[25],
        nickname: item[21],
        description: item[22],
    }));
};

export const transformFailedData = (data: any[]): any[] => {
    return data.map((item) => ({
        internalId: item[0],
        id: item[1],
        brandtransid: item[13],
        username: item[2],
        appid: item[3],
        brandgroup: item[4],
        channel: item[6],
        transtype: item[7],
        amount: item[8],
        actualamount: item[9],
        charge: item[10],
        clienttransid: item[11],
        clienttransref: item[14],
        status: item[15],
        statusreason: item[16],
        transactiondate: item[17],
        statusupdatedate: item[18],
        isconfirmed: item[26],
        callbacksuccess: item[25],
        nickname: item[19],
        description: item[20],
    }));
};
export const formatDateToLongString = (dateString: string | undefined | null): string => {
    if (!dateString) return "N/A";

    try {
        const date = new Date(dateString);

        // Check if date is valid
        if (isNaN(date.getTime())) {
            return "Invalid Date";
        }

        // Array for ordinal suffixes
        const suffixes = ["th", "st", "nd", "rd"];
        const day = date.getDate();

        // Get correct ordinal suffix
        const relevantDigits = day % 100 > 10 && day % 100 < 14 ? 0 : day % 10;
        const suffix = suffixes[relevantDigits] || suffixes[0];

        return date
            .toLocaleDateString("en-US", {
                month: "long",
                day: "numeric",
                year: "numeric",
            })
            .replace(/\d+/, day + suffix);
    } catch (error) {
        console.error("Error formatting date:", error);
        return "Invalid Date";
    }
};
