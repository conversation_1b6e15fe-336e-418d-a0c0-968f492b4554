import React from "react";
import { Bell } from "lucide-react";
import { INotificationIconProps } from "@/lib/interfaces";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";


const NotificationIcon: React.FC<INotificationIconProps> = ({ notificationCount }) => {
    return (
        <>
            <div className="relative inline-block rounded-full">
                {/* <Bell className="h-5 w-5 text-gray-800" /> */}

                <Button variant="secondary" size="icon" className="rounded-full bg-primary">
                    <Bell className="h-5 w-5 text-gray-800" />
                    <span className="sr-only">Toggle notification menu</span>
                </Button>
                {notificationCount > 0 && (
                    <span className="absolute right-0 top-0 inline-flex -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-primary p-1 text-xs font-bold leading-none text-white">
                        {notificationCount}
                    </span>
                )}
            </div>

            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <NotificationIcon notificationCount={notificationCount} />
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuLabel>My Account</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>Settings</DropdownMenuItem>
                    <DropdownMenuItem>Support</DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>Logout</DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </>
    );
};

export default NotificationIcon;
