import React from "react";
import { Card, CardContent } from "./ui/card";

type BannerProps = {
    title: string;
    subTitle: string | number;
};
const Banner = ({ title, subTitle }: BannerProps) => {
    return (
        <Card className=" space-y-5 rounded-lg border   p-4  ">
            <CardContent>
                <h2 className="text-lg font-bold uppercase">{title}</h2>
                <p className="font-bold text-red-500">{subTitle}</p>
            </CardContent>
        </Card>
    );
};
export default Banner;
