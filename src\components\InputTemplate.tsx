"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";

import { Input } from "@/components/ui/input";
import React from "react";
import { Checkbox } from "./ui/checkbox";
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

interface InputFormProps {
    fields: any[];
    onSubmit: (data: any) => void;
    layout?: "row" | "column";
    submit?: string;
    submitBg?: string;
    filteredIds?: number[];
}

export function InputForm({
    fields,
    onSubmit,
    layout = "column",
    submit = "Submit",
    submitBg,
    filteredIds = [],
}: InputFormProps) {
    const fieldSchemas = fields.reduce((schema: any, field: any) => {
        if (field.name === "email") {
            schema.email = z.string().email({ message: "Invalid email address." });
        } else if (field.name === "mobile") {
            schema.mobile = z.string().regex(/^[0-9]+$/, { message: "Invalid mobile number." });
        } else if (field.name === "fullname") {
            schema.fullname = z.string().min(2, { message: "Text must be at least 2 characters." });
        } else if (field.name === "password") {
            schema.password = z
                .string()
                .min(8, { message: "Password must be at least 8 characters." });
        } else if (field.inputType === "checkbox") {
            schema[field.name] = z.boolean();
        } else if (field.name === "description") {
            schema.description = z
                .string()
                .min(2, { message: "Text Description must be at least 2 characters." });
        } else if (field.name === "apiKey") {
            schema.apiKey = z.string().min(5, { message: "ApiKey must be at least 5 characters." });
        } else if (field.name === "merchantID") {
            schema.merchantID = z
                .string()
                .min(5, { message: "MerchantID must be at least 5 characters." });
        } else if (field.name === "productID") {
            schema.productID = z
                .string()
                .min(5, { message: "ProductID must be at least 5 characters." });
        } else if (field.name === "senderID") {
            schema.senderID = z
                .string()
                .min(5, { message: "SenderID must be at least 5 characters." });
        } else if (field.name === "accountID") {
            schema.accountID = z
                .string()
                .min(5, { message: "AccountID must be at least 5 characters." });
        } else if (field.name === "username") {
            schema.username = z
                .string()
                .min(5, { message: "Username must be at least 5 characters." });
        } else if (field.name === "requestURL") {
            schema.requestURL = z
                .string()
                .min(5, { message: "Request URL must be at least 5 characters." });
        } else if (field.name === "campaignID") {
            schema.campaignID = z
                .string()
                .min(5, { message: "Campaign ID must be at least 5 characters." });
        } else if (field.name === "amount") {
            schema.amount = z.string().min(1, { message: "Enter Amount." });
        } else if (field.name === "startDate") {
            schema.startDate = z.string().min(1, { message: "Select Start Date" });
        } else if (field.name === "endDate") {
            schema.endDate = z.string().min(1, { message: "Select End Date" });
        } else if (field.inputType === "date") {
            schema[field.name] = z.string().min(1, { message: "Select a valid date." });
        } else if (field.name === "reason") {
            schema.reason = z.string().min(5, { message: "Reason must be at least 2 characters." });
        } else if (field.inputType === "select") {
            schema[field.name] = z.string().min(1, { message: "Please select a value." });
        }

        return schema;
    }, {});

    const FormSchema = z.object(fieldSchemas);

    const form = useForm<z.infer<typeof FormSchema>>({
        resolver: zodResolver(FormSchema),
        defaultValues: fields.reduce((defaults: any, field: any) => {
            if (field.inputType === "checkbox") {
                defaults[field.name] = false;
            } else {
                defaults[field.name] = "";
            }
            return defaults;
        }, {}),
    });

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-6">
                {/*  render first two fields based on layout prop */}
                {layout === "row" ? (
                    <div className="flex space-x-4">
                        {fields.slice(0, 2).map((field) => (
                            <FormField
                                key={field.id}
                                control={form.control}
                                name={field.name}
                                render={({ field: formField }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel>{field.label}</FormLabel>
                                        <FormControl>
                                            <Input
                                                type={field.inputType}
                                                placeholder={field.placeholder}
                                                {...formField}
                                                className="rounded-md py-5"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        ))}
                    </div>
                ) : (
                    fields
                        .slice(0, 2)
                        .filter((field) => !filteredIds.includes(field.id))
                        .map((field) => (
                            <FormField
                                key={field.id}
                                control={form.control}
                                name={field.name}
                                render={({ field: formField }) => (
                                    <FormItem>
                                        <FormLabel>{field.label}</FormLabel>
                                        <FormControl>
                                            {field.inputType === "select" ? (
                                                <Select
                                                    onValueChange={formField.onChange}
                                                    value={formField.value}
                                                >
                                                    <SelectTrigger className="w-full py-3">
                                                        <SelectValue
                                                            placeholder={field.placeholder}
                                                        />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectGroup>
                                                            <SelectLabel>{field.label}</SelectLabel>
                                                            {field.options.map((option: string) => (
                                                                <SelectItem
                                                                    key={option}
                                                                    value={option}
                                                                    className="py-5"
                                                                >
                                                                    {option}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectGroup>
                                                    </SelectContent>
                                                </Select>
                                            ) : (
                                                <Input
                                                    type={field.inputType}
                                                    placeholder={field.placeholder}
                                                    {...formField}
                                                    className="rounded-md py-5"
                                                />
                                            )}
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        ))
                )}

                {/* other fields */}

                {fields
                    .filter(
                        (field) => !filteredIds.includes(field.id) && field.inputType !== "checkbox"
                    )
                    .slice(2)
                    .map((field) => (
                        <FormField
                            key={field.id}
                            control={form.control}
                            name={field.name}
                            render={({ field: formField }) => (
                                <FormItem>
                                    <FormLabel>{field.label}</FormLabel>
                                    <FormControl>
                                        {field.inputType === "select" ? (
                                            <Select
                                                onValueChange={formField.onChange}
                                                value={formField.value}
                                            >
                                                <SelectTrigger className="w-full py-3">
                                                    <SelectValue placeholder={field.placeholder} />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectGroup>
                                                        <SelectLabel>{field.label}</SelectLabel>
                                                        {field.options.map((option: string) => (
                                                            <SelectItem
                                                                key={option}
                                                                value={option}
                                                                className="py-5"
                                                            >
                                                                {option}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectGroup>
                                                </SelectContent>
                                            </Select>
                                        ) : (
                                            <Input
                                                type={field.inputType}
                                                placeholder={field.placeholder}
                                                {...formField}
                                                className="rounded-md py-5"
                                            />
                                        )}
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    ))}

                {/* Render fields with IDs 3 to 6 in a flex container */}
                <div className="flex w-full space-x-4">
                    {fields
                        .filter((field) => filteredIds.includes(field.id))
                        .map((field) => (
                            <FormField
                                key={field.id}
                                control={form.control}
                                name={field.name}
                                render={({ field: formField }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel>{field.label}</FormLabel>
                                        <FormControl>
                                            <Input
                                                type={field.inputType}
                                                placeholder={field.placeholder}
                                                {...formField}
                                                className="rounded-md py-5"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        ))}
                </div>

                {/* checkboxes */}
                <div className="flex flex-row space-x-4">
                    {fields
                        .filter((field) => field.inputType === "checkbox")
                        .map((field) => (
                            <FormField
                                key={field.id}
                                control={form.control}
                                name={field.name}
                                render={({ field: formField }) => (
                                    <FormItem>
                                        <div className="flex items-center space-x-2">
                                            <Checkbox
                                                checked={formField.value}
                                                onCheckedChange={formField.onChange}
                                            />
                                            <label className="text-sm font-medium leading-none">
                                                {field.checkboxLabel}
                                            </label>
                                        </div>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        ))}
                </div>
                <Button className={`${submitBg}`} type="submit">
                    {submit}
                </Button>
            </form>
        </Form>
    );
}
