import React from "react";

import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
// import { Badge }  "@/components/ui/badge";
import { useFetchChannels } from "@/hooks/optionsHooks/use-fetch-channels";
import { useState } from "react";
import {
    Select,
    SelectItem,
    SelectContent,
    SelectValue,
    SelectTrigger,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormField, Form, FormMessage, FormLabel, FormItem } from "@/components/ui/form";
import { useFetchConfirmedTransactions } from "@/hooks/transactionHooks/use-confirm-transactions";
import { toast } from "@/components/ui/use-toast";

const confirmTransactionSchema = z.object({
    channel: z.string(),
    startdate: z.date().transform((date) => date.toISOString().split("T")[0]),
    enddate: z.date().transform((date) => date.toISOString().split("T")[0]),
});

export const ConfirmTransaction = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const channelsData = useFetchChannels();

    const form = useForm<z.infer<typeof confirmTransactionSchema>>({
        resolver: zodResolver(confirmTransactionSchema),
        defaultValues: {
            channel: "",
            startdate: undefined,
            enddate: undefined,
        },
    });

    const confirmTransactions = useFetchConfirmedTransactions();
    const transformData = (data: any[]): any[] => {
        return data?.map((item) => ({
            channel: item,
        }));
    };

    const onSubmit = async (values: z.infer<typeof confirmTransactionSchema>) => {
        // console.log("confirm transactions data", data);
        // confirmTransactions(data);

        try {
            console.log("Submitting values:", values);
            const result = await confirmTransactions.mutateAsync(values);

            toast({
                title: "Success",
                description: "Transactions confirmed successfully",
            });

            form.reset(); // Reset form after successful submission
            setIsModalOpen(false);
        } catch (error: any) {
            const errorStatus = error?.response?.status;
            const errorMessage =
                error?.response?.data?.message || error.message || "Failed to confirm transactions";

            toast({
                title: `Error (${errorStatus || "unknown"})`,
                description: errorMessage,
                variant: "destructive",
            });
        }
    };

    const transformedChannels = channelsData ? transformData(channelsData?.data?.data?.flat()) : [];
    return (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
                <Button>Confirm Transaction</Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
                <DialogHeader>
                    <DialogTitle>Confirm Transaction</DialogTitle>
                </DialogHeader>
                <DialogDescription>
                    <p>This is a description of the transaction that you are confirming.</p>
                </DialogDescription>

                <Form {...form}>
                    <form className="my-4 grid w-full grid-cols-1 gap-y-4">
                        <FormField
                            control={form.control}
                            name="channel"
                            render={({ field }) => (
                                <FormItem className="w-full">
                                    <FormLabel>Channel</FormLabel>
                                    <Select value={field.value} onValueChange={field.onChange}>
                                        <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Channel" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {transformedChannels &&
                                                transformedChannels.map((cha, index) => (
                                                    <SelectItem value={cha.channel} key={index}>
                                                        {cha.channel}
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <div className="grid grid-cols-2 gap-x-4">
                            <FormField
                                control={form.control}
                                name="startdate"
                                render={({ field }) => (
                                    <FormItem className="w-full">
                                        <FormLabel>Start Date</FormLabel>
                                        <DatePicker
                                            className="w-full"
                                            date={field.value}
                                            title="start date"
                                            setDate={field.onChange}
                                        />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="enddate"
                                render={({ field }) => (
                                    <FormItem className="w-full">
                                        <FormLabel>End Date</FormLabel>
                                        <DatePicker
                                            className="w-full"
                                            title="end date"
                                            date={field.value}
                                            setDate={field.onChange}
                                        />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <Button onClick={form.handleSubmit(onSubmit)} size="sm" className="w-full">
                            Confirm Transaction
                        </Button>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
};
