import React, { use<PERSON><PERSON>back, useState } from "react";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Search } from "lucide-react";

import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";

import { DateRange } from "react-day-picker";
import { CalendarDateRangePicker } from "@/components/ui/date-range-picker";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { columns } from "@/components/dashboard/transactions-table/columns";
import { confirmedColumns } from "@/components/dashboard/transactions-table/confirmed-columns";
import { ServerSideDataTable } from "@/components/ui/server-side-data-table";
import { Input } from "@/components/ui/input";
import { debounce } from "lodash";

export interface IAppProps {
    id: number;
    accountid: number;
    callbackurl: string | null;
    description: string;
    enddate: string | null;
    isactive: boolean;
    settlementcallbackurl: string | null;
    startdate: string;
}

export type AppsTableProps = {
    apps: any;
    filterable?: boolean;
    title?: string;
    balances?: any;
    loading?: boolean;
    pagination: any;
    pageCount: number;
    keyword: string;
    sorting: any;
    onPaginationChange: (newPagination: any) => void;
    onKeywordChange: (newKeyword: string) => void;
    onSortingChange: (newSorting: any) => void;
};

export default function AppsTable({
    apps,
    filterable = true,
    title,
    loading,
    pagination,
    pageCount,
    keyword,
    sorting,
    onPaginationChange,
    onKeywordChange,
    onSortingChange,
}: AppsTableProps) {
    const [searchTerm, setSearchTerm] = useState("");

    const debouncedSearch = useCallback(
        debounce((value: string) => {
            onKeywordChange(value);
        }, 500),
        [onKeywordChange]
    );

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        debouncedSearch(value);
    };

    const columns: ColumnDef<IAppProps>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-[2px]"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-[2px]"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },

        {
            accessorKey: "id",
            header: "App ID",
            cell: ({ row }) => <p className="text-xs font-bold">{row.getValue("id")}</p>,
        },

        {
            accessorKey: "description",
            header: "Description",
            cell: ({ row }) => (
                <p className="font-semibold lowercase text-muted-foreground">
                    {row.getValue("description")}
                </p>
            ),
        },

        {
            accessorKey: "isactive",
            header: "Status",
            cell: ({ row }) => {
                const active = row.getValue("isactive");
                return (
                    <p
                        className={`flex items-center justify-center rounded-full border p-1 text-xs lowercase ${
                            active
                                ? "border-green-500 bg-green-100 text-green-500"
                                : "border-red-500 bg-red-100 text-red-500"
                        }`}
                    >
                        {active ? "Active" : "Inactive"}
                    </p>
                );
            },
        },

        {
            accessorKey: "callbackurl",
            header: "Callback URL",
            cell: ({ row }) => {
                const url = row.getValue("callbackurl");
                // const cashout = row.getValue("settlementcallbackurl");

                return (
                    <div>
                        {url ? (
                            <>
                                <p className="text-wrap text-xs text-blue-500">
                                    <span className="font-bold">Receive: </span> {url}
                                </p>
                                {/* <p className="text-wrap text-xs text-blue-500">
                                    <span className="font-bold">Cashout: </span> {cashout}
                                </p> */}
                            </>
                        ) : (
                            <p className="text-xs font-semibold text-muted-foreground">
                                No Callback URL
                            </p>
                        )}
                    </div>
                );
            },
        },
        {
            accessorKey: "startdate",
            header: "Effective On",
            cell: ({ row }) => {
                const startDate = row.getValue("startdate");
                if (!startDate) {
                    return <div>Not set</div>;
                }
                const date = new Date(startDate);
                return <div>{format(date, "PPP")}</div>;
            },
        },

        {
            accessorKey: "enddate",
            header: "Expires On",
            cell: ({ row }) => {
                const endDate = row.getValue("enddate");
                if (!endDate) {
                    return <div>Not set</div>;
                }
                const date = new Date(endDate);
                return <div>{format(date, "PPP")}</div>;
            },
        },
        {
            id: "actions",
            enableHiding: false,
            header: "Action",
            cell: ({ row }) => {
                const user = row.original;
                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>View App</DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];

    // console.log("🚀 ~ AppsTable ~ data:", data)
    return (
        <Card className="mx-auto py-8">
            <CardContent>
                {filterable && (
                    <Card>
                        <CardHeader>Filter</CardHeader>
                        <CardContent>
                            <div className="my-4 grid w-full grid-cols-4 gap-x-4">
                                <Input
                                    type="text"
                                    placeholder="Search..."
                                    value={searchTerm}
                                    onChange={handleSearchChange}
                                />
                            </div>
                        </CardContent>
                    </Card>
                )}

                <div className="mt-4">
                    <ServerSideDataTable
                        columns={columns}
                        data={apps || []}
                        title={title || "  "}
                        loading={loading}
                        filterable={filterable}
                        pagination={pagination}
                        pageCount={pageCount}
                        keyword={keyword}
                        sorting={sorting}
                        onKeywordChange={onKeywordChange}
                        onPaginationChange={onPaginationChange}
                        onSortingChange={onSortingChange}
                    />
                </div>
            </CardContent>
        </Card>
    );
}
