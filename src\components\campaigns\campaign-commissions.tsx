// AddUser.js
import React, { useState } from "react";

import ServerSideCampaignsTable from "@/components/campaigns/campaigns-table/server-side-table";
import { useFetchCampaignCharges } from "@/hooks/campaignHooks/use-fetch-campaign-charges";
import _ from "lodash";
import ServerSideCampaignsChargesTable from "./campaigns-table/campaign-charges-table";
import { Button } from "../ui/button";
import { PlusIcon } from "lucide-react";
import AddSingleCharge, { FormState } from "./add-single-charge";
import { useAuth } from "@/contexts/AuthContexts";
import { useChargeCampaign } from "@/hooks/accountsHooks/use-fetch-transactions";
import { toast } from "../ui/use-toast";
import { useFetchCampaignCommissions } from "@/hooks/campaignHooks/use-fetch-campaign-commissions";
import ServerSideCampaignsCommissionsTable from "./campaigns-table/campaign-commissions-table";

interface CampaignChargesProps {
    campaignId: number;
}

const CampaignCommissions: React.FC<CampaignChargesProps> = ({ campaignId }) => {
    const { user } = useAuth();
    const [open, setOpen] = React.useState(false);
    const [campaignid, setCampaignid] = useState<number | null>(null);
    const chargeCampaign = useChargeCampaign(campaignId ?? 0);
    const {
        data,
        isLoading,
        pagination,
        pageCount,
        keyword,
        sorting,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
    } = useFetchCampaignCommissions(campaignId);

    return (
        <div className="">
            <ServerSideCampaignsCommissionsTable
                data={data}
                title="Campaign Commissions"
                loading={isLoading}
                filterable={false}
                pagination={pagination}
                pageCount={pageCount}
                keyword={keyword}
                sorting={sorting}
                onPaginationChange={onPaginationChange}
                onKeywordChange={onKeywordChange}
                onSortingChange={onSortingChange}
            />
            {/* <p>Coming Soon</p> */}
        </div>
    );
};

export default CampaignCommissions;
