import React from "react";
import user from "@/assets/images/user.svg";
import { EllipsisVertical, UserRoundCheckIcon } from "lucide-react";
import { IUserProfileProps } from "@/lib/interfaces";

const UserProfile: React.FC<IUserProfileProps> = ({ toggleSidebar }) => {
    return (
        <div className="mx-2 my-4 flex items-center space-x-2 rounded-md bg-gray-200 p-3">
            {/* <div className="h-12 w-12 rounded-full "> */}
            <UserRoundCheckIcon
                className="h-12 w-12"
                // src={user} // Replace with the path to your profile image
                // alt="User Profile"
            />
            {/* </div> */}
            {/* <div className="mr-2 flex-1">
                <h3 className="text-md font-semibold text-gray-800">Emmanuel</h3>
                <p className="text-sm text-gray-500">Administrator</p>
            </div> */}
            {/* <EllipsisVertical onClick={toggleSidebar} className="cursor-pointer" color="#5B6B79" /> */}
        </div>
    );
};

export default UserProfile;
