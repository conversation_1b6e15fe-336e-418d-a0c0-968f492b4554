import { api } from "../api";

/**
 * Fetches all channels.
 *
 * @returns {Promise<any>} A promise that resolves to the API response data containing the campaigns.
 */
export const fetchAllChannels = async () => {
    try {
        const response = await api.get(`/options/channels`);
        return response?.data;
    } catch (error) {
        console.error("Failed to fetch channels:", error);
        throw error;
    }
};

/**
 * Fetches all transactions.
 *
 * @returns {Promise<any>} A promise that resolves to the API response data containing the campaigns.
 */
export const fetchTransactionTypes = async () => {
    try {
        const response = await api.get(`/options/transtypes`);
        return response?.data;
    } catch (error) {
        console.error("Failed to fetch transaction types:", error);
        throw error;
    }
};

export const fetchChargeProfileTypes = async () => {
    try {
        const response = await api.get(`/options/chargeprofiletypes`);
        return response?.data;
    } catch (error) {
        console.error("Failed to fetch charge profiles:", error);
        throw error;
    }
};
export const fetchChargeProfiles = async (keyword?: string) => {
    const searchTerm = keyword?.toString() || "";

    try {
        const response = await api.get(
            `/charges/profiles?sort=${"createddate,desc"}&search=${searchTerm}`
        );
        return response?.data;
    } catch (error) {
        console.error("Failed to fetch charge profiles:", error);
        throw error;
    }
};

export const fetchChargeTypes = async () => {
    try {
        const response = await api.get(`/options/chargetypes`);
        return response?.data;
    } catch (error) {
        // console.error("Failed to fetch charge profile details:", error);
        throw error;
    }
};

export const fetchBrands = async () => {
    try {
        const response = await api.get(`/options/brands`);
        return response?.data;
    } catch (error) {
        // console.error("Failed to fetch brands:", error);
        throw error;
    }
};

export const fetchBrandGroups = async () => {
    try {
        const response = await api.get(`/options/brandgroups`);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const fetchChargeModels = async () => {
    try {
        const response = await api.get(`/options/chargemodels`);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const fetchTransactionStatuses = async () => {
    try {
        const response = await api.get(`/options/transstatuses`);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const fetchBanks = async () => {
    try {
        const response = await api.get(`/options/banks`);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const fetchBankBranches = async (bankId: number) => {
    try {
        const response = await api.get(`/options/banks/${bankId}/branches/`);
        return response?.data;
    } catch (error) {
        throw error;
    }
};
