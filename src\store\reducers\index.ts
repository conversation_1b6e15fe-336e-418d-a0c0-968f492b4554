// third-party
import { combineReducers } from "redux";
import { persistReducer } from "redux-persist";
import productReducer from "./product";

import storage from "redux-persist/lib/storage";

// ==============================|| COMBINE REDUCERS ||============================== //

const reducers = combineReducers({
    product: productReducer,
    //   company: persistReducer(
    //     {
    //       key: "company",
    //       storage,
    //       keyPrefix: "user-company"
    //     },
    //     companyReducer
    //   ),
});

export default reducers;
