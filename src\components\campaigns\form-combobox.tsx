"use client";
import * as React from "react";

import { Check, ChevronsUpDown, Circle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { debounce } from "@/lib/utils";

export function FormCombobox({
    form,
    name,
    label,
    data,
    isLoading = false,
    onSearch,
    onSelect,
    selectedValue,
    onKeywordChange
}: any) {
    const [open, setOpen] = React.useState(false);

    const debouncedSearch = React.useCallback(
        debounce((value: string) => {
            onKeywordChange(value);
        }, 500),
        [onKeywordChange]
    );

    const handleSearch = (value: string) => {
        if (onSearch) {
            onSearch(value);
            debouncedSearch(value);
        }
    };

    return (
        <FormField
            control={form.control}
            name={name}
            render={({ field }) => (
                <FormItem className="w-full">
                    <FormLabel>{label}</FormLabel>
                    <FormControl>
                        <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                                <Button
                                    variant="outline"
                                    role="combobox"
                                    aria-expanded={open}
                                    className="w-full justify-between"
                                >
                                    {selectedValue?.name || `Select ${label.toLowerCase()}...`}
                                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0">
                                <Command>
                                    <CommandInput
                                        placeholder={`Search ${label.toLowerCase()}...`}
                                        onValueChange={handleSearch}
                                        className="h-9"
                                    />
                                    {isLoading ? (
                                        <div className="flex items-center justify-center py-4">
                                            <Circle className="h-6 w-6 animate-spin" />
                                        </div>
                                    ) : (
                                        <div className="max-h-64 overflow-y-auto">
                                            <CommandList>
                                                <CommandEmpty>No {label.toLowerCase()} found.</CommandEmpty>
                                                <CommandGroup>
                                                    {data?.map((item: any) => (
                                                        <CommandItem
                                                            key={item.name}
                                                            value={item.name}
                                                            onSelect={(currentValue) => {
                                                                if (onSelect) {
                                                                    onSelect(currentValue);
                                                                }
                                                                setOpen(false);
                                                            }}
                                                        >
                                                            {item.name}
                                                            <Check
                                                                className={cn(
                                                                    "ml-auto h-4 w-4",
                                                                    selectedValue?.name === item.name
                                                                        ? "opacity-100"
                                                                        : "opacity-0"
                                                                )}
                                                            />
                                                        </CommandItem>
                                                    ))}
                                                </CommandGroup>
                                            </CommandList>
                                        </div>
                                    )}
                                </Command>
                            </PopoverContent>
                        </Popover>
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    );
}
