import { useState, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { UsersApiResponse } from "@/lib/interfaces";
import debounce from "lodash/debounce";
import { fetchBankAccounts } from "@/api/bank/bankaccountsApi";

export const useFetchBankAccounts = (accountId: any, initialPageSize: number = 50) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });

    const [keyword, setKeyword] = useState<string | number>("");
    const [sorting, setSorting] = useState([{ id: "createdtime", desc: true }]);

    const fetchAccounts = useCallback(() => {
        return fetchBankAccounts(
            accountId,
            pagination.pageIndex,
            pagination.pageSize,
            keyword,
            sorting
        );
    }, [accountId, pagination.pageIndex, pagination.pageSize, keyword, sorting]);

    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        queryKey: [
            "bankaccounts",
            accountId,
            pagination.pageIndex,
            pagination.pageSize,
            keyword,
            sorting,
        ],
        queryFn: fetchAccounts,
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const debouncedKeywordChange = useCallback(
        debounce((newKeyword: string) => {
            setKeyword(newKeyword);
        }, 300),
        []
    );

    const onKeywordChange = (newKeyword: string) => {
        debouncedKeywordChange(newKeyword);
    };

    const onSortingChange = (newSorting: any) => {
        setSorting(newSorting);
    };

    return {
        data: data?.data,
        pagination,
        keyword,
        isLoading,
        isError,
        error,
        sorting,
        onSortingChange,
        onPaginationChange,
        onKeywordChange,
        refetch,
    };
};
