import { campaignCharges, deleteChargeProfileAssignedToCampaign, modifyCampaignCharge, simulateCharge } from "@/api/campaigns/appsApi";
import { useMutation } from "@tanstack/react-query";

interface SimulateChargeParams {
    accountId: number;
    campaignData: any;
}

interface DeleteChargeProfileAssignedToCampaignParams {
    campaignId: number;
    chargeId: number;
}


/**
 * A hook that wraps the simulateCharge function in a mutation for use with react-query.
 *
 * @returns {UseMutationResult} A mutation result object containing the mutation function and related states.
 */
export const useSimulateCharge = () => {
    return useMutation({
        mutationFn: async ({ accountId, campaignData }: SimulateChargeParams) => {
            const response = await simulateCharge(accountId, campaignData);
            return response.data;
        },
    });
};
export const useAssignCharge = () => {
    return useMutation({
        mutationFn: async ({ accountId, campaignData }: SimulateChargeParams) => {
            return await campaignCharges(accountId, campaignData);
        },
    });
};

export const useModifyCampaignCharge = () => {
    return useMutation({
        mutationFn: async ({ accountId, campaignData }: SimulateChargeParams) => {
            return await modifyCampaignCharge(accountId, campaignData);
        },
    });
};

export const useDeleteChargeProfileAssignedToCampaign = () => {
    return useMutation({
        mutationFn: async ({campaignId, chargeId}: DeleteChargeProfileAssignedToCampaignParams) => {
            return await deleteChargeProfileAssignedToCampaign(campaignId, chargeId);
        },
    });
};
