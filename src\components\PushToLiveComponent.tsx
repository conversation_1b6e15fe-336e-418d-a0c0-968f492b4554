import React from "react";
import {
    Alert<PERSON><PERSON>og,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { EyeIcon, SendHorizonal } from "lucide-react";

const PushToLiveComponent = (user: any) => {
    // console.log("user to be pushed to live", user);
    return (
        <div>
            <div className="flex items-center gap-2">
                <AlertDialog>
                    <AlertDialogTrigger>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button variant="icon">
                                        <SendHorizonal className="h-4 w-4 text-primary" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Move to live</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>
                                Are you sure you want to push to live?
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                                If you are okay with the user information, proceed to push them to
                                live!
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>No</AlertDialogCancel>
                            <AlertDialogAction>Yes</AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
                {/* <Suspense fallback={<div>Loading...</div>}>
                        <AddCampaignDialogLazy user={user} />
                    </Suspense> */}
            </div>
        </div>
    );
};

export default React.memo(PushToLiveComponent);
