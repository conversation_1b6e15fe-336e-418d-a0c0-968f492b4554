import React, { createContext, useState, ReactNode, FC, useEffect } from "react";

interface DemoContextType {
    isDemo: boolean;
    toggleDemo: () => void;
}

const DemoContext = createContext<DemoContextType | undefined>(undefined);

const DemoProvider: FC<{ children: ReactNode }> = ({ children }) => {
    const [isDemo, setIsDemo] = useState(() => {
        const storedValue = localStorage.getItem("isDemo");
        return storedValue === "true";
    });

    const toggleDemo = () => {
        setIsDemo((prev) => !prev);
    };

    useEffect(() => {
        localStorage.setItem("isDemo", isDemo.toString());
    }, [isDemo]);

    return <DemoContext.Provider value={{ isDemo, toggleDemo }}>{children}</DemoContext.Provider>;
};

export { DemoContext, DemoProvider };
