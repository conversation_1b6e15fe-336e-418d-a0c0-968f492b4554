import { createCampaign, createCampaignCharges } from "@/api/campaigns/appsApi";
import { useMutation } from "@tanstack/react-query";

/**
 * A hook that wraps the createCampaign function in a mutation for use with react-query.
 *
 * This hook returns a mutation function that calls the createCampaign function with the provided data.
 * It can be used to handle campaign creation in React components.
 *
 * @returns {UseMutationResult} A mutation result object from react-query, containing the mutation function and related states.
 */
export const useCreateCampaignCharges = (campaignId: number) => {
    return useMutation({
        mutationFn: (data: any) => createCampaignCharges(campaignId, data),
    });
};
