import { useState, useCallback } from "react";
import { fetchAllUsers } from "@/api/user/authApi";
import { useQuery } from "@tanstack/react-query";
import { UsersApiResponse } from "@/lib/interfaces";
import debounce from "lodash/debounce";

export const useFetchUsers = (initialPageSize: number = 50) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });

    const [keyword, setKeyword] = useState<string | number>("");
    const [sorting, setSorting] = useState([{ id: "createdtime", desc: true }]);

    const fetchUsers = useCallback(() => {
        return fetchAllUsers(pagination.pageIndex, pagination.pageSize, keyword, sorting);
    }, [pagination.pageIndex, pagination.pageSize, keyword, sorting]);

    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        queryKey: ["users", pagination.pageIndex, pagination.pageSize, keyword, sorting],
        queryFn: fetchUsers,
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const debouncedKeywordChange = useCallback(
        debounce((newKeyword: string) => {
            setKeyword(newKeyword);
        }, 300),
        []
    );

    const onKeywordChange = (newKeyword: string) => {
        debouncedKeywordChange(newKeyword);
    };

    const onSortingChange = (newSorting: any) => {
        setSorting(newSorting);
    };

    return {
        data: data?.data,
        pagination,
        keyword,
        isLoading,
        isError,
        error,
        sorting,
        onSortingChange,
        onPaginationChange,
        onKeywordChange,
        refetch,
    };
};
