# Account Wallets API Interaction Documentation

This module provides a function for fetching the wallet balances associated with specific accounts in the Redde system.

## Table of Contents

- [fetchWallets](#fetchwallets)

---

### `fetchWallets`

Fetches the wallets for a specific account by sending a GET request to the API. It supports optional parameters for filtering the results.

#### Parameters
- `accountId: IAccountIdParams`  
  The ID of the account whose wallets are to be fetched.

- `params: any`  
  (Optional) Additional parameters for filtering the wallets (e.g., date range, wallet type).

#### Returns
- `Promise<any>`  
  A promise that resolves to the API response data containing the wallets.

#### Example Usage
```typescript
// Define the account ID and optional parameters
const accountId = { id: "12345" };
const params = { currency: "USD" };

// Fetch the wallets for the given account
const wallets = await fetchWallets(accountId, params);
console.log(wallets);  // Logs the wallet data for the specified account
```

---

### Notes
- The `accountId` parameter must be structured according to the `IAccountIdParams` interface, typically containing a unique identifier for the account.
- The `params` parameter allows for additional filters to be applied to the request, making it flexible for different use cases (e.g., filtering by wallet type or currency).