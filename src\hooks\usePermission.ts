import { PermissionService } from "@/lib/PermissionService";

export const usePermission = () => {
    return {
        hasPermission: (
            authItemId: string,
            action:
                | "canview"
                | "canadd"
                | "canedit"
                | "candelete"
                | "canprint"
                | "canviewlog"
                | "canapprove"
        ) => {
            return PermissionService.hasPermission(authItemId, action);
        },
    };
};
