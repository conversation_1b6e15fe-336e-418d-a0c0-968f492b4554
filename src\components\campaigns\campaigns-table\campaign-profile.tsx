import React, { Suspense } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { EyeIcon, CopyIcon, CheckIcon, XIcon } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { isNil } from "es-toolkit";
import {
    CampaignChargesProps,
    ChargeProfile,
    CampaignProfileProps,
    ChargeProfileDetails,
} from "./campaign-charges-table";
import { Add } from "iconsax-react";

interface CampaignTableColumnsProps {
    ViewCampaignChargeDialogLazy: React.ComponentType<{ campaigncharge: CampaignProfileProps }>;
    EditCampaignProfileDialogLazy: React.ComponentType<{ campaign: CampaignProfileProps }>;
}
export const CampaignProfileColumns = ({
    ViewCampaignChargeDialogLazy,
    EditCampaignProfileDialogLazy,
}: CampaignTableColumnsProps): ColumnDef<CampaignProfileProps>[] => [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
        cell: ({ row }) => <div className="w-[50px]">{row.getValue("id")}</div>,
        enableSorting: true,
        enableHiding: false,
    },
    {
        accessorKey: "name",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
        cell: ({ row }) => <div>{row.getValue("name")}</div>,
    },
    {
        accessorKey: "description",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Description" />,
        cell: ({ row }) => <div className="capitalize">{row.getValue("description")}</div>,
    },
    // {
    //     accessorKey: "chargeprofiledetails",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="Brand" />,
    //     cell: ({ row }) => {
    //         const profiles = row.getValue("chargeprofiledetails") as ChargeProfileDetails[];

    //         if (!profiles || profiles.length === 0) return <div>No charge profiles</div>;

    //         return (
    //             <div className="space-y-2">
    //                 {profiles.map((profile, index) => (
    //                     <div key={index} className="">
    //                         <div className="font-medium">{profile.brand}</div>
    //                     </div>
    //                 ))}
    //             </div>
    //         );
    //     },
    // },
    // {
    //     accessorKey: "chargeprofiledetails",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="Merchant Charge" />,
    //     cell: ({ row }) => {
    //         const profiles = row.getValue("chargeprofiledetails") as ChargeProfileDetails[];

    //         if (!profiles || profiles.length === 0) return <div>No charge profiles</div>;

    //         return (
    //             <div className="space-y-2">
    //                 {profiles.map((profile, index) => (
    //                     <div key={index} className="">
    //                         <div className="text-sm text-muted-foreground">
    //                             {profile.merchantcharge}
    //                         </div>
    //                     </div>
    //                 ))}
    //             </div>
    //         );
    //     },
    // },
    // {
    //     accessorKey: "chargeprofiledetails",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="Payer Charge" />,
    //     cell: ({ row }) => {
    //         const profiles = row.getValue("chargeprofiledetails") as ChargeProfileDetails[];

    //         if (!profiles || profiles.length === 0) return <div>No charge profiles</div>;

    //         return (
    //             <div className="space-y-2">
    //                 {profiles.map((profile, index) => (
    //                     <div key={index} className="">
    //                         <div className="text-sm text-muted-foreground">
    //                             {profile.payercharge}
    //                         </div>
    //                     </div>
    //                 ))}
    //             </div>
    //         );
    //     },
    // },
    // {
    //     accessorKey: "chargeprofiledetails",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction Type" />,
    //     cell: ({ row }) => {
    //         const profiles = row.getValue("chargeprofiledetails") as ChargeProfileDetails[];

    //         if (!profiles || profiles.length === 0) return <div>No charge profiles</div>;

    //         return (
    //             <div className="space-y-2">
    //                 {profiles.map((profile, index) => (
    //                     <div key={index} className="">
    //                         <div className="text-sm text-muted-foreground">{profile.transtype}</div>
    //                     </div>
    //                 ))}
    //             </div>
    //         );
    //     },
    // },
    // {
    //     accessorKey: "chargeprofiledetails",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="Charge Type" />,
    //     cell: ({ row }) => {
    //         const profiles = row.getValue("chargeprofiledetails") as ChargeProfileDetails[];

    //         if (!profiles || profiles.length === 0) return <div>No charge profiles</div>;

    //         return (
    //             <div className="space-y-2">
    //                 {profiles.map((profile, index) => (
    //                     <div key={index} className="">
    //                         <div className="text-sm text-muted-foreground">
    //                             {profile.chargetype}
    //                         </div>
    //                     </div>
    //                 ))}
    //             </div>
    //         );
    //     },
    // },

    // {
    //     accessorKey: "startdate",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="Start Date" />,
    //     cell: ({ row }) => {
    //         const date = new Date(row.getValue("startdate"));
    //         return <div>{format(date, "PPP")}</div>;
    //     },
    // },
    // {
    //     accessorKey: "enddate",
    //     header: ({ column }) => <DataTableColumnHeader column={column} title="End Date" />,
    //     cell: ({ row }) => {
    //         const date = new Date(row.getValue("enddate"));
    //         return <div>{format(date, "PPP")}</div>;
    //     },
    // },
    {
        accessorKey: "createddate",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Created Date" />,
        cell: ({ row }) => {
            const date = new Date(row.getValue("createddate"));
            return <div>{format(date, "PPP")}</div>;
        },
    },
    {
        id: "actions",
        enableHiding: false,
        header: "Actions",
        cell: ({ row }) => {
            const campaigncharge = row.original; // Get the full row data
            // console.log("cccc", row.original);
            return (
                <div className="flex items-center gap-2">
                    {/* <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <Button
                                    className="w-full"
                                    variant="default"
                                    color="primary"
                                    onClick={() => {
                                        // Handle view action
                                    }}
                                >
                                    <EyeIcon className="w-5 h-5" />
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>View Charge Profile</TooltipContent>
                        </Tooltip>
                    </TooltipProvider> */}
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <Suspense fallback={<div>Loading...</div>}>
                                    <ViewCampaignChargeDialogLazy campaigncharge={campaigncharge} />
                                </Suspense>
                            </TooltipTrigger>
                            <TooltipContent>View Charge Profile Details</TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <Suspense fallback={<div>Loading...</div>}>
                                    <EditCampaignProfileDialogLazy campaign={campaigncharge} />
                                </Suspense>
                            </TooltipTrigger>
                            <TooltipContent>Edit Charge Profile</TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            );
        },
    },
];

export default CampaignProfileColumns;
