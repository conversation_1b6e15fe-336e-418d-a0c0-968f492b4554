import React, { lazy, Suspense, useCallback, useState } from "react";

import { Input } from "@/components/ui/input";

import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

import { CalendarDateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import { PaginationState, ServerSideDataTable } from "@/components/ui/server-side-data-table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import loadable from "@loadable/component";
import campaignsTableColumns from "./campaigns-columns";
import { debounce } from "lodash";
import CampaignChargesColumns from "./campaign-charges-columns";
import { SortingState } from "@tanstack/react-table";
import ChargeProfileDetailsColumn from "./charge-profile-details-column";
import { DataTable } from "@/components/ui/data-table";

export interface ChargeProfileDetailsProps {
    id: number;
    brandgroup: string;
    brand: string;
    chargemodel: string;
    chargetype: string;
    lowerbound: number;
    merchantcharge: number | null;
    payercharge: number;
    transtype: string;
    upperbound: number | null;
}

type CampaignChargesTableProps = {
    data: ChargeProfileDetailsProps[];
};

export default function ChargeProfileDetailsTable({ data }: CampaignChargesTableProps) {
    const columns = ChargeProfileDetailsColumn();
    // console.log("demo campaigns", columns);
    return (
        <div className="mx-auto py-8">
            <DataTable
                columns={columns}
                data={data || []}
                title="Campaign Charge Profile Details"
            />
        </div>
    );
}
