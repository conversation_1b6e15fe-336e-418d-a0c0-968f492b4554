import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { UsersApiResponse } from "@/lib/interfaces";
import { fetchCampaignCharges, fetchCampaignCommissions } from "@/api/campaigns/appsApi";

export const useFetchCampaignCommissions = (campaignId: number, initialPageSize: number = 10) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });

    const [keyword, setKeyword] = useState<string>("");
    const [sorting, setSorting] = useState([{ id: "createddate", desc: true }]);

    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        queryKey: [
            "campaign-commissions",
            campaignId,
            pagination.pageIndex,
            pagination.pageSize,
            keyword,
            sorting,
        ],
        queryFn: () => fetchCampaignCommissions(campaignId),
        staleTime: 1000,
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const onKeywordChange = (newKeyword: string) => {
        setPagination((prev) => ({ ...prev, pageIndex: 0 }));
        setKeyword(newKeyword);
    };

    const onSortingChange = (newSorting: any) => {
        setSorting(newSorting);
    };

    return {
        data: data?.data,
        pagination,
        keyword,
        sorting,
        isLoading,
        isError,
        error,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
        refetch,
    };
};
