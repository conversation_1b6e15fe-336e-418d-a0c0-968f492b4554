import {
    LoginRequest,
    LoginResponse,
    RegisterRequest,
    User,
    UsersApiResponse,
    Filters,
} from "@/lib/interfaces";
import { api } from "../api";
import { useMutation } from "@tanstack/react-query";
import useDemoToggle from "@/hooks/use-demo";

/**
 * Logs in a user asynchronously.
 *
 * This function sends a POST request to the API with the provided login data.
 * It then returns the response data, which should contain the login response.
 *
 * @param {LoginRequest} data - The login data to be sent in the request.
 * @returns {Promise<LoginResponse>} A promise that resolves to the login response.
 */
export const loginUser = async (data: LoginRequest): Promise<LoginResponse> => {
    try {
        const response = await api.post<LoginResponse>(`/users/adm/slogin`, data);
        return response.data;
    } catch (error) {
        console.error("Failed to login user:", error);
        throw error;
    }
};

/**
 * Registers a new user asynchronously.
 *
 * This function sends a POST request to the API with the provided registration data.
 * It then returns the response data, which should contain the newly created user information.
 *
 * @param {RegisterRequest} data - The registration data to be sent in the request.
 * @returns {Promise<User>} A promise that resolves to the newly created user object.
 */
export const registerUser = async (data: RegisterRequest): Promise<User> => {
    try {
        const response = await api.post<User>(`/users/register`, data);
        return response.data;
    } catch (error) {
        console.error("Failed to register user:", error);
        throw error;
    }
};

/**
 * Fetches all users from the API.
 *
 * This function sends a GET request to the API to fetch all users. It includes pagination and search functionality.
 *
 * @param {number} page - The page number for pagination.
 * @param {number} pageSize - The number of items per page.
 * @param {string} keyword - The search keyword to filter users. Optional.
 * @returns {Promise<any>} A promise that resolves to the API response data.
 */
export const fetchAllUsers = async (
    page: number,
    pageSize: number,
    keyword?: string | number,
    sorting?: { id: string; desc: boolean }[]
) => {
    try {
        const sortQuery = sorting
            ?.map((sort) => `${sort.id},${sort.desc ? "desc" : "asc"}`)
            .join(",");
        const response = await api.get(
            `/users?page=${page}&size=${pageSize}&sort=${sortQuery || "createdtime,desc"}&search=${keyword || ""}`
        );
        return response?.data;
    } catch (error) {}
};

/**
 * Fetches all demo users from the demo API.
 *
 * This function sends a GET request to the demo API to fetch all demo users.
 * It includes pagination and search functionality.
 *
 * @param {number} page - The page number for pagination.
 * @param {number} pageSize - The number of items per page.
 * @param {string} keyword - The search keyword to filter users.
 * @returns {Promise<any>} A promise that resolves to the API response data.
 */
export const fetchAllDemoUsers = async (page: number, pageSize: number, keyword: string) => {
    const response = await api.get(
        `https://demoapi.reddeonline.com/users?page=${page}&size=${pageSize}&sort=desc,&search=${keyword}`
    );
    return response?.data;
};

/**
 * Fetches all sub-users for a given user.
 *
 * @param {number} page - The page number for pagination.
 * @param {number} pageSize - The number of items per page.
 * @param {number} userid - The ID of the user whose sub-users are being fetched.
 * @param {string} keyword - The search keyword to filter sub-users.
 * @returns {Promise<any>} A promise that resolves to the API response data.
 */
export const fetchAllSubUsers = async (
    page: number,
    pageSize: number,
    userid: number,
    keyword: string
) => {
    const response = await api.get(
        `/users/${userid}/subusers?page=${page}&size=${pageSize}&sort=desc,&search=${keyword}`
    );
    return response?.data;
};

/**
 * Fetches a user by their ID.
 *
 * This function sends a GET request to the API to fetch a user by their ID.
 *
 * @param {any} userid - The ID of the user to be fetched.
 * @returns {Promise<any>} A promise that resolves to the API response data.
 */
export const fetchUser = async (userid: any) => {
    try {
        const response = await api.get(`/users/${userid}`);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const createUser = async (data: any) => {
    try {
        const response = await api.post(`/users/register`, data);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const editUser = async (data: any) => {
    try {
        const response = await api.put(`/users/${data.userid}`, data);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

export const fetchUserDocuments = async (
    page: number,
    pageSize: number,
    accountid: number,
    keyword: string
) => {
    const response = await api.get(
        `/accounts/${accountid}/documents?page=${page}&size=${pageSize}&sort=desc,&search=${keyword}`
    );
    return response?.data;
};
