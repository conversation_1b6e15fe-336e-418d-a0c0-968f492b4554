import React, { useState } from "react";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowUpDown, MoreHorizontal, Search } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { format } from "date-fns";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { Navigate, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { CalendarDateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import { ServerSideDataTable } from "@/components/ui/server-side-data-table";

export interface UsersProps {
    id: number;
    userid: number;
    name: string;
    email: string;
    accountid: number;
    isexpired: boolean;
    isactive: boolean;
    mobilenumber: string;
    iscompany: boolean;
    createdtime: string;
}

type UserTableProps = {
    // columns: ColumnDef<any, any>[];
    data: any;
    filterable?: boolean;
    title?: string;
    loading?: boolean;
    pagination: any;
    pageCount: number;
    keyword: string;
    onPaginationChange: (newPagination: any) => void;
    onKeywordChange: (newKeyword: string) => void;
};

export default function SubUsersTable({
    data,
    filterable = true,
    title,
    loading,
    pagination,
    pageCount,
    keyword,
    onPaginationChange,
    onKeywordChange,
}: UserTableProps) {
    const navigate = useNavigate();
    const [searchTerm, setSearchTerm] = useState("");
    const [channel, setChannel] = useState("");
    const [transType, setTransType] = useState("");
    const [startDate, setStartDate] = useState<Date | null>(null);
    const [endDate, setEndDate] = useState<Date | null>(null);
    const [dateRange, setDateRange] = useState<DateRange | undefined>();

    const handleSearch = () => {
        // Implement search logic here
        console.log("Search with:", { searchTerm, channel, transType, startDate, endDate });
    };

    const viewUser = (user: UsersProps) => {
        // console.log("Viewing user:", user);
        navigate("/user-profile", { state: { user } });
    };

    const manageUser = (user: UsersProps) => {
        // console.log("Viewing user:", user);
        navigate("/user-profile", { state: { user } });
    };

    const columns: ColumnDef<UsersProps>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-[2px]"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-[2px]"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "userid",
            header: ({ column }) => <DataTableColumnHeader column={column} title="User ID" />,
            cell: ({ row }) => <div className="w-[50px]">{row.getValue("userid")}</div>,
            enableSorting: false,
            enableHiding: false,
        },

        {
            accessorKey: "name",
            header: "Full Name",
            cell: ({ row }) => <div className="uppercase">{row.getValue("name")}</div>,
        },
        {
            accessorKey: "email",
            header: "Email",
            cell: ({ row }) => <div className="lowercase">{row.getValue("email")}</div>,
        },
        {
            accessorKey: "accountid",
            header: "Account ID",
            cell: ({ row }) => <div className="lowercase">{row.getValue("accountid")}</div>,
        },
        {
            accessorKey: "mobilenumber",
            header: "Phone",
            cell: ({ row }) => <div>{row.getValue("mobilenumber")}</div>,
        },
        {
            accessorKey: "iscompany",
            header: "Type",
            cell: ({ row }) => {
                const company = row.getValue("iscompany");
                return <p className="font-medium">{company ? "Company" : "Client"}</p>;
            },
        },
        {
            accessorKey: "isactive",
            header: "Status",
            cell: ({ row }) => {
                const active = row.getValue("isactive");
                return (
                    <p
                        className={`flex items-center justify-center rounded-full border px-2 py-1 ${
                            active
                                ? "border-green-500 bg-green-100 text-green-500"
                                : "border-red-500 bg-red-100 text-red-500"
                        }`}
                    >
                        {active ? "Active" : "Inactive"}
                    </p>
                );
            },
        },
        {
            accessorKey: "createdtime",
            header: "Created At",
            cell: ({ row }) => {
                const date = new Date(row.getValue("createdtime"));
                return <div>{format(date, "PPP")}</div>;
            },
        },
        {
            id: "actions",
            enableHiding: false,
            header: "Action",
            cell: ({ row }) => {
                const user = row.original;
                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => viewUser(user)}>
                                View User
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => manageUser(user)}>
                                Manage User
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];

    // console.log('from subusers table', data);
    return (
        <div className="mx-auto py-8">
            {filterable && (
                <Card>
                    <CardHeader>Filter</CardHeader>
                    <CardContent>
                        <div className="my-4 grid w-full grid-cols-4 gap-x-4">
                            <Select value={channel} onValueChange={setChannel}>
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Channel" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Channels</SelectItem>
                                    <SelectItem value="mobile">Mobile Money</SelectItem>
                                    <SelectItem value="bank">Bank</SelectItem>
                                    {/* Add more channels as needed */}
                                </SelectContent>
                            </Select>

                            <Select value={transType} onValueChange={setTransType}>
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Transaction Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Types</SelectItem>
                                    <SelectItem value="deposit">Receive</SelectItem>
                                    <SelectItem value="withdrawal">Cashout</SelectItem>
                                    {/* Add more transaction types as needed */}
                                </SelectContent>
                            </Select>
                            <CalendarDateRangePicker
                                className="w-full"
                                date={dateRange}
                                setDate={setDateRange}
                            />
                            <Button onClick={handleSearch} size="sm" className="w-full">
                                <Search className="mr-2 h-4 w-4" /> Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            )}

            <ServerSideDataTable
                columns={columns}
                data={data?.content || []}
                title={title}
                loading={loading}
                pagination={pagination}
                pageCount={pageCount}
                onPaginationChange={onPaginationChange}
            />
        </div>
    );
}
