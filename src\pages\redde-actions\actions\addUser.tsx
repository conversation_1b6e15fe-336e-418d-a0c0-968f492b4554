// AddUser.js
import { Typography } from "@/components/Typography";
import { toast } from "@/components/ui/use-toast";
import React from "react";
import BannerAction from "../banner";
import { useFetchDemoUsers } from "@/hooks/usersHooks/use-fetch-demo-users";
import ServerSideUsersTable from "@/pages/users/users-table/server-side-table";
import { useFetchUsers } from "@/hooks/usersHooks/use-fetch-users";
import { useLocation } from "react-router-dom";

const ManageDemoUsers: React.FC = () => {
    // const { data, isLoading, isError } = useFetchUsers(50);

    const {
        data,
        pagination,
        isError,
        keyword,
        isLoading,
        onPaginationChange,
        onKeywordChange,
        sorting,
        onSortingChange,
    } = useFetchUsers(50);

    const memoizedUserObject = React.useMemo(() => data, [data?.content]);

    const handleSubmit = (data: any) => {
        toast({
            title: "You submitted the following values:",
            description: (
                <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
                    <code className="text-white">{JSON.stringify(data, null, 2)}</code>
                </pre>
            ),
        });
    };
    return (
        <>
            <div className="flex w-full flex-col">
                <div className="my-3 w-full border-b border-gray-300 pb-2">
                    <Typography variant="large" text="Create Campaigns" />
                </div>
                <BannerAction text="Add new campaigns for users here!" />
            </div>

            <div className="my-4 rounded-lg bg-card p-4">
                {/* <UsersTable data={data} filterable={true} title="Dev Users" /> */}

                <ServerSideUsersTable
                    // columns={columns}
                    data={memoizedUserObject}
                    isDemo={false}
                    title="Add new campaign"
                    loading={isLoading}
                    filterable={true}
                    bulk="bulkcampaign"
                    pagination={pagination}
                    pageCount={data?.totalPages || 0}
                    keyword={keyword}
                    onPaginationChange={onPaginationChange}
                    onKeywordChange={onKeywordChange}
                    onSortingChange={onSortingChange}
                    sorting={sorting}
                />
            </div>
        </>
    );
};

export default ManageDemoUsers;
