import React from "react";
import { Download, Upload, ChevronRight, ChevronLeft } from "lucide-react";
import * as XLSX from "xlsx";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";

type ChargeProfileDetail = {
    brand: string;
    brandgroup: string;
    chargemodel: string;
    chargetype: string;
    id?: number;
    lowerbound: number;
    merchantcharge: number;
    payercharge: number;
    transtype: string;
    upperbound: number;
};

type StepType = {
    title: string;
    description: string;
};

export interface FormState {
    campaignId: number;
    chargeprofile: {
        chargeprofiledetails: ChargeProfileDetail[];
        createdby: number;
        name: string;
        description: string;
        isactive: boolean;
    };
    chargeprofileid: number;
    chargeprofiletype: string;
    createdby: number;
}

interface ChargeProfileStepperDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onSubmit: (payload: FormState) => Promise<void> | void;
    user: any;
}

const initialState: FormState = {
    campaignId: 0,
    chargeprofile: {
        chargeprofiledetails: [],
        createdby: 0,
        name: "",
        description: "",
        isactive: true,
    },
    chargeprofileid: 0,
    chargeprofiletype: "",
    createdby: 0,
};

const steps: StepType[] = [
    { title: "Charge Profile Details", description: "Upload charge profile details via Excel" },
    { title: "Basic Information", description: "Enter profile name and description" },
    { title: "Review", description: "Review and confirm your entries" },
];

const ChargeProfileStepperDialog: React.FC<ChargeProfileStepperDialogProps> = ({
    open,
    onOpenChange,
    onSubmit,
    user,
}) => {
    const userId = user?.useraccount?.accountid ?? 0;

    const [state, setState] = React.useState<FormState>(initialState);
    const [activeStep, setActiveStep] = React.useState(0);
    const [canProceed, setCanProceed] = React.useState(false);
    const resetForm = () => {
        setState(initialState);
    };

    const handleDialogClose = (open: boolean) => {
        onOpenChange(open);
        if (!open) {
            resetForm();
        }
    };

    const downloadSampleExcel = () => {
        const sampleData: ChargeProfileDetail[] = [
            {
                brand: "Sample Brand",
                brandgroup: "Sample Group",
                chargemodel: "Fixed",
                chargetype: "Fee",
                lowerbound: 0,
                merchantcharge: 10,
                payercharge: 5,
                transtype: "Payment",
                upperbound: 1000,
            },
        ];

        const ws = XLSX.utils.json_to_sheet(sampleData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "Sample Data");
        XLSX.writeFile(wb, "charge_profile_template.xlsx");
    };

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        const reader = new FileReader();

        reader.onload = (e) => {
            const data = new Uint8Array(e.target?.result as ArrayBuffer);
            const workbook = XLSX.read(data, { type: "array" });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet) as ChargeProfileDetail[];

            setState((prev) => ({
                ...prev,
                chargeprofile: {
                    ...prev.chargeprofile,
                    chargeprofiledetails: jsonData,
                },
            }));
            setCanProceed(true);
        };

        reader.readAsArrayBuffer(file);
    };

    // const handleBasicInfoChange = (
    //     e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    // ) => {
    //     const { name, value, type } = e.target;
    //     setState((prev) => ({
    //         ...prev,
    //         [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    //     }));
    // };

    const handleBasicInfoChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const { name, value, type } = e.target;
        setState((prev) => {
            // Handle nested chargeprofile properties
            if (name === "name" || name === "description" || name === "isactive") {
                return {
                    ...prev,
                    chargeprofile: {
                        ...prev.chargeprofile,
                        [name]:
                            type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
                    },
                };
            }
            // Handle top-level properties
            return {
                ...prev,
                [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
            };
        });
    };

    const handleNext = async () => {
        if (activeStep < steps.length - 1) {
            setActiveStep((prev) => prev + 1);
        } else {
            try {
                // Prepare payload excluding activeStep and canProceed
                const payload = {
                    campaignId: state.campaignId,
                    chargeprofile: {
                        chargeprofiledetails: state.chargeprofile.chargeprofiledetails,
                        createdby: userId,
                        name: state.chargeprofile.name,
                        description: state.chargeprofile.description,
                        isactive: state.chargeprofile.isactive,
                        id: 1,
                    },
                    id: 1,
                    chargeprofileid: state.chargeprofileid,
                    chargeprofiletype: state.chargeprofiletype,
                    createdby: userId,
                };
                // console.log("🚀 ~ handleNext ~ payload:", payload);

                // Call onSubmit prop with the prepared payload
                await onSubmit(payload);

                // Close dialog on successful submission
                onOpenChange(false);
            } catch (error) {
                console.error("Submission error:", error);
                // Optionally handle submission error (e.g., show error message)
            }
        }
    };

    const handleBack = () => {
        if (activeStep > 0) {
            setActiveStep((prev) => prev - 1);
        }
    };

    const renderStepContent = () => {
        switch (activeStep) {
            case 0:
                return (
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <Button
                                variant="outline"
                                onClick={downloadSampleExcel}
                                className="flex items-center gap-2"
                            >
                                <Download size={16} />
                                Download Template
                            </Button>
                            <label className="flex cursor-pointer items-center gap-2 rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">
                                <Upload size={16} />
                                Upload Excel
                                <input
                                    type="file"
                                    accept=".xlsx,.xls"
                                    className="hidden"
                                    onChange={handleFileUpload}
                                />
                            </label>
                        </div>

                        {state.chargeprofile.chargeprofiledetails?.length > 0 && (
                            <div className="mt-4">
                                <h3 className="mb-2 text-lg font-semibold">
                                    Uploaded Data Preview
                                </h3>
                                <div className="max-h-64 overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                {Object.keys(
                                                    state.chargeprofile.chargeprofiledetails[0]
                                                ).map((header) => (
                                                    <th
                                                        key={header}
                                                        className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                                                    >
                                                        {header}
                                                    </th>
                                                ))}
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-200 bg-white">
                                            {state.chargeprofile.chargeprofiledetails.map(
                                                (row, index) => (
                                                    <tr key={index}>
                                                        {Object.values(row).map((value, i) => (
                                                            <td
                                                                key={i}
                                                                className="whitespace-nowrap px-6 py-4 text-sm text-gray-500"
                                                            >
                                                                {value}
                                                            </td>
                                                        ))}
                                                    </tr>
                                                )
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        )}
                    </div>
                );

            case 1:
                return (
                    <div className="space-y-4">
                        <div className="flex w-full justify-between gap-3">
                            <div className="w-full space-y-2">
                                <label className="block text-sm font-medium">Campaign Id</label>
                                <Input
                                    type="text"
                                    name="campaignId"
                                    value={state.campaignId}
                                    onChange={handleBasicInfoChange}
                                    className="w-full"
                                />
                            </div>
                            <div className="w-full space-y-2">
                                <label className="block text-end text-sm font-medium">
                                    Charge Profile Id
                                </label>
                                <Input
                                    type="text"
                                    name="chargeprofileid"
                                    value={state.chargeprofileid}
                                    onChange={handleBasicInfoChange}
                                    className="w-full"
                                />
                            </div>
                        </div>
                        <div className="w-full space-y-2">
                            <label className="block text-sm font-medium">Charge Profile Type</label>
                            <Input
                                type="text"
                                name="chargeprofiletype"
                                value={state.chargeprofiletype}
                                onChange={handleBasicInfoChange}
                                className="w-full"
                            />
                        </div>
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Profile Name</label>
                            <Input
                                type="text"
                                name="name"
                                value={state.chargeprofile.name}
                                onChange={handleBasicInfoChange}
                                className="w-full"
                            />
                        </div>

                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Description</label>
                            <Textarea
                                name="description"
                                value={state.chargeprofile.description}
                                onChange={handleBasicInfoChange}
                                className="w-full"
                                rows={4}
                            />
                        </div>
                        <div className="flex items-center gap-2">
                            <Checkbox
                                name="isactive"
                                checked={state.chargeprofile.isactive}
                                onChange={handleBasicInfoChange}
                                className="rounded"
                            />
                            <label className="text-sm">Is Active</label>
                        </div>
                    </div>
                );

            case 2:
                return (
                    <div className="space-y-6">
                        <div>
                            <h3 className="mb-2 text-lg font-semibold">Profile Details</h3>
                            <div className="overflow-x-auto rounded bg-gray-50 p-4">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b border-gray-200">
                                            <th className="px-4 py-2 text-left font-semibold">
                                                Field
                                            </th>
                                            <th className="px-4 py-2 text-left font-semibold">
                                                Value
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr className="border-b border-gray-200">
                                            <td className="px-4 py-2 font-medium">Name</td>
                                            <td className="px-4 py-2">
                                                {state.chargeprofile.name}
                                            </td>
                                        </tr>
                                        <tr className="border-b border-gray-200">
                                            <td className="px-4 py-2 font-medium">Description</td>
                                            <td className="px-4 py-2">
                                                {state.chargeprofile.description}
                                            </td>
                                        </tr>
                                        <tr className="border-b border-gray-200">
                                            <td className="px-4 py-2 font-medium">Status</td>
                                            <td className="px-4 py-2">
                                                {state.chargeprofile.isactive
                                                    ? "Active"
                                                    : "Inactive"}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td className="px-4 py-2 font-medium">
                                                Number of charge profile details
                                            </td>
                                            <td className="px-4 py-2">
                                                {state.chargeprofile.chargeprofiledetails.length}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div>
                            <h3 className="mb-2 text-lg font-semibold">Charge Profile Details</h3>
                            <div className="max-h-64 overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            {Object.keys(
                                                state.chargeprofile.chargeprofiledetails[0] || {}
                                            ).map((header) => (
                                                <th
                                                    key={header}
                                                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                                                >
                                                    {header}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 bg-white">
                                        {state.chargeprofile.chargeprofiledetails.map(
                                            (row, index) => (
                                                <tr key={index}>
                                                    {Object.values(row).map((value, i) => (
                                                        <td
                                                            key={i}
                                                            className="whitespace-nowrap px-6 py-4 text-sm text-gray-500"
                                                        >
                                                            {value}
                                                        </td>
                                                    ))}
                                                </tr>
                                            )
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                );

            default:
                return null;
        }
    };

    return (
        <Dialog open={open} onOpenChange={handleDialogClose}>
            <DialogContent className="max-h-screen max-w-4xl overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>Create New Charge Profile</DialogTitle>
                </DialogHeader>

                <div className="py-4">
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            {steps.map((step, index) => (
                                <div key={index} className="flex items-center">
                                    <div
                                        className={`flex flex-col items-center ${index === activeStep ? "text-blue-600" : "text-gray-400"}`}
                                    >
                                        <div
                                            className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                                                index === activeStep
                                                    ? "border-blue-600 bg-blue-50"
                                                    : "border-gray-300"
                                            }`}
                                        >
                                            {index + 1}
                                        </div>
                                        <div className="mt-2 text-sm font-medium">{step.title}</div>
                                        {/* <div className="text-xs text-gray-500">
                                            {step.description}
                                        </div> */}
                                    </div>
                                    {index < steps.length - 1 && (
                                        <div className="mx-4 h-0.5 w-32 bg-gray-200" />
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="mb-8">{renderStepContent()}</div>

                    <div className="flex justify-between">
                        <Button
                            variant="outline"
                            onClick={handleBack}
                            disabled={activeStep === 0}
                            className="flex items-center gap-2"
                        >
                            <ChevronLeft size={16} />
                            Back
                        </Button>

                        <Button
                            onClick={handleNext}
                            disabled={activeStep === 0 && !canProceed}
                            className="flex items-center gap-2"
                        >
                            {activeStep === steps.length - 1 ? "Submit" : "Next"}
                            {activeStep !== steps.length - 1 && <ChevronRight size={16} />}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default ChargeProfileStepperDialog;
