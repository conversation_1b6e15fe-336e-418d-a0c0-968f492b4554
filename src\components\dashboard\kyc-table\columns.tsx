import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";

import { Checkbox } from "@/components/ui/checkbox";

import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { formatCurrency, formatDateToLongString } from "@/lib/utils";
import { format } from "date-fns";
import { useFetchDocumentTypes } from "@/hooks/kycHooks/use-fetch-documenttypes";
import TransactionActions from "../transactions-table/transaction-details";
import { find } from "lodash";
import KYCImage from "./kycActions/KYCImage";
import { useFetchKycImage } from "@/hooks/kycHooks/use-fetch-kyc-image";
// import { TransactionActions } from "./transaction-details";

// This type is used to define the shape of our data.
// You can use a Zod schema here if you want.
export type KYCTypes = {
    id: number;
    transId: string;
    brandTransId: string;
    username: string;
    appName: string;
    brandGroup: string;
    amount: any;
};

export interface IKYCProps {
    accountid: number;
    approvedby: number;
    createdby: number;
    createddate: string;
    dateapproved: string;
    documentnumber: string;
    documenttypeid: number;
    id: number;
    isapproved: boolean;
    issueddate: string;
    name: string;
    notes: string;
}

// const data = useFetchDocumentTypes();

// console.log("data =>", data);
export const columns: ColumnDef<IKYCProps>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
        cell: ({ row }) => <div className="w-[50px]">{row.getValue("id")}</div>,
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "accountid",
        header: "Account ID",
        cell: ({ row }) => <div className="lowercase">{row.getValue("accountid")}</div>,
    },

    {
        accessorKey: "documenttypeid",
        header: "Document Type ID",
        cell: ({ row }) => {
            // const { data } = useFetchDocumentTypes();
            // console.log("data =>", data);
            const { data } = useFetchDocumentTypes();
            const docsArray = data?.data;
            // console.log("document types", docsArray);

            const docType = row.getValue("documenttypeid");

            const docName = find(docsArray, { id: docType });

            // console.log("docs name", docName);

            return <span className="font-bold capitalize text-primary">{`${docName?.name}`}</span>;
        },
    },

    {
        accessorKey: "documentnumber",
        header: "Document Number",
        cell: ({ row }) => <div className="lowercase">{row.getValue("documentnumber")}</div>,
    },

    {
        accessorKey: "name",
        header: "Name",

        cell: ({ row }) => <div> {row.getValue("name")}</div>,
    },
    {
        accessorKey: "notes",
        header: "Notes",
        cell: ({ row }) => {
            const note = row.getValue("notes");

            return (
                <div className="uppercase">{note === null ? <p>N/A</p> : <p>{`${note}`}</p>}</div>
            );
        },
    },

    {
        accessorKey: "issueddate",
        header: "Issue Date",
        cell: ({ row }) => {
            const date = new Date(row.getValue("issueddate"));
            return <div>{formatDateToLongString(date)}</div>;
        },
    },
    {
        accessorKey: "isapproved",
        header: "Status",
        cell: ({ row }) => {
            const approved = row.getValue("isapproved");
            return (
                <p className={` ${approved ? "text-green-500" : "text-red-500"}`}>
                    {approved ? "APPROVED" : "UNAPPROVED"}
                </p>
            );
        },
    },
    {
        accessorKey: "createddate",
        header: "Created Date",
        cell: ({ row }) => {
            const date = new Date(row.getValue("createddate"));
            return <div>{formatDateToLongString(date)}</div>;
        },
    },
    {
        id: "actions",
        enableHiding: false,
        header: "Action",
        cell: ({ row }) => {
            // const user = row.original;
            // const id = row.getValue("id");
            const accountId: number = row.getValue("accountid");
            const documentId: number = row.getValue("id");

            const { data, isLoading, isError } = useFetchKycImage(accountId, documentId);
            console.log("kyc image", data?.data[0]?.image);
            return (
                <div className="flex gap-x-2">
                    <KYCImage
                        src={`${isLoading ? "loading..." : data?.data[0]?.image}`}
                        alt="document name"
                        className="rounded-md" // Optional additional classes
                        triggerClassName="bg-primary text-primary-foreground"
                    />
                </div>
            );
        },
    },
];
