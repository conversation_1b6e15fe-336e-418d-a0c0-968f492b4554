import { createBulkCampaigns } from "@/api/campaigns/appsApi";
import { useMutation } from "@tanstack/react-query";

interface BulkUploadParams {
    data: any[];
    onProgress?: (count: number) => void;
}

interface BulkUploadResult {
    successful: number;
    failed: number;
    errors: { record: any; error: any }[];
}

export const useBulkCreateCampaigns = () => {
    return useMutation<BulkUploadResult, Error, BulkUploadParams>({
        mutationFn: async ({ data, onProgress }) => {
            const result = await createBulkCampaigns(data, onProgress);
            return result;
        },
    });
};
