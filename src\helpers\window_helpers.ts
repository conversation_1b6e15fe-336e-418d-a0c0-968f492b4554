export async function minimizeWindow() {
    await window.electronWindow.minimize();
}
export async function maximizeWindow() {
    await window.electronWindow.maximize();
}
export async function closeWindow() {
    await window.electronWindow.close();
}

// export async function goBack() {
//     await window.electronWindow.
// }

// call the go back function
export async function goBack() {
    await window.electron.goBack();
}

export async function goForward() {
    await window.electron.goForward();
}
export async function canGoBack() {
    await window.electron.canGoBack();
}

export async function canGoForward() {
    await window.electron.canGoForward();
}
export async function grabMacAddress(): Promise<string> {
    return new Promise<string>((resolve, reject) => {
        const handleMacAddress = (mac: string) => {
            resolve(mac);
            window.electron.onMacAddress(() => {}); // Clean up listener after resolving
        };

        window.electron.onMacAddress(handleMacAddress);

        // Optionally handle cases where the response is not received
        setTimeout(() => {
            reject(new Error("MAC address request timed out"));
        }, 1000); // Adjust timeout as needed
    });
}
