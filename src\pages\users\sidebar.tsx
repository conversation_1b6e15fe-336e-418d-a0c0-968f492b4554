import React from "react";

interface SidebarProps {
    selected: string;
    onSelect: (section: string) => void;
    links: string[];
}

const Sidebar: React.FC<SidebarProps> = ({ selected, onSelect, links }) => {
    return (
        <div className="flex flex-col gap-6 p-8">
            {links?.map((link) => (
                <a
                    key={link}
                    onClick={() => onSelect(link)}
                    className={`cursor-pointer text-sm font-medium ${
                        selected === link ? "text-red-500" : "text-slate-400"
                    }`}
                >
                    {link}
                </a>
            ))}
        </div>
    );
};

export default Sidebar;
