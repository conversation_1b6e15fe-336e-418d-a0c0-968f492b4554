import React from "react";
import { format } from "date-fns";
import { formatCurrency } from "@/lib/utils";
import {
    ArrowDown,
    ArrowUp,
    ReceiptCent,
    EyeIcon,
    PlusCircleIcon,
    PlusIcon,
    SendHorizonalIcon,
    ServerCog,
} from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import AddCampaignForm from "./new-campain-form";
import { useApproveCampaign } from "@/hooks/campaignHooks/use-approve-campaign";
import { useToast } from "../ui/use-toast";
import {
    ChargeProfile,
    ChargeProfileDetails,
    CampaignChargesProps,
    CampaignProfileProps,
} from "./campaigns-table/campaign-charges-table";
import ChargeProfileDetailsTable from "./campaigns-table/charge-profile-details-table";
import { Tooltip, <PERSON>lt<PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@radix-ui/react-tooltip";

interface ViewCampaignChargeProps {
    campaigncharge: CampaignProfileProps;
}

const ViewCampaignProfile: React.FC<ViewCampaignChargeProps> = ({ campaigncharge }) => {
    const [open, setOpen] = React.useState(false);
    const { toast } = useToast();
    // console.log("campaign charge", campaigncharge?.chargeprofile?.chargeprofiledetails);
    const chargeProfileDetails = campaigncharge?.chargeprofiledetails;

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <EyeIcon className="h-4 w-4 bg-gray-100 text-primary dark:bg-background" />
                </Button>
                {/* <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger>
                            <Button variant="icon">
                                <EyeIcon className="w-4 h-4 bg-gray-100 text-primary dark:bg-background" />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>View Charge Profile</TooltipContent>
                    </Tooltip>
                </TooltipProvider> */}
            </DialogTrigger>
            <DialogContent className="max-w-screen-xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">
                            View Charge Profile Details
                        </DialogTitle>
                        <DialogDescription className="">
                            This table contains all details for this campaign charge{" "}
                        </DialogDescription>
                    </DialogHeader>
                </DialogHeader>
                {/* Add your charge profile details display here */}
                <ChargeProfileDetailsTable data={chargeProfileDetails} />
            </DialogContent>
        </Dialog>
    );
};

export default ViewCampaignProfile;
