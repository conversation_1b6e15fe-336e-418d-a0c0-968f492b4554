import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { format } from "date-fns";
import { UsersProps } from "@/pages/users/users-table/server-side-table";
import PushToLiveComponent from "@/components/PushToLiveComponent";
import loadable from "@loadable/component";

interface UserTableColumnsProps {
    viewUser: (user: UsersProps) => void;
    AddCampaignDialogLazy: React.ComponentType<{ user: UsersProps }>;
}

// const LazyPushToLiveComponent = loadable(() => import("@/components/PushToLiveComponent"), {
//     <Suspense>
//     </Suspense >
// })

const LazyPushToLiveComponent = loadable(() => import("@/components/PushToLiveComponent"), {
    fallback: <p>Loading...</p>,
});
export const demoUsersTableColumns = ({
    viewUser,
    AddCampaignDialogLazy,
}: UserTableColumnsProps): ColumnDef<UsersProps>[] => [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "userid",
        header: ({ column }) => <DataTableColumnHeader column={column} title="User ID" />,
        cell: ({ row }) => <div className="w-[50px]">{row.getValue("userid")}</div>,
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "name",
        // header: "Full Name",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Full Name" />,

        cell: ({ row }) => <div className="uppercase">{row.getValue("name")}</div>,
    },
    {
        accessorKey: "email",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Email" />,

        // header: "Email",
        cell: ({ row }) => <div className="lowercase">{row.getValue("email")}</div>,
    },
    {
        accessorKey: "accountid",
        // header: "Account ID",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,

        cell: ({ row }) => <div className="lowercase">{row.getValue("accountid")}</div>,
    },
    {
        accessorKey: "mobilenumber",
        // header: "Phone",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Phone" />,

        cell: ({ row }) => <div>{row.getValue("mobilenumber")}</div>,
    },
    {
        accessorKey: "iscompany",
        // header: "Type",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Type" />,

        cell: ({ row }) => {
            const company = row.getValue("iscompany");
            return <p className="font-medium">{company ? "Company" : "Client"}</p>;
        },
    },
    {
        accessorKey: "isactive",
        header: "Status",
        cell: ({ row }) => {
            const active = row.getValue("isactive");
            return (
                <p
                    className={`flex items-center justify-center rounded-sm border px-2 py-1 ${
                        active ? "border-green-500 text-green-500" : "border-red-500 text-red-500"
                    }`}
                >
                    {active ? "Active" : "Inactive"}
                </p>
            );
        },
    },
    {
        accessorKey: "createdtime",
        // header: "Created At",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Created At" />,

        cell: ({ row }) => {
            const date = new Date(row.getValue("createdtime"));
            return <div>{format(date, "PPP")}</div>;
        },
    },
    {
        id: "actions",
        enableHiding: false,
        header: "Action",
        cell: ({ row }) => {
            const user = row.original;
            return <LazyPushToLiveComponent user={user} />;
        },
    },
];

export default demoUsersTableColumns;
