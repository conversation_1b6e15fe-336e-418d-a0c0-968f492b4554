import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { UsersApiResponse } from "@/lib/interfaces";
import { fetchConfirmedTransactions } from "@/api/accounts/accountsApi";

export const useFetchConfirmedTransactions = (
    accountId: number,
    initialPageSize: number = 10,
    campaignId?: number,
    fromDate?: string,
    toDate?: string,
    transType?: string,
    search?: string
) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });
    const [keyword, setKeyword] = useState("");
    const [sorting, setSorting] = useState([{ id: "createddate", desc: true }]);

    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        queryKey: [
            "confirmed-transactions",
            pagination.pageIndex,
            pagination.pageSize,
            fromDate,
            toDate,
            transType,
            search,
            sorting,
        ],
        queryFn: () =>
            fetchConfirmedTransactions(
                pagination.pageIndex,
                pagination.pageSize,
                keyword,
                {
                    from: fromDate,
                    to: toDate,
                    transtype: transType,
                    search,
                },
                sorting
            ),
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const onKeywordChange = (newKeyword: string) => {
        setKeyword(newKeyword);
    };

    const onSortingChange = (newSorting: any) => {
        setSorting(newSorting);
    };

    return {
        data: data?.data,
        pagination,
        keyword,
        sorting,
        isLoading,
        isError,
        error,
        onPaginationChange,
        onKeywordChange,
        onSortingChange,
        refetch,
    };
};
