import { api } from "../api";

/**
 * Fetches available document types for KYC verification.
 *
 * This function makes a GET request to retrieve the list of valid document types
 * that can be used for KYC verification.
 *
 * @returns {Promise<any>} A promise that resolves to the list of document types
 * @throws {Error} If the API request fails
 */
export const documentTypes = async () => {
    try {
        const response = await api.get(`/options/documenttypes`);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

/**
 * Retrieves the image associated with a specific KYC document.
 *
 * @param {number} accountid - The ID of the account
 * @param {number} documentid - The ID of the document whose image is to be retrieved
 * @returns {Promise<any>} A promise that resolves to the document image data
 * @throws {Error} If the API request fails
 */
export const kycDocumentImage = async (accountid: number, documentid: number) => {
    try {
        const response = await api.get(`/accounts/${accountid}/documents/${documentid}/image`);
        return response?.data;
    } catch (error) {
        throw error;
    }
};

/**
 * Creates a new KYC document entry with associated document data.
 *
 * This function sends a POST request to create a new KYC document record along with
 * the actual document data (like images or files) for a specific account.
 *
 * @param {number} accountid - The ID of the account to associate the document with
 * @param {any} data - The document data and metadata to be uploaded
 * @returns {Promise<any>} A promise that resolves to the created document record
 * @throws {Error} If the API request fails
 */
export const createKYCWithDoc = async (accountid: number, data: any) => {
    try {
        // const response = await api.post(`/accounts/${accountid}/documents/addwithdoc`, data);
        // return response?.data;
        console.log("kyc data", data);
    } catch (error) {
        throw error;
    }
};
