import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useSimulateCharge } from "@/hooks/campaignHooks/use-simulate-charge";
import { useFetchBrandGroups } from "@/hooks/optionsHooks/use-fetch-brandgroups";
import { useFetchTransactionTypes } from "@/hooks/optionsHooks/use-fetch-transtypes";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "../ui/scroll-area";
import SimulationResultsTable from "./simulation-table";
// import { ScrollArea } from "@/components/ui/scroll-area";

const formSchema = z.object({
    amount: z
        .string()
        .min(1)
        .transform((val) => parseFloat(val)),
    brandgroup: z.string().min(1),
    transtype: z.string().min(1),
});

interface SimulateChargeFormProps {
    accountId: number;
}

function SimulateChargeForm({ accountId }: SimulateChargeFormProps) {
    const [simulationResult, setSimulationResult] = useState<any>(null);
    const { toast } = useToast();
    const { mutate: simulateCharge, isPending: isSimulating } = useSimulateCharge();
    const { data: brandGroups, isLoading: isLoadingBrandGroups } = useFetchBrandGroups();
    const { data: transTypes, isLoading: isLoadingTransTypes } = useFetchTransactionTypes();

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            amount: "",
            brandgroup: "",
            transtype: "",
        },
    });

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        simulateCharge(
            {
                accountId: accountId,
                campaignData: {
                    ...values,
                },
            },
            {
                onSuccess: (data) => {
                    setSimulationResult(data);
                    toast({ title: "Success", description: "Charge simulated successfully" });
                },
                onError: (error: any) => {
                    setSimulationResult(null);
                    toast({
                        title: "Error",
                        description: error.message || "Failed to simulate charge",
                        variant: "destructive",
                    });
                },
            }
        );
    };

    const isLoading = form.formState.isSubmitting || isSimulating;

    return (
        <div className="space-y-6">
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                        control={form.control}
                        name="amount"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel className="dark:text-white">Amount</FormLabel>
                                <FormControl>
                                    <Input
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        {...field}
                                        placeholder="Enter amount"
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <div className="flex w-full flex-col gap-2">
                        <FormField
                            control={form.control}
                            name="brandgroup"
                            render={({ field }) => (
                                <FormItem className="w-full">
                                    <FormLabel>Brand Group</FormLabel>
                                    <Select onValueChange={field.onChange} value={field.value}>
                                        <FormControl>
                                            <SelectTrigger className="w-full">
                                                <SelectValue placeholder="Select brand group" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {isLoadingBrandGroups ? (
                                                <SelectItem value="loading">Loading...</SelectItem>
                                            ) : (
                                                brandGroups?.data?.map((brandgroup, index) => (
                                                    <SelectItem key={index} value={brandgroup}>
                                                        {brandgroup}
                                                    </SelectItem>
                                                ))
                                            )}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="transtype"
                            render={({ field }) => (
                                <FormItem className="w-full">
                                    <FormLabel>Transaction Types</FormLabel>
                                    <Select onValueChange={field.onChange} value={field.value}>
                                        <FormControl>
                                            <SelectTrigger className="w-full">
                                                <SelectValue placeholder="Transaction Types " />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {isLoadingTransTypes ? (
                                                <SelectItem value="loading">Loading...</SelectItem>
                                            ) : (
                                                transTypes?.data?.map(
                                                    (type: string, index: number) => (
                                                        <SelectItem key={index} value={type}>
                                                            {type}
                                                        </SelectItem>
                                                    )
                                                )
                                            )}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    <Button type="submit" size="default" className="w-full" disabled={isLoading}>
                        {isLoading ? (
                            <span className="flex items-center justify-center">
                                <svg
                                    className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <circle
                                        className="opacity-25"
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        stroke="currentColor"
                                        strokeWidth="4"
                                    ></circle>
                                    <path
                                        className="opacity-75"
                                        fill="currentColor"
                                        d="M4 12a8 8 0 018-8v8H4z"
                                    ></path>
                                </svg>
                                Loading...
                            </span>
                        ) : (
                            "Simulate Charge"
                        )}
                    </Button>
                </form>
            </Form>

            {/* {simulationResult && (
                <Card>
                    <CardHeader>
                        <CardTitle className="text-lg font-semibold">Simulation Results</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ScrollArea className="h-[300px] rounded-md border p-4">
                            <div className="space-y-4">
                                {Object.entries(simulationResult).map(([key, value]) => (
                                    <div key={key} className="flex flex-col space-y-1">
                                        <span className="text-sm font-medium capitalize text-muted-foreground">
                                            {key.replace(/([A-Z])/g, " $1").trim()}:
                                        </span>
                                        <span className="text-base">
                                            {typeof value === "object"
                                                ? JSON.stringify(value, null, 2)
                                                : String(value)}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </ScrollArea>
                    </CardContent>
                </Card>
            )} */}
            {simulationResult && <SimulationResultsTable simulationResult={simulationResult} />}
        </div>
    );
}

export default React.memo(SimulateChargeForm);
