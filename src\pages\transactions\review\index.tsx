("use client");
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import React, { useEffect, useState } from "react";
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, Edit, EyeIcon, MoreHorizontal, Trash } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTable } from "@/components/Customtable/data-table";
// import { Edit, Eye, Trash } from "iconsax-react";

// This type is used to define the shape of our data.
export type Payment = {
    id: string;
    account: string;
    status: "pending" | "processing" | "success" | "failed";
    email: string;
    name: string;
    phone: number;
    // handleView(original: Payment): void;
};


export const columns: ColumnDef<Payment>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value: any) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value: any) => row.toggleSelected(!!value)}
                aria-label="Select row"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "status",
        header: "TransID",
    },
    {
        accessorKey: "account",
        header: "BrandTransID",
    },
    {
        accessorKey: "email",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                    className="hover:bg-transparent"
                >
                    Username
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
    },
    {
        accessorKey: "name",
        header: "AppID",
    },
    {
        accessorKey: "phone",
        header: "BrandGroup",
    },
    {
        accessorKey: "phone",
        header: "Channel",
    },
    {
        accessorKey: "phone",
        header: "TransType",
    },
    {
        accessorKey: "phone",
        header: "TransDate",
    },
    {
        id: "actions",
        enableHiding: false,
        header: "Action",
        cell: ({ row }) => {
            const payment = row.original;

            return (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => navigator.clipboard.writeText(payment.id)}>
                            Copy payment ID
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>View customer</DropdownMenuItem>
                        <DropdownMenuItem>View payment details</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );
        },
    },
];

async function getData(): Promise<Payment[]> {
    // Fetch data from your API here.
    return [
        {
            id: "728ed52f",
            account: "Accountant",
            status: "pending",
            email: "<EMAIL>",
            name: "Kanaan",
            phone: ********,
            // handleView: function (original: Payment): void {
            //     throw new Error("Function not implemented.");
            // },
        },
        // ...
    ];
}

function Review() {
    const [data, setData] = useState<Payment[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [modalData, setModalData] = useState<Payment | null>(null);
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

    const handleView = (rowData: Payment) => {
        setModalData(rowData);
        setIsModalOpen(true);
    };

    useEffect(() => {
        async function fetchData() {
            const result = await getData();
            // Add handleView function to each row
            const resultWithViewHandler = result.map((item) => ({
                ...item,
                handleView: handleView,
            }));
            setData(resultWithViewHandler);
            setLoading(false);
        }

        fetchData();
    }, []);

    if (loading) {
        return <div>Loading...</div>;
    }

    return (
        <div className="mx-0 h-screen w-full bg-gray-100 px-20 py-24">
            <div className="container mx-auto rounded-lg border border-gray-200 bg-white py-10 shadow-lg">
                <DataTable columns={columns} data={data} />
            </div>
            {/* <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} data={modalData} /> */}
        </div>
    );
}

export default Review;
