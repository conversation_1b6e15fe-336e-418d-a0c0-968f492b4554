import React, { lazy, Suspense, useCallback, useState } from "react";

import { Input } from "@/components/ui/input";

import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

import { CalendarDateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import { ServerSideDataTable } from "@/components/ui/server-side-data-table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import loadable from "@loadable/component";
import campaignsTableColumns from "./campaigns-columns";
import { debounce } from "lodash";
import ChargeProfileStepperDialog, { FormState } from "./chargeProfile";
import { Add } from "iconsax-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContexts";
import { useChargeCampaign } from "@/hooks/accountsHooks/use-fetch-transactions";
import { toast } from "@/components/ui/use-toast";

export interface CampaignsProps {
    id: number;
    allowcreditapi: boolean;
    apikey: string | null;
    apikeyexpirydate: string | null;
    autopayout: boolean;
    callbackurl: string;
    createby: number;
    createddate: string;
    description: string;
    enddate: string | null;
    ipwhitelist: string | null;
    isactive: boolean;
    isenhanced: boolean;
    legacyusername: string;
    settlementcallbackurl: string | null;
    startdate: string;
    transnotif: boolean;
    transnotiftype: string | null;
    updatedby: number | null;
    updateddate: string | null;
    accountid: number;
    approvedby: number;
    approveddate: string;
    isapproved: boolean;
    enableotp: boolean;
    payoutaccountid: number | null;
    senderid: string;
    itcproductid: string | null;
    itcmerchantid: string | null;
    itcapikey: string | null;
}

export interface SimulateChargeDialogProps {
    amount: number;
    brandgroup: string;
    transtype: string;
}

type CampaignTableProps = {
    // columns: ColumnDef<any, any>[];
    data: any;
    filterable?: boolean;
    title?: string;
    loading?: boolean;
    pagination: any;
    pageCount: number;
    keyword: string;
    sorting: any;
    onSortingChange: (newSorting: any) => void;
    onPaginationChange: (newPagination: any) => void;
    onKeywordChange: (newKeyword: string) => void;
};

// const AddCampaignDialogLazy = loadable(() => import("@/components/campaigns/add-campaign"));
const ApproveCampaignDialogLazy = loadable(() => import("@/components/campaigns/approve-campaign"));
const EditCampaignDialogLazy = loadable(() => import("@/components/campaigns/edit-campaign"));
const SimulateChargeDialogLazy = loadable(
    () => import("@/components/campaigns/simulate-charge-dialog")
);
const AssignChargeLazy = loadable(() => import("@/components/campaigns/assign-charge"));
const AddTransactionDialog = loadable(() => import("@/components/campaigns/add-transaction"));

export default function ServerSideCampaignsTable({
    data,
    filterable = true,
    title,
    loading,
    pagination,
    pageCount,
    keyword,
    sorting,
    onPaginationChange,
    onKeywordChange,
    onSortingChange,
}: CampaignTableProps) {
    const navigate = useNavigate();
    const { user } = useAuth();
    const [open, setOpen] = React.useState(false);
    const [campaignId, setCampaignId] = useState<number | null>(null);
    const chargeCampaign = useChargeCampaign(campaignId ?? 0);

    const [searchTerm, setSearchTerm] = useState("");

    const debouncedSearch = useCallback(
        debounce((value: string) => {
            onKeywordChange(value);
        }, 500),
        [onKeywordChange]
    );

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        debouncedSearch(value);
    };

    const viewCampaign = (campaign: CampaignsProps) => {
        navigate("/campaign-profile", { state: { campaign } });
    };

    const columns = campaignsTableColumns({
        viewCampaign,
        ApproveCampaignDialogLazy,
        EditCampaignDialogLazy,
        SimulateChargeDialogLazy,
        AssignChargeLazy,
        AddTransactionDialog,
    });
    // console.log("demo campaigns", data);
    const handleSubmit = async (payload: FormState) => {
        try {
            // console.log("🚀 ~ handleSubmit ~ payload:", payload);

            const { campaignId } = payload;
            if (!campaignId) {
                throw new Error("Campaign ID is missing");
            }
            setCampaignId(campaignId);
            // const chargeCampaign = useChargeCampaign(campaignId);

            await chargeCampaign.mutateAsync(payload);
            toast({
                title: "Success",
                description: "Charge profile created successfully!",
            });
        } catch (error) {
            // console.error("Failed to create charge profile:", error);
            toast({
                title: "Error",
                description: "Failed to create charge profile. Please try again.",
                variant: "destructive", // Adjust based on your toast component
            });
            throw error;
        }
    };
    return (
        <div className="mx-auto py-8">
            {filterable && (
                <>
                    <Card className="my-4 justify-center">
                        <CardHeader>Filter</CardHeader>
                        <CardContent>
                            <div className="my-4 grid w-full grid-cols-2 items-center gap-x-4">
                                <Input
                                    type="text"
                                    placeholder="Search by ID, sender ID, username, merchant ID..."
                                    value={searchTerm}
                                    onChange={handleSearchChange}
                                    className="w-full"
                                />
                            </div>
                        </CardContent>
                    </Card>
                    <ChargeProfileStepperDialog
                        open={open}
                        onOpenChange={setOpen}
                        onSubmit={handleSubmit}
                        user={user}
                    />
                </>
            )}
            <ServerSideDataTable
                columns={columns}
                data={data || []}
                title={title}
                loading={loading}
                pagination={pagination}
                pageCount={pageCount}
                sorting={sorting}
                onPaginationChange={onPaginationChange}
                onSortingChange={onSortingChange}
                onKeywordChange={onKeywordChange}
            />
        </div>
    );
}
