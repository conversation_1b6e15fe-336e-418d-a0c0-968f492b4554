import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { UsersApiResponse } from "@/lib/interfaces";
import { fetchCampaignCharges } from "@/api/campaigns/appsApi";

export const useFetchCampaignCharges = (
    campaignId: number,
    active: boolean,
    initialPageSize: number = 10
) => {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize,
    });

    const [keyword, setKeyword] = useState<string>("");
    const [sorting, setSorting] = useState([{ id: "createddate", desc: true }]);

    const { data, isLoading, isError, error, refetch } = useQuery<UsersApiResponse, Error>({
        queryKey: [
            "campaign-charges",
            campaignId,
            active,
            pagination.pageIndex,
            pagination.pageSize,
            keyword,
            sorting,
        ],
        queryFn: () => fetchCampaignCharges(campaignId, active),
        staleTime: 1000,
    });

    const onPaginationChange = (newPagination: any) => {
        setPagination(newPagination);
    };

    const onKeywordChange = (newKeyword: string) => {
        setPagination((prev) => ({ ...prev, pageIndex: 0 }));
        setKeyword(newKeyword);
    };

    const onActiveChange = (newActive: boolean) => {
        setPagination((prev) => ({ ...prev, pageIndex: 0 }));
        setActive(newActive);
    };

    const onSortingChange = (newSorting: any) => {
        setSorting(newSorting);
    };

    return {
        data: data?.data,
        pagination,
        active,
        keyword,
        sorting,
        isLoading,
        isError,
        error,
        onPaginationChange,
        onKeywordChange,
        onActiveChange,
        onSortingChange,
        refetch,
    };
};
