import React, { useState } from "react";
import container from "../../assets/images/container.png";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
    BookUser,
    CarTaxiFrontIcon,
    CopyIcon,
    EyeIcon,
    KeyRound,
    KeySquare,
    Mail,
    Phone,
    ShieldCheck,
    ShieldX,
    User2Icon,
    UserCircle,
    Wallet2,
    Wallet2Icon,
} from "lucide-react";
import { PieChartComponent } from "@/pages/users/Chart";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "../ui/tooltip";
import { Button } from "../ui/button";
import { Chart2, User } from "iconsax-react";
// import { PieChartComponent } from "./Chart";

const CampaignBanner = ({ campaign }: any) => {
    const StatusIcon = ({ status }: { status: boolean }) => {
        return status ? <ShieldCheck color="#1FDF64" /> : <ShieldX color="#E8505B" />;
    };
    // console.log("🚀 ~ CampaignBanner ~ campaign:", campaign);
    return (
        <div className="relative h-52 w-full">
            <img src={container} className="w-full object-cover" alt="Background" />
            <div className="absolute bottom-0 left-0 right-0 top-0 flex items-center justify-center">
                <div className="flex w-full items-center gap-52 rounded-lg bg-opacity-50 px-20">
                    <div className="flex items-center gap-4">
                        <div className="flex h-20 w-20 items-center justify-center rounded-full border-4">
                            <Avatar>
                                <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
                                <AvatarFallback>{campaign.id}</AvatarFallback>
                            </Avatar>
                        </div>
                        <div>
                            <h3 className="flex w-full text-lg text-white">
                                {campaign.senderid}({campaign.description})
                            </h3>
                            <div className="mt-2 flex gap-7">
                                <div className="flex flex-col items-center justify-center gap-3">
                                    <p className="text-white">Active</p>
                                    <StatusIcon status={campaign?.isactive} />
                                </div>
                                <div className="flex flex-col items-center justify-center gap-3">
                                    <p className="text-white">Approved</p>
                                    <StatusIcon status={campaign?.isapproved} />
                                </div>
                                <div className="flex flex-col items-center justify-center gap-3">
                                    <p className="text-white">Enhanced</p>
                                    <StatusIcon status={campaign?.isenhanced} />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="mt-6 w-full space-y-4 grid grid-cols-2">
                        <div className="flex items-center gap-x-2 text-white">
                            <>
                                <div className="flex items-center gap-2">
                                    <KeyRound className="h-5 w-5 text-white" />
                                    <p className="text-sm font-bold">ITC API Key</p>
                                </div>
                                <div className="flex gap-x-2">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger className="flex items-center gap-2">
                                                {/* <span className="text-lg">
                                                    {String(campaign.itcapikey).slice(0, 9)}...
                                                </span> */}
                                                <Tooltip>
                                                    <TooltipTrigger>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 w-6 p-0"
                                                            onClick={() => {
                                                                alert(campaign.itcapikey); // You might want to replace this with a modal
                                                            }}
                                                        >
                                                            <EyeIcon className="h-4 w-4 cursor-pointer" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        View full ITC API key
                                                    </TooltipContent>
                                                </Tooltip>
                                                <Tooltip>
                                                    <TooltipTrigger>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 w-6 p-0"
                                                            onClick={() =>
                                                                navigator.clipboard.writeText(
                                                                    String(campaign.itcapikey)
                                                                )
                                                            }
                                                        >
                                                            <CopyIcon className="h-4 w-4 cursor-pointer" />
                                                        </Button>
                                                        <TooltipContent>
                                                            Copy ITC API Key
                                                        </TooltipContent>
                                                    </TooltipTrigger>
                                                </Tooltip>
                                            </TooltipTrigger>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                            </>
                        </div>

                        <div className="flex items-center gap-x-2 text-white">
                            <>
                                <div className="flex items-center gap-2">
                                    <BookUser className="h-5 w-5 text-white" />
                                    <p className="text-sm font-bold">ITC Merchant ID/Transflow</p>
                                </div>
                                <div className="flex gap-x-2">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger className="flex items-center gap-2">
                                                {/* <span className="text-lg">
                                                    {String(campaign.itcapikey).slice(0, 9)}...
                                                </span> */}
                                                <Tooltip>
                                                    <TooltipTrigger>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 w-6 p-0"
                                                            onClick={() => {
                                                                alert(campaign.itcmerchantid); // You might want to replace this with a modal
                                                            }}
                                                        >
                                                            <EyeIcon className="h-4 w-4 cursor-pointer" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        View full ITC Merchant ID
                                                    </TooltipContent>
                                                </Tooltip>
                                                <Tooltip>
                                                    <TooltipTrigger>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 w-6 p-0"
                                                            onClick={() =>
                                                                navigator.clipboard.writeText(
                                                                    String(campaign.itcmerchantid)
                                                                )
                                                            }
                                                        >
                                                            <CopyIcon className="h-4 w-4 cursor-pointer" />
                                                        </Button>
                                                        <TooltipContent>
                                                            Copy ITC Merchant ID{" "}
                                                        </TooltipContent>
                                                    </TooltipTrigger>
                                                </Tooltip>
                                            </TooltipTrigger>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                            </>
                        </div>
                        <div className="flex items-center gap-x-2 text-white">
                            <>
                                <div className="flex items-center gap-2">
                                    <CarTaxiFrontIcon className="h-5 w-5 text-white" />
                                    <p className="text-sm font-bold">ITC Product ID</p>
                                </div>
                                <div className="flex gap-x-2">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger className="flex items-center gap-2">
                                                {/* <span className="text-lg">
                                                    {String(campaign.itcapikey).slice(0, 9)}...
                                                </span> */}
                                                <Tooltip>
                                                    <TooltipTrigger>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 w-6 p-0"
                                                            onClick={() => {
                                                                alert(campaign.itcproductid); // You might want to replace this with a modal
                                                            }}
                                                        >
                                                            <EyeIcon className="h-4 w-4 cursor-pointer" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        View full ITC Product ID
                                                    </TooltipContent>
                                                </Tooltip>
                                                <Tooltip>
                                                    <TooltipTrigger>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 w-6 p-0"
                                                            onClick={() =>
                                                                navigator.clipboard.writeText(
                                                                    String(campaign.itcproductid)
                                                                )
                                                            }
                                                        >
                                                            <CopyIcon className="h-4 w-4 cursor-pointer" />
                                                        </Button>
                                                        <TooltipContent>
                                                            Copy ITC Product ID{" "}
                                                        </TooltipContent>
                                                    </TooltipTrigger>
                                                </Tooltip>
                                            </TooltipTrigger>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                            </>
                        </div>
                        <div className="flex items-center gap-x-2 text-white">
                            <>
                                <div className="flex items-center gap-2">
                                    <UserCircle className="h-5 w-5 text-white" />
                                    <p className="text-sm font-bold">Legacy Username</p>
                                </div>
                                <div className="flex gap-x-2">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger className="flex items-center gap-2">
                                                {/* <span className="text-lg">
                                                    {String(campaign.itcapikey).slice(0, 9)}...
                                                </span> */}
                                                <Tooltip>
                                                    <TooltipTrigger>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 w-6 p-0"
                                                            onClick={() => {
                                                                alert(campaign.legacyusername); // You might want to replace this with a modal
                                                            }}
                                                        >
                                                            <EyeIcon className="h-4 w-4 cursor-pointer" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        View full Legacy Username
                                                    </TooltipContent>
                                                </Tooltip>
                                                <Tooltip>
                                                    <TooltipTrigger>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 w-6 p-0"
                                                            onClick={() =>
                                                                navigator.clipboard.writeText(
                                                                    String(campaign.legacyusername)
                                                                )
                                                            }
                                                        >
                                                            <CopyIcon className="h-4 w-4 cursor-pointer" />
                                                        </Button>
                                                        <TooltipContent>
                                                            Copy Legacy Username{" "}
                                                        </TooltipContent>
                                                    </TooltipTrigger>
                                                </Tooltip>
                                            </TooltipTrigger>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                            </>
                        </div>
                        <div className="flex items-center gap-x-2 text-white">
                            <>
                                <div className="flex items-center gap-2">
                                    <User2Icon className="h-5 w-5 text-white" />
                                    <p className="text-sm font-bold">Account ID</p>
                                </div>
                                <div className="flex gap-x-2">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger className="flex items-center gap-2">
                                                {/* <span className="text-lg">
                                                    {String(campaign.itcapikey).slice(0, 9)}...
                                                </span> */}
                                                <Tooltip>
                                                    <TooltipTrigger>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 w-6 p-0"
                                                            onClick={() => {
                                                                alert(campaign.accountid); // You might want to replace this with a modal
                                                            }}
                                                        >
                                                            <EyeIcon className="h-4 w-4 cursor-pointer" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        View full Account ID
                                                    </TooltipContent>
                                                </Tooltip>
                                                <Tooltip>
                                                    <TooltipTrigger>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 w-6 p-0"
                                                            onClick={() =>
                                                                navigator.clipboard.writeText(
                                                                    String(campaign.accountid)
                                                                )
                                                            }
                                                        >
                                                            <CopyIcon className="h-4 w-4 cursor-pointer" />
                                                        </Button>
                                                        <TooltipContent>
                                                            Copy Account ID{" "}
                                                        </TooltipContent>
                                                    </TooltipTrigger>
                                                </Tooltip>
                                            </TooltipTrigger>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                            </>
                        </div>

                        {/* <div className="flex items-center gap-x-2 text-white">
                            <div className="flex items-center gap-x-1">
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <User2Icon className="h-5 w-5 text-white" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>Account ID</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                                <h1 className="text-md font-bold">Account ID:</h1>
                            </div>

                            <span className="text-md">{campaign.accountid}</span>
                        </div> */}

                        <div className="flex items-center gap-x-2 text-white">
                            <>
                                {campaign.apikey !== null ? (
                                    <>
                                        <div className="flex items-center gap-2">
                                            <KeySquare className="h-5 w-5 text-white" />
                                            <p className="text-sm font-bold">Redde API Key</p>
                                        </div>
                                        <div className="flex gap-x-2">
                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger className="flex items-center gap-2">
                                                        {/* <span className="text-lg">
                                                    {String(campaign.itcapikey).slice(0, 9)}...
                                                </span> */}
                                                        <Tooltip>
                                                            <TooltipTrigger>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    className="h-6 w-6 p-0"
                                                                    onClick={() => {
                                                                        alert(campaign.apikey); // You might want to replace this with a modal
                                                                    }}
                                                                >
                                                                    <EyeIcon className="h-4 w-4 cursor-pointer" />
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                View full REDDE API key
                                                            </TooltipContent>
                                                        </Tooltip>
                                                        <Tooltip>
                                                            <TooltipTrigger>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    className="h-6 w-6 p-0"
                                                                    onClick={() =>
                                                                        navigator.clipboard.writeText(
                                                                            String(campaign.apikey)
                                                                        )
                                                                    }
                                                                >
                                                                    <CopyIcon className="h-4 w-4 cursor-pointer" />
                                                                </Button>
                                                                <TooltipContent>
                                                                    Copy REDDE API key
                                                                </TooltipContent>
                                                            </TooltipTrigger>
                                                        </Tooltip>
                                                    </TooltipTrigger>
                                                </Tooltip>
                                            </TooltipProvider>
                                        </div>
                                    </>
                                ) : (
                                    <>
                                        <div className="flex items-center gap-2">
                                            <KeySquare className="h-5 w-5 text-white" />
                                            <p className="text-sm font-bold">Redde API Key:</p>
                                        </div>
                                        <span className="text-base font-medium text-white">{`${campaign.apikey}`}</span>
                                    </>
                                )}
                            </>
                        </div>
                    </div>
                    {/* <div className="w-full">
                        <PieChartComponent apps={campaign.accountid} isCharge={true} />
                    </div> */}
                </div>
            </div>
        </div>
    );
};
export default CampaignBanner;
