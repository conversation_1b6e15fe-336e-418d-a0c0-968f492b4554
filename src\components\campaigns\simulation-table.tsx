import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

const SimulationResultsTable = ({ simulationResult }) => {
    if (!simulationResult) return null;

    // Extract details for the charges table
    const chargeDetails = simulationResult[0].details;

    // Define the specific keys we want to display in order
    const displayKeys = [
        "roundedcharge",
        "totalmerchantcharge",
        "totalpayercharge",
        "roundedmerchantcharge",
        "roundedpayercharge",
        "brand"
    ];

    // For debugging - log the simulation result to see what values are available
    console.log("Simulation Result:", simulationResult);

    return (
        <Card>

            <CardContent>
                <ScrollArea className="h-[400px] rounded-md border">
                    <div className="space-y-6 p-4">
                        {/* Charge Details Table */}
                        <div>
                            <h3 className="mb-2 font-medium">Charge Details</h3>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Charge Model</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead className="text-right">Charge</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {chargeDetails.map((detail, index) => (
                                        <TableRow key={index}>
                                            <TableCell className="font-medium">{detail.chargemodel}</TableCell>
                                            <TableCell>{detail.type}</TableCell>
                                            <TableCell className="text-right">{detail.charge}</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Summary Table */}
                        <div>
                            <h3 className="mb-2 font-medium">Charge Summary</h3>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Description</TableHead>
                                        <TableHead className="text-right">Value</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {/* Manual hard-coded rows to ensure display */}
                                    <TableRow>
                                        <TableCell className="font-medium">Rounded Charge</TableCell>
                                        <TableCell className="text-right">
                                            {simulationResult[0].roundedcharge !== undefined ?
                                                simulationResult[0].roundedcharge : "N/A"}
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell className="font-medium">Total Merchant Charge</TableCell>
                                        <TableCell className="text-right">
                                            {simulationResult[0].totalmerchantcharge !== undefined ?
                                                simulationResult[0].totalmerchantcharge : "N/A"}
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell className="font-medium">Total Payer Charge</TableCell>
                                        <TableCell className="text-right">
                                            {simulationResult[0].totalpayercharge !== undefined ?
                                                simulationResult[0].totalpayercharge : "N/A"}
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell className="font-medium">Rounded Merchant Charge</TableCell>
                                        <TableCell className="text-right">
                                            {simulationResult[0].roundedmerchantcharge !== undefined ?
                                                simulationResult[0].roundedmerchantcharge : "N/A"}
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell className="font-medium">Rounded Payer Charge</TableCell>
                                        <TableCell className="text-right">
                                            {simulationResult[0].roundedpayercharge !== undefined ?
                                                simulationResult[0].roundedpayercharge : "N/A"}
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell className="font-medium">Brand</TableCell>
                                        <TableCell className="text-right">
                                            {simulationResult[0].brand || "N/A"}
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                </ScrollArea>
            </CardContent>
        </Card>
    );
};

export default SimulationResultsTable;
