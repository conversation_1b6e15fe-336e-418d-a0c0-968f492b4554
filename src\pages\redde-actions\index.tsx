import React, { LazyExoticComponent, FC, useState, Suspense } from "react";
import Sidebar from "@/pages/users/sidebar";
// import { filter } from "es-toolkit/compat";
import _ from "lodash";

import ServerSideCampaignsTable from "@/components/campaigns/campaigns-table/server-side-table";
import { useFetchCampaigns } from "@/hooks/campaignHooks/use-fetch-campaigns";
import { BulkBankAccounts } from "@/components/ui/bulk-bank-accounts";

const LazyCampaigns: LazyExoticComponent<FC> = React.lazy(() => import("./actions/newCampaign"));
const LazyCampaignsCharges: LazyExoticComponent<FC> = React.lazy(
    () => import("./actions/campaignCharges")
);
const LazyAddTransaction: LazyExoticComponent<FC> = React.lazy(
    () => import("./actions/addTrnsaction")
);
const LazyReverseTransaction: LazyExoticComponent<FC> = React.lazy(
    () => import("./actions/reverseTransaction")
);
const LazySimulateCharge: LazyExoticComponent<FC> = React.lazy(
    () => import("./actions/simulateCharge")
);
const LazyConfirmTransaction: LazyExoticComponent<FC> = React.lazy(
    () => import("./actions/confirmTransaction")
);

const LazyManageDemoUsers: LazyExoticComponent<FC> = React.lazy(() => import("./actions/addUser"));

function ReddeActions() {
    const [selectedSection, setSelectedSection] = useState("Add New Campaign");
    const deferredSelectedSection = React.useDeferredValue(selectedSection);
    const links = [
        "Add New Campaign",
        "All Campaigns",
        // "Approve Campaigns",
        // "Simulate Charge",
        "Confirm Transaction",
        // "Reverse Transaction",
        "Charge Profile",
        "Bulk Bank Accounts",
    ];

    return (
        <div
            className="flex h-screen w-full"
            style={{
                scrollbarWidth: "none",
                msOverflowStyle: "none",
            }}
        >
            <div className="fixed h-full w-[10%] bg-white shadow-2xl dark:bg-card">
                <Sidebar
                    links={links}
                    selected={deferredSelectedSection}
                    onSelect={setSelectedSection}
                />
            </div>
            <div className="ml-[10%] w-[90%] overflow-y-auto px-8 py-4">
                {deferredSelectedSection === "Add New Campaign" && (
                    <Suspense>
                        <LazyManageDemoUsers />
                    </Suspense>
                )}
                {deferredSelectedSection === "All Campaigns" && <LazyCampaigns />}
                {deferredSelectedSection === "Confirm Transaction" && <LazyConfirmTransaction />}
                {/* {deferredSelectedSection === "Reverse Transaction" && <LazyReverseTransaction />} */}
                {deferredSelectedSection === "Charge Profile" && <LazyCampaignsCharges />}
                {deferredSelectedSection === "Bulk Bank Accounts" && <BulkBankAccounts />}
            </div>
        </div>
    );
}

export default ReddeActions;
