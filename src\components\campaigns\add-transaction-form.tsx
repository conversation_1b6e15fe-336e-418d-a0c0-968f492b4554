import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useFetchTransactionTypes } from "@/hooks/optionsHooks/use-fetch-transtypes";
import { Textarea } from "../ui/textarea";
import { useAddTransaction } from "@/hooks/transactionHooks/use-add-transaction";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "../ui/dialog";
import { HandCoins } from "lucide-react";

const formSchema = z.object({
    campaignid: z.number(),
    amount: z
        .union([z.string().transform((x) => x.replace(/[^0-9.-]+/g, "")), z.number()])
        .pipe(z.coerce.number().min(0.0001).max(999999999)),
    reason: z
        .string({
            required_error: "This field is required.",
        })
        .min(1),
    transtype: z
        .string({
            required_error: "This field is required!",
        })
        .min(1),
});

interface TransactionProps {
    campaignId: number;
}

function AddTransactionForm({ campaignId }: TransactionProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const { toast } = useToast();
    const { data: transTypes, isLoading: isLoadingTransTypes } = useFetchTransactionTypes();

    const createTransaction = useAddTransaction();
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            campaignid: campaignId,
            amount: undefined,
            reason: "",
            transtype: "",
        },
    });

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            const result = await createTransaction.mutateAsync(values);

            toast({
                title: "Success",
                description: "Transaction  created successfully",
            });

            setIsModalOpen(false);
        } catch (error: any) {
            const errorStatus = error?.response?.status;
            const errorMessage =
                error?.response?.data?.message || error.message || "Failed to create transaction";

            toast({
                title: `Error (${errorStatus || "unknown"})`,
                description: errorMessage,
                variant: "destructive",
            });

            setIsModalOpen(false);
        }
    };

    const isLoading = form.formState.isSubmitting;

    return (
        <div>
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogTrigger asChild>
                    <Button variant="icon">
                        <HandCoins className="h-4 w-4 text-blue-500" />
                    </Button>
                </DialogTrigger>
                <DialogContent className="max-w-xl !rounded-sm">
                    <DialogHeader>
                        <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                            <DialogTitle className="mb-2 text-xl font-bold">
                                Add Transaction
                            </DialogTitle>
                            <DialogDescription>
                                {" "}
                                Add a transaction to this campaign
                            </DialogDescription>
                        </DialogHeader>
                    </DialogHeader>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <FormField
                                control={form.control}
                                name="amount"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Amount</FormLabel>
                                        <FormControl>
                                            <Input
                                                type="text"
                                                {...field}
                                                placeholder="Enter amount"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="transtype"
                                render={({ field }) => (
                                    <FormItem className="w-full">
                                        <FormLabel>Transaction Types</FormLabel>
                                        <Select onValueChange={field.onChange} value={field.value}>
                                            <FormControl>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Transaction Types " />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {isLoadingTransTypes ? (
                                                    <SelectItem value="loading">
                                                        Loading...
                                                    </SelectItem>
                                                ) : (
                                                    transTypes?.data?.map(
                                                        (type: string, index: number) => (
                                                            <SelectItem key={index} value={type}>
                                                                {type}
                                                            </SelectItem>
                                                        )
                                                    )
                                                )}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="reason"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">Amount</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                {...field}
                                                placeholder="Enter reason for this transaction"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <Button
                                type="submit"
                                size="default"
                                className="w-full"
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <span className="flex items-center justify-center">
                                        <svg
                                            className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v8H4z"
                                            ></path>
                                        </svg>
                                        Loading...
                                    </span>
                                ) : (
                                    "Add Transaction"
                                )}
                            </Button>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>
        </div>
    );
}

export default React.memo(AddTransactionForm);
