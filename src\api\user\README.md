# API Interaction Module Documentation

This module provides utility functions for interacting with a user management API. It includes functions for logging in, fetching users, and performing user-related operations. The module also includes a React hook for integrating these functions with React Query.

## Table of Contents

- [loginUser](#loginuser)
- [useLoginUser](#useloginuser)
- [fetchAllUsers](#fetchallusers)
- [fetchAllDemoUsers](#fetchalldemousers)
- [fetchAllSubUsers](#fetchallsubusers)
- [fetchUser](#fetchuser)

---

### `loginUser`

Logs in a user asynchronously by sending a POST request with the login data.

#### Parameters
- `data: LoginRequest`  
  The login data to be sent to the API.

#### Returns
- `Promise<LoginResponse>`  
  A promise that resolves to the login response data.

#### Example Usage
```typescript
const loginData = {
  username: "exampleUser",
  password: "examplePassword",
};
const response = await loginUser(loginData);
console.log(response);
```

---

### `useLoginUser`

A custom React hook that wraps the `loginUser` function for use with `react-query`. This allows the login process to be managed as a mutation.

#### Functionality
- Uses `useDemoToggle` to determine whether demo mode is active.
- Returns an object containing the mutation function, which can be triggered with login data.

#### Example Usage
```typescript
const { mutate: login, isLoading, error } = useLoginUser();

login({ username: "exampleUser", password: "examplePassword" });
```

---

### `fetchAllUsers`

Fetches all users from the API with optional pagination and search filtering.

#### Parameters
- `page: number`  
  The page number for pagination.
- `pageSize: number`  
  The number of items per page.
- `keyword?: string`  
  (Optional) The search keyword to filter users.

#### Returns
- `Promise<UsersApiResponse>`  
  A promise that resolves to the API response containing the users.

#### Example Usage
```typescript
const users = await fetchAllUsers(1, 10, "john");
console.log(users);
```

---

### `fetchAllDemoUsers`

Fetches demo users from the demo API, supporting pagination and search.

#### Parameters
- `page: number`  
  The page number for pagination.
- `pageSize: number`  
  The number of items per page.
- `keyword: string`  
  The search keyword to filter users.

#### Returns
- `Promise<UsersApiResponse>`  
  A promise that resolves to the API response data containing demo users.

#### Example Usage
```typescript
const demoUsers = await fetchAllDemoUsers(1, 10, "demoUser");
console.log(demoUsers);
```

---

### `fetchAllSubUsers`

Fetches sub-users associated with a specific user, supporting pagination and search.

#### Parameters
- `page: number`  
  The page number for pagination.
- `pageSize: number`  
  The number of items per page.
- `userid: number`  
  The ID of the user whose sub-users are to be fetched.
- `keyword: string`  
  The search keyword to filter sub-users.

#### Returns
- `Promise<any>`  
  A promise that resolves to the API response data containing the sub-users.

#### Example Usage
```typescript
const subUsers = await fetchAllSubUsers(1, 10, 123, "subUser");
console.log(subUsers);
```

---

### `fetchUser`

Fetches a specific user by their user ID.

#### Parameters
- `userid: any`  
  The ID of the user to be fetched.

#### Returns
- `Promise<any>`  
  A promise that resolves to the API response data containing the user details.

#### Example Usage
```typescript
const user = await fetchUser(123);
console.log(user);
```

---

### Notes
- The `api` instance used in these functions is assumed to be an Axios instance pre-configured to handle API requests.
- The `useMutation` hook from `react-query` is used to manage the mutation lifecycle (loading, error, success states) for login operations.