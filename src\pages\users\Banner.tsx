import React, { useState } from "react";
import container from "../../assets/images/container.png";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Mail, Phone, ShieldCheck, ShieldX, User2Icon, Wallet2, Wallet2Icon } from "lucide-react";
import { PieChartComponent } from "./Chart";

const Banner = ({ user, apps }: any) => {
    const StatusIcon = ({ status }: { status: boolean }) => {
        return status ? <ShieldCheck color="#1FDF64" /> : <ShieldX color="#E8505B" />;
    };
    return (
        <div className="relative w-full py-4">
            <img src={container} className="w-full object-cover" alt="Background" />
            <div className="absolute bottom-0 left-0 right-0 top-0 flex items-center justify-center">
                <div className="flex w-full items-center gap-52 rounded-lg bg-opacity-50 px-20">
                    <div className="flex items-center gap-4">
                        <div className="flex h-20 w-20 items-center justify-center rounded-full border-4">
                            <Avatar>
                                <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
                                <AvatarFallback>{user.name}</AvatarFallback>
                            </Avatar>
                        </div>
                        <div>
                            <h3 className="flex w-full text-lg text-white">
                                {user.name} ({user.userid})
                            </h3>
                            <div className="mt-2 flex gap-7">
                                <div className="flex flex-col items-center justify-center gap-3">
                                    <p className="text-white">Active</p>
                                    <StatusIcon status={user?.isactive} />
                                </div>
                                <div className="flex flex-col items-center justify-center gap-3">
                                    <p className="text-white">Company</p>
                                    <StatusIcon status={user?.iscompany} />
                                </div>
                                <div className="flex flex-col items-center justify-center gap-3">
                                    <p className="text-white">Expired</p>
                                    <StatusIcon status={user?.isexpired} />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="mt-6 space-y-4">
                        <p className="flex items-center text-white">
                            <Mail className="h-5 w-5 text-white" />
                            <span className="ml-3 text-lg">{user.email}</span>
                        </p>
                        <p className="flex items-center text-white">
                            <Phone className="h-5 w-5 text-white" />
                            <span className="ml-3 text-lg">{user.mobilenumber}</span>
                        </p>
                        <p className="flex items-center text-white">
                            <User2Icon className="h-5 w-5 text-white" />
                            <span className="ml-3 text-lg">{user.accountid}</span>
                        </p>
                    </div>
                    <div className="w-full">
                        <PieChartComponent apps={apps} />
                    </div>
                </div>
            </div>
        </div>
    );
};
export default Banner;
