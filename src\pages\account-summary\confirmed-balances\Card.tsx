import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";



type CardComponentProps = {
    title: string;
    value: string | number;
    icon: React.ReactNode;
};

export function CardComponent({ title, value, icon }: CardComponentProps) {
    return (
        <div className="flex w-full flex-col">
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div className="flex items-center space-x-2">
                        <div className="rounded-full bg-blue-100 p-2">{icon}</div>
                        <CardTitle className="text-xs font-semibold">{title}</CardTitle>
                    </div>

                </CardHeader>
                <CardContent className="w-full">
                    <div className="text-xl font-bold">{value}</div>
                </CardContent>
            </Card>
        </div>
    );
}
