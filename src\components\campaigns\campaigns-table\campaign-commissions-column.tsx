import React, { Suspense } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table-components/data-table-column-header";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { EyeIcon, CopyIcon, CheckIcon, XIcon } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { isNil } from "es-toolkit";
import { CampaignChargesProps, ChargeProfile } from "./campaign-charges-table";

interface CampaignTableColumnsProps {
    ViewCampaignChargeDialogLazy: React.ComponentType<{ campaigncharge: CampaignChargesProps }>;
}
export const CampaignCommissionsColumns = ({
    ViewCampaignChargeDialogLazy,
}: CampaignTableColumnsProps): ColumnDef<CampaignChargesProps>[] => [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "id",
        header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
        cell: ({ row }) => <div className="w-[50px]">{row.getValue("id")}</div>,
        enableSorting: true,
        enableHiding: false,
    },
    {
        accessorKey: "accountid",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,
        cell: ({ row }) => <div>{row.getValue("accountid")}</div>,
    },
    {
        accessorKey: "custcampaignid",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Customer Campaign ID" />
        ),
        cell: ({ row }) => <div className="">{row.getValue("custcampaignid")}</div>,
    },
    {
        accessorKey: "merchantcampaignid",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Merchant Campaign ID" />
        ),
        cell: ({ row }) => <div className="">{row.getValue("merchantcampaignid")}</div>,
    },
    {
        accessorKey: "description",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Description" />,
        cell: ({ row }) => <div>{row.getValue("description")}</div>,
    },
    {
        accessorKey: "createdby",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Created By" />,
        cell: ({ row }) => <div>{row.getValue("createdby")}</div>,
    },
    {
        accessorKey: "startdate",
        header: ({ column }) => <DataTableColumnHeader column={column} title="Start Date" />,
        cell: ({ row }) => {
            const date = new Date(row.getValue("startdate"));
            return <div>{format(date, "PPP")}</div>;
        },
    },
    {
        accessorKey: "enddate",
        header: ({ column }) => <DataTableColumnHeader column={column} title="End Date" />,
        cell: ({ row }) => {
            const date = new Date(row.getValue("enddate"));
            return <div>{format(date, "PPP")}</div>;
        },
    },

    {
        id: "actions",
        enableHiding: false,
        header: "Actions",
        cell: ({ row }) => {
            const campaigncharge = row.original; // Get the full row data
            // console.log("cccc", row.original);
            return (
                <div className="flex items-center gap-2">
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <Button
                                    className="w-full"
                                    variant="default"
                                    color="primary"
                                    onClick={() => {
                                        // Handle view action
                                    }}
                                >
                                    <EyeIcon className="h-4 w-4" />
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>View Details</TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                    <Suspense fallback={<div>Loading...</div>}>
                        <ViewCampaignChargeDialogLazy campaigncharge={campaigncharge} />
                    </Suspense>
                </div>
            );
        },
    },
];

export default CampaignCommissionsColumns;
