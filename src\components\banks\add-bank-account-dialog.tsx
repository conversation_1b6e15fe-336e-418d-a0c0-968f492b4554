import React from "react";
import AddBankAccountForm from "./BankAccount";
interface BankDetailsProps {
    user?: any; // Replace 'any' with the actual type of your user object
    accountId?: number;
}

const AddBankAccountDialog: React.FC<BankDetailsProps> = ({ user, accountId }) => {
    console.log("account id", accountId);
    return <AddBankAccountForm data={user} accountId={accountId} />;
};

export default AddBankAccountDialog;
