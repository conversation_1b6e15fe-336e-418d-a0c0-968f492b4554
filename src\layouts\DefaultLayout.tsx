// import { SearchComponent } from "@/components/dashboard/Search";
import Sidebar from "@/components/Sidebar";
import React, { LazyExoticComponent, FC } from "react";
// import { Sidebar } from "@/components/Sidebar";

const SearchLazy: LazyExoticComponent<FC> = React.lazy(
    () => import("@/components/dashboard/Search")
);

export default function DefaultLayout({ children }: { children: React.ReactNode }) {
    return (
        <div className="flex h-screen dark:bg-background">
            <div className="h-full w-20">
                <Sidebar />
            </div>
            <main className="relative h-full flex-1 overflow-y-auto overflow-x-hidden">
                <div className="my-16 bg-background">
                    <SearchLazy />
                </div>
                {children}
            </main>
        </div>
    );
}
