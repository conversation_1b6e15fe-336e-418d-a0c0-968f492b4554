import React from "react";

import { Eye, ServerCog } from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

import { useToast } from "../ui/use-toast";
import { CampaignChargesProps } from "./campaigns-table/campaign-charges-table";
import ChargeProfileDetailsTable from "./campaigns-table/charge-profile-details-table";

interface ViewCampaignChargeProps {
    campaigncharge: CampaignChargesProps;
}

const ViewCampaignCharge: React.FC<ViewCampaignChargeProps> = ({ campaigncharge }) => {
    const [open, setOpen] = React.useState(false);
    const { toast } = useToast();
    const chargeProfileDetails = campaigncharge?.chargeprofile?.chargeprofiledetails;

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <Eye className="h-4 w-4 bg-gray-100 text-green-500 dark:bg-background" />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-screen-xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">
                            View Charge Profile Details
                        </DialogTitle>
                        <DialogDescription className="">
                            This table contains all details for this campaign charge{" "}
                        </DialogDescription>
                    </DialogHeader>
                </DialogHeader>
                {/* Add your charge profile details display here */}
                <ChargeProfileDetailsTable data={chargeProfileDetails} />
            </DialogContent>
        </Dialog>
    );
};

export default ViewCampaignCharge;
