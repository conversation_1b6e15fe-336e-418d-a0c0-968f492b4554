import React from "react";
import { PencilLine } from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import EditCampaignForm from "./edit-campaign-form";
import AddSingleCharge from "./add-single-charge";
import { useAuth } from "@/contexts/AuthContexts";

interface TransactionDetailsDialogProps {
    campaign: any; // Replace 'any' with the actual type of your user object
}

const EditCampaignProfileDialog: React.FC<TransactionDetailsDialogProps> = ({ campaign }) => {
    const { user } = useAuth();

    // console.log("user", typeof user);
    return (
        <div className="flex flex-col items-center justify-center">
            <Dialog>
                <DialogTrigger asChild>
                    <Button variant="icon">
                        <PencilLine className="h-4 w-4 text-green-500" />
                    </Button>
                </DialogTrigger>
                <DialogContent className="max-w-fit !rounded-sm">
                    <AddSingleCharge data={campaign} user={user} campaignId={0} />
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default EditCampaignProfileDialog;
